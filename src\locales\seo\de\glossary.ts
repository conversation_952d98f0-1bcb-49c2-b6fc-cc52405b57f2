/**
 * Deutsch - Technisches Glossar
 * Enthält Definitionen technischer Begriffe im Zusammenhang mit Gerätetests
 */

import type { GlossaryTranslation } from '../types';

export const deGlossary: GlossaryTranslation = {
  title: "Technisches Glossar",
  terms: {
    resolution: {
      title: "Auflösung",
      description: "Video-Pixelabmessungen wie 1920x1080, höhere Werte bedeuten bessere Bildqualität"
    },
    frameRate: {
      title: "Bildrate",
      description: "Anzahl der pro Sekunde angezeigten Bildframes, normalerweise in fps ausgedrückt, beeinflusst Video-Glätte"
    },
    latency: {
      title: "Latenz",
      description: "Zeitverzögerung bei der Datenübertragung, gemessen in Millisekunden (ms), niedriger ist besser"
    },
    bandwidth: {
      title: "Bandbreite",
      description: "Netzwerkübertragungskapazität, normalerweise in Mbps gemessen, bestimmt Datenübertragungsgeschwindigkeit"
    },
    sampleRate: {
      title: "Abtastrate",
      description: "Anzahl der pro Sekunde erfassten Audio-Samples, übliche Raten sind 44,1kHz, 48kHz"
    },
    bitRate: {
      title: "Bitrate",
      description: "Audio- oder Video-Datenübertragungsrate, beeinflusst Qualität und Dateigröße"
    },
    dpi: {
      title: "DPI",
      description: "Maus-Empfindlichkeitseinheit, repräsentiert Pixel pro Zoll Bewegung"
    },
    pollingRate: {
      title: "Polling-Rate",
      description: "Frequenz, mit der Gerät Status an Computer meldet, gemessen in Hz, höher bedeutet schnellere Antwort"
    },
    fps: {
      title: "Bildrate (FPS)",
      description: "Anzahl der pro Sekunde angezeigten Bildframes, beeinflusst Video-Glätte und -Qualität"
    },
    megapixel: {
      title: "Megapixel",
      description: "Grundeinheit digitaler Bilder, Megapixel bestimmen Bildschärfe"
    },
    exposure: {
      title: "Belichtung",
      description: "Lichtempfindlichkeitsniveau der Kamera, beeinflusst Bildhelligkeit und -klarheit"
    },
    noiseReduction: {
      title: "Rauschunterdrückung",
      description: "Technologie zur Eliminierung von Rauschen und Hintergrundgeräuschen im Audio"
    },
    sensitivity: {
      title: "Empfindlichkeit",
      description: "Fähigkeit des Mikrofons, Tonsignale zu erfassen"
    },
    frequency: {
      title: "Frequenz",
      description: "Anzahl der Schwingungen von Ton- oder elektrischen Signalen, gemessen in Hz"
    },
    impedance: {
      title: "Impedanz",
      description: "Widerstand des Audio-Geräts gegen elektrischen Strom, beeinflusst Leistungsanpassung"
    },
    soundStage: {
      title: "Klangbühne",
      description: "Räumliches Gefühl und Positionierung im Audio, spiegelt Klangqualitätsschichten wider"
    },
    drivers: {
      title: "Treiber",
      description: "Kernkomponenten in Kopfhörern, die elektrische Signale in Klang umwandeln"
    },
    thd: {
      title: "Gesamte harmonische Verzerrung",
      description: "Messindikator für Audio-Signalverzerrungsniveau"
    },
    keyTravel: {
      title: "Tastenweg",
      description: "Entfernung, die eine Taste von Ruheposition zur Aktivierung bewegt"
    },
    actuationForce: {
      title: "Betätigungskraft",
      description: "Mindestdruck erforderlich zur Aktivierung einer Taste"
    },
    tactile: {
      title: "Taktil",
      description: "Taktile Rückmeldung bei Tastenauslösung"
    },
    linear: {
      title: "Linear",
      description: "Tastendruck proportional zur Wegstrecken-Charakteristik"
    },
    polling: {
      title: "Polling",
      description: "Mechanismus für System zur periodischen Überprüfung des Gerätestatus"
    },
    acceleration: {
      title: "Beschleunigung",
      description: "Maus-Antwortcharakteristik bei schneller Bewegung"
    },
    liftOffDistance: {
      title: "Abhebe-Entfernung",
      description: "Maximale Höhe, auf die Maus angehoben werden kann, während sie noch erfasst"
    },
    tracking: {
      title: "Tracking",
      description: "Fähigkeit des Maussensors, Bewegung zu erfassen"
    },
    jitter: {
      title: "Jitter",
      description: "Grad der Netzwerklatenz-Variation, beeinflusst Stabilität"
    },
    packetLoss: {
      title: "Paketverlust",
      description: "Anteil der während Netzwerkübertragung verlorenen Datenpakete"
    },
    throughput: {
      title: "Durchsatz",
      description: "Tatsächliche Rate der Netzwerkdatenübertragung"
    },
    codec: {
      title: "Codec",
      description: "Algorithmus zur Komprimierung und Dekomprimierung von Audio-/Video-Daten"
    },
    compression: {
      title: "Komprimierung",
      description: "Technologie zur Reduzierung der Datengröße für Bandbreiteneinsparungen"
    },
    inputLag: {
      title: "Eingabe-Lag",
      description: "Zeitunterschied von Eingabeoperation zur Systemantwort"
    }
  }
};
