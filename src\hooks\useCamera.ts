import { useState, useEffect, useRef, useCallback } from "react";
import { useLanguage } from "./useLanguage";

interface VideoDevice {
  deviceId: string;
  label: string;
}

interface UseCameraReturn {
  isActive: boolean;
  devices: VideoDevice[];
  selectedDevice: string;
  error: string | null;
  videoRef: React.RefObject<HTMLVideoElement>;
  startCamera: () => Promise<void>;
  stopCamera: () => void;
  setSelectedDevice: (deviceId: string) => void;
  hasPermission: boolean;
}

export const useCamera = (): UseCameraReturn => {
  const { t } = useLanguage();
  const [isActive, setIsActive] = useState(false);
  const [devices, setDevices] = useState<VideoDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  const getDevices = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices
        .filter(device => device.kind === "videoinput")
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`
        }));

      setDevices(prevDevices => {
        // 只在设备列表发生变化时更新
        if (JSON.stringify(prevDevices) !== JSON.stringify(videoDevices)) {
          return videoDevices;
        }
        return prevDevices;
      });

      // 使用函数式更新来避免依赖 selectedDevice
      setSelectedDevice(prevSelected => {
        if (!prevSelected && videoDevices.length > 0) {
          return videoDevices[0].deviceId;
        }
        return prevSelected;
      });
    } catch (err) {
      setError("Failed to get video devices");
    }
  }, []); // 无依赖，避免循环

  const startCamera = useCallback(async () => {
    try {
      setError(null);

      const constraints = {
        video: {
          deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
          width: { ideal: 640 },
          height: { ideal: 480 },
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;
      setHasPermission(true);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        // 显式调用 play() 方法确保视频开始播放
        try {
          await videoRef.current.play();
        } catch (playError) {
          console.warn("Video play failed:", playError);
          // 即使播放失败，我们仍然认为摄像头已激活
          // 因为流已经成功获取并设置
        }
      }

      setIsActive(true);

      // Get devices after permission is granted
      await getDevices();
    } catch (err) {
      const error = err as Error;
      if (error.name === "NotAllowedError") {
        setError(t("cameraAccessDenied"));
      } else if (error.name === "NotFoundError") {
        setError(t("cameraNotFound"));
      } else {
        setError(t("cameraAccessFailed") + ": " + error.message);
      }
    }
  }, [selectedDevice, getDevices, t]);

  const stopCamera = useCallback(() => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsActive(false);
  }, []);

  useEffect(() => {
    // Check if we already have permission
    navigator.permissions?.query({ name: 'camera' as PermissionName })
      .then(permission => {
        if (permission.state === 'granted') {
          setHasPermission(true);
          getDevices();
        }
      })
      .catch(() => {
        // Permissions API not supported, try to get devices anyway
        getDevices();
      });

    return () => {
      // 清理函数
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
      setIsActive(false);
    };
  }, []); // 移除依赖，只在组件挂载时执行一次

  // Handle device change - 使用 ref 来避免依赖问题
  const selectedDeviceRef = useRef(selectedDevice);
  selectedDeviceRef.current = selectedDevice;

  useEffect(() => {
    if (isActive && selectedDevice) {
      // 停止当前摄像头
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }

      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }

      // 重新启动摄像头
      const restartCamera = async () => {
        try {
          setError(null);

          const constraints = {
            video: {
              deviceId: selectedDeviceRef.current ? { exact: selectedDeviceRef.current } : undefined,
              width: { ideal: 640 },
              height: { ideal: 480 },
            }
          };

          const stream = await navigator.mediaDevices.getUserMedia(constraints);
          mediaStreamRef.current = stream;

          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            try {
              await videoRef.current.play();
            } catch (playError) {
              console.warn("Video play failed:", playError);
            }
          }
        } catch (err) {
          const error = err as Error;
          if (error.name === "NotAllowedError") {
            setError("Camera access denied");
          } else if (error.name === "NotFoundError") {
            setError("Camera not found");
          } else {
            setError("Camera access failed: " + error.message);
          }
        }
      };

      setTimeout(restartCamera, 100);
    }
  }, [selectedDevice, isActive]); // 只依赖必要的状态

  return {
    isActive,
    devices,
    selectedDevice,
    error,
    videoRef,
    startCamera,
    stopCamera,
    setSelectedDevice,
    hasPermission,
  };
};