import React, { useState, useEffect } from 'react';
import { X, Shield, BarChart3, Target, Settings as SettingsIcon, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useLanguage } from '@/hooks/useLanguage';
import { CookieConsent, CookieCategory } from '@/types/privacy';

interface CookieConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (consent: Partial<CookieConsent>) => void;
  currentConsent: CookieConsent | null;
}

export const CookieConsentModal: React.FC<CookieConsentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentConsent
}) => {
  const { t } = useLanguage();
  
  const [consent, setConsent] = useState({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false
  });

  // 初始化同意状态
  useEffect(() => {
    if (currentConsent) {
      setConsent({
        necessary: currentConsent.necessary,
        analytics: currentConsent.analytics,
        marketing: currentConsent.marketing,
        preferences: currentConsent.preferences
      });
    }
  }, [currentConsent]);

  const cookieCategories: CookieCategory[] = [
    {
      id: 'necessary',
      name: t('cookieNecessaryTitle'),
      description: t('cookieNecessaryDesc'),
      required: true,
      enabled: consent.necessary
    },
    {
      id: 'analytics',
      name: t('cookieAnalyticsTitle'),
      description: t('cookieAnalyticsDesc'),
      required: false,
      enabled: consent.analytics
    },
    {
      id: 'marketing',
      name: t('cookieMarketingTitle'),
      description: t('cookieMarketingDesc'),
      required: false,
      enabled: consent.marketing
    },
    {
      id: 'preferences',
      name: t('cookiePreferencesTitle'),
      description: t('cookiePreferencesDesc'),
      required: false,
      enabled: consent.preferences
    }
  ];

  const handleToggle = (categoryId: keyof typeof consent) => {
    if (categoryId === 'necessary') return; // 必要 Cookie 不能禁用
    
    setConsent(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const handleSave = () => {
    onSave(consent);
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true
    };
    setConsent(allAccepted);
    onSave(allAccepted);
  };

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false
    };
    setConsent(onlyNecessary);
    onSave(onlyNecessary);
  };

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'necessary':
        return <Shield className="h-5 w-5 text-green-400" />;
      case 'analytics':
        return <BarChart3 className="h-5 w-5 text-blue-400" />;
      case 'marketing':
        return <Target className="h-5 w-5 text-purple-400" />;
      case 'preferences':
        return <SettingsIcon className="h-5 w-5 text-orange-400" />;
      default:
        return <Shield className="h-5 w-5 text-gray-400" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <div className="relative bg-gray-900/95 backdrop-blur-md border border-white/10 rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-xl font-semibold text-white">
            {t('cookieSettingsTitle')}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-white/60 hover:text-white/80 transition-colors rounded-lg hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 space-y-6 max-h-[60vh] overflow-y-auto">
          <p className="text-white/80 text-sm leading-relaxed">
            {t('cookieSettingsDescription')}
          </p>

          {/* Cookie 类别 */}
          <div className="space-y-4">
            {cookieCategories.map((category) => (
              <div
                key={category.id}
                className="bg-white/5 rounded-xl p-4 border border-white/10"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getCategoryIcon(category.id)}
                    <h3 className="font-medium text-white">{category.name}</h3>
                    {category.required && (
                      <span className="text-xs bg-green-500/20 text-green-300 px-2 py-1 rounded-full">
                        {t('required')}
                      </span>
                    )}
                  </div>
                  <Switch
                    checked={category.enabled}
                    onCheckedChange={() => handleToggle(category.id)}
                    disabled={category.required}
                  />
                </div>
                <p className="text-white/70 text-sm leading-relaxed">
                  {category.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-white/10">
          <Button
            variant="outline"
            onClick={handleRejectAll}
            className="flex-1 bg-transparent border-white/20 text-white hover:bg-white/10"
          >
            {t('rejectAll')}
          </Button>
          <Button
            variant="outline"
            onClick={handleSave}
            className="flex-1 bg-transparent border-white/20 text-white hover:bg-white/10"
          >
            {t('saveSettings')}
          </Button>
          <Button
            onClick={handleAcceptAll}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Check className="h-4 w-4 mr-2" />
            {t('acceptAll')}
          </Button>
        </div>
      </div>
    </div>
  );
};
