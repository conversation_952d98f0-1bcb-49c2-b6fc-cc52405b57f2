import React, { useState, useEffect } from "react";
import { Wifi, WifiOff, Activity, Clock, Upload, Download, Gauge, Award, Globe, Zap, Signal, Router, Trophy, MapPin, Server, Navigation, Ruler } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useNetworkTest } from "@/hooks/useNetworkTest";
import { useLanguage } from "@/hooks/useLanguage";

interface NetworkTestProps {
  onNext?: (result?: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack?: () => void;
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
  showNavigation?: boolean; // 控制是否显示导航按钮，默认为true
}

export const NetworkTest: React.FC<NetworkTestProps> = ({ onNext, onBack, onTestResult, showNavigation = true }) => {
  const { t } = useLanguage();
  const {
    isTestingNetwork,
    networkResult,
    error,
    startNetworkTest,
    testProgress,
    currentPhase,
    realTimeData,
    isLoadingLocation
  } = useNetworkTest();

  // 实时显示的数据状态（现在从 Hook 获取真实数据）
  const [pulseAnimation, setPulseAnimation] = useState(false);
  
  // 使用来自 Hook 的实时数据，测试完成后使用最终结果
  const displaySpeed = !isTestingNetwork && networkResult ? {
    download: networkResult.downloadSpeed,
    upload: networkResult.uploadSpeed
  } : {
    download: realTimeData.downloadSpeed,
    upload: realTimeData.uploadSpeed
  };
  
  const displayLatency = !isTestingNetwork && networkResult ? 
    networkResult.latency : realTimeData.latency;
    
  const displayJitter = !isTestingNetwork && networkResult ? 
    networkResult.jitter : realTimeData.jitter;

  // 动画效果
  useEffect(() => {
    if (isTestingNetwork) {
      const interval = setInterval(() => {
        setPulseAnimation(prev => !prev);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isTestingNetwork]);

  // 自动报告测试结果
  useEffect(() => {
    if (networkResult && !isTestingNetwork && onTestResult) {
      const testPassed = ['excellent', 'good'].includes(networkResult.quality);
      let failureReason;
      
      if (!testPassed) {
        const issues = [];
        if (networkResult.latency > 150) issues.push(`${t("latencyTooHigh") || "延迟过高"}(${networkResult.latency.toFixed(0)}ms)`);
        if (networkResult.downloadSpeed < 1) issues.push(`${t("downloadSpeedTooSlow") || "下载速度过慢"}(${formatSpeed(networkResult.downloadSpeed)})`);
        if (networkResult.uploadSpeed < 0.5) issues.push(`${t("uploadSpeedTooSlow") || "上传速度过慢"}(${formatSpeed(networkResult.uploadSpeed)})`);
        if (networkResult.jitter > 50) issues.push(`${t("jitterTooHigh") || "抖动过高"}(${networkResult.jitter.toFixed(0)}ms)`);
        
        failureReason = issues.length > 0 ? issues.join(", ") : (t("networkQualityPoor") || "网络质量较差");
      }

      onTestResult({
        passed: testPassed,
        failureReason: testPassed ? undefined : failureReason,
        details: {
          quality: networkResult.quality,
          latency: networkResult.latency,
          downloadSpeed: networkResult.downloadSpeed,
          uploadSpeed: networkResult.uploadSpeed,
          jitter: networkResult.jitter,
          aimScores: networkResult.aimScores
        }
      });
    }
  }, [networkResult, isTestingNetwork, onTestResult, t]);

  // 真实数据更新逻辑已移至 useNetworkTest Hook 的 onResultsChange 回调中
  // 这里不再需要模拟数据更新，所有显示的数据都来自 Cloudflare SpeedTest 的真实测量结果

  // 显示数据现在直接从 Hook 的实时数据或最终结果计算得出，无需额外的 useEffect

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'fair': return 'text-yellow-400';
      case 'poor': return 'text-red-400';
      default: return 'text-white/60';
    }
  };

  const getQualityBg = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'bg-green-500/10 border-green-400/30';
      case 'good': return 'bg-blue-500/10 border-blue-400/30';
      case 'fair': return 'bg-yellow-500/10 border-yellow-400/30';
      case 'poor': return 'bg-red-500/10 border-red-400/30';
      default: return 'bg-white/5 border-white/20';
    }
  };

  const getPhaseText = (phase: string) => {
    switch (phase) {
      case 'latency': return t("testingLatency") || "测试延迟";
      case 'download': return t("testingDownload") || "测试下载速度";
      case 'upload': return t("testingUpload") || "测试上传速度";
      case 'packetLoss': return t("testingPacketLoss") || "测试丢包率";
      case 'completed': return t("testCompleted") || "测试完成";
      default: return t("preparingTest") || "准备测试";
    }
  };

  const formatSpeed = (speed: number) => {
    if (speed >= 1000) {
      return `${(speed / 1000).toFixed(2)} Gbps`;
    } else if (speed >= 1) {
      return `${speed.toFixed(1)} Mbps`;
    } else {
      return `${(speed * 1000).toFixed(0)} Kbps`;
    }
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m`;
    } else if (distance < 100) {
      return `${distance.toFixed(1)}km`;
    } else {
      return `${Math.round(distance)}km`;
    }
  };

  const getLocationSource = (source: string) => {
    switch (source) {
      case 'gps': return t("gpsLocation") || "GPS定位";
      case 'ip': return t("ipLocation") || "IP定位";
      case 'timezone': return t("timezoneLocation") || "时区推断";
      default: return t("unknownLocation") || "未知来源";
    }
  };

  // 圆形进度条组件
  const CircularProgress = ({ 
    percentage, 
    size = 120, 
    strokeWidth = 8, 
    color = "#10B981",
    label,
    value,
    unit 
  }: {
    percentage: number;
    size?: number;
    strokeWidth?: number;
    color?: string;
    label: string;
    value: string;
    unit: string;
  }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <div className="relative flex items-center justify-center">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* 背景圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="rgba(255,255,255,0.1)"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* 进度圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-out"
            style={{
              filter: 'drop-shadow(0 0 6px rgba(16, 185, 129, 0.4))'
            }}
          />
        </svg>
        {/* 中心内容 */}
        <div className="absolute flex flex-col items-center justify-center text-center">
          <div className="text-xl font-bold text-white">{value}</div>
          <div className="text-xs text-white/60">{unit}</div>
          <div className="text-xs text-white/40 mt-1">{label}</div>
        </div>
      </div>
    );
  };



  // 波形动画组件
  const WaveAnimation = ({ isActive }: { isActive: boolean }) => {
    return (
      <div className="flex items-center justify-center h-16">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className={`w-1 bg-gradient-to-t from-cyan-500 to-blue-400 mx-1 rounded-full transition-all duration-300 ${
              isActive ? 'animate-pulse' : ''
            }`}
            style={{
              height: isActive ? `${20 + Math.sin((Date.now() / 200) + i) * 15}px` : '4px',
              animationDelay: `${i * 100}ms`
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <GlassCard className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-6">
          {isTestingNetwork ? (
            <div className="relative">
              <Activity className="h-16 w-16 text-blue-400 animate-pulse" />
              <div className="absolute -inset-2 border-2 border-blue-400/30 rounded-full animate-spin" style={{
                borderTopColor: 'rgb(96 165 250)',
                animationDuration: '2s'
              }} />
            </div>
          ) : networkResult ? (
            <div className="relative">
              <Wifi className="h-16 w-16 text-green-400" />
              <Globe className="h-6 w-6 text-white/60 absolute -bottom-1 -right-1" />
            </div>
          ) : (
            <WifiOff className="h-16 w-16 text-white/60" />
          )}
        </div>
        <h2 className="text-3xl font-bold text-white mb-3">
          {t("networkQualityTest") || "网络质量测试"}
        </h2>
        <p className="text-white/80 text-lg max-w-2xl mx-auto">
          {t("networkQualityTestDesc") || "使用 Cloudflare 全球边缘网络测试您的网络连接质量"}
        </p>
        <div className="flex items-center justify-center mt-4 text-sm text-white/60">
          <Globe className="h-4 w-4 mr-2" />
          <span>Powered by Cloudflare SpeedTest</span>
        </div>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-8">
        {/* 实时仪表盘 - 测试进行中 */}
        {isTestingNetwork && (
          <div className="space-y-8">
            {/* 主仪表盘区域 */}
            <div className="bg-gradient-to-br from-slate-900/50 to-slate-800/30 rounded-3xl p-8 border border-white/10">
              {/* 当前阶段指示器 */}
              <div className="text-center mb-8">
                <div className={`inline-flex items-center px-6 py-3 rounded-full bg-white/10 border border-white/20 ${pulseAnimation ? 'animate-pulse' : ''}`}>
                  <div className="w-3 h-3 rounded-full bg-cyan-400 mr-3 animate-ping"></div>
                  <span className="text-lg font-medium text-white">{getPhaseText(currentPhase)}</span>
                  <span className="ml-4 text-cyan-400 font-mono">{testProgress}%</span>
                </div>
                

              </div>

              {/* 三个主要指标的圆形仪表盘 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                {/* 下载速度仪表盘 */}
                <div className="text-center">
                  <CircularProgress
                    percentage={currentPhase === 'download' ? testProgress : (networkResult ? 100 : 0)}
                    size={140}
                    color="#10B981"
                    label={t("download") || "下载"}
                    value={displaySpeed.download.toFixed(1)}
                    unit="Mbps"
                  />
                  <div className={`mt-4 ${currentPhase === 'download' ? 'animate-pulse' : ''}`}>
                    <WaveAnimation isActive={currentPhase === 'download'} />
                  </div>
                </div>

                {/* 延迟仪表盘 */}
                <div className="text-center">
                  <CircularProgress
                    percentage={currentPhase === 'latency' ? testProgress : (networkResult ? 100 : 0)}
                    size={140}
                    color="#F59E0B"
                    label={t("latency") || "延迟"}
                    value={displayLatency.toFixed(0)}
                    unit="ms"
                  />
                  <div className={`mt-4 ${currentPhase === 'latency' ? 'animate-pulse' : ''}`}>
                    <div className="flex justify-center">
                      <div className={`w-4 h-4 rounded-full bg-yellow-400 ${currentPhase === 'latency' ? 'animate-ping' : ''}`}></div>
                    </div>
                  </div>
                </div>

                {/* 上传速度仪表盘 */}
                <div className="text-center">
                  <CircularProgress
                    percentage={currentPhase === 'upload' ? testProgress : (networkResult ? 100 : 0)}
                    size={140}
                    color="#3B82F6"
                    label={t("upload") || "上传"}
                    value={displaySpeed.upload.toFixed(1)}
                    unit="Mbps"
                  />
                  <div className={`mt-4 ${currentPhase === 'upload' ? 'animate-pulse' : ''}`}>
                    <WaveAnimation isActive={currentPhase === 'upload'} />
                  </div>
                </div>
              </div>

              {/* 实时数据流动画 */}
              <div className="relative h-20 bg-black/20 rounded-xl overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  {[...Array(20)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-1 h-1 rounded-full mx-1 transition-all duration-300 ${
                        isTestingNetwork ? 'bg-cyan-400' : 'bg-white/20'
                      }`}
                      style={{
                        animationDelay: `${i * 100}ms`,
                        transform: isTestingNetwork ? `translateY(${Math.sin((Date.now() / 300) + i) * 10}px)` : 'translateY(0)',
                        opacity: isTestingNetwork ? Math.abs(Math.sin((Date.now() / 500) + i)) : 0.3
                      }}
                    />
                  ))}
                </div>
                <div className="absolute bottom-2 left-4 text-xs text-white/50">
                  {isTestingNetwork ? '数据传输中...' : '等待测试'}
                </div>
              </div>

              {/* 阶段进度指示器 */}
              <div className="flex justify-between mt-6 px-4">
                {[
                  { phase: 'latency', icon: Clock, label: '延迟测试', color: '#F59E0B' },
                  { phase: 'download', icon: Download, label: '下载测试', color: '#10B981' },
                  { phase: 'upload', icon: Upload, label: '上传测试', color: '#3B82F6' },
                  { phase: 'completed', icon: Award, label: '测试完成', color: '#8B5CF6' }
                ].map(({ phase, icon: Icon, label, color }, index) => (
                  <div key={phase} className="flex flex-col items-center">
                    <div 
                      className={`p-3 rounded-full border-2 transition-all duration-300 ${
                        currentPhase === phase 
                          ? 'border-white bg-white/10 animate-pulse' 
                          : testProgress > index * 25 
                            ? 'border-white/50 bg-white/5' 
                            : 'border-white/20 bg-transparent'
                      }`}
                      style={{
                        borderColor: currentPhase === phase ? color : undefined
                      }}
                    >
                      <Icon 
                        className={`h-5 w-5 transition-colors duration-300 ${
                          currentPhase === phase ? 'text-white' : 'text-white/60'
                        }`}
                        style={{
                          color: currentPhase === phase ? color : undefined
                        }}
                      />
                    </div>
                    <span className={`text-xs mt-2 transition-colors duration-300 ${
                      currentPhase === phase ? 'text-white font-medium' : 'text-white/60'
                    }`}>
                      {label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 增强的结果仪表盘显示 */}
        {networkResult && (
          <div className="space-y-8">
            {/* 总体质量评估横幅 */}
            <div className={`rounded-3xl p-8 border-2 ${getQualityBg(networkResult.quality)} relative overflow-hidden`}>
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 transform translate-x-full animate-shimmer"></div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-2 flex items-center">
                      <Trophy className="h-7 w-7 mr-3 text-yellow-400" />
                      {t("networkQualityAssessment") || "网络质量评估"}
                    </h3>
                    <p className="text-white/80 flex items-center">
                      <Globe className="h-4 w-4 mr-2" />
                      {networkResult.testRegion}
                    </p>

                    {/* 紧凑的位置信息 - 一行显示 */}
                    <div className="mt-4">
                      {isLoadingLocation ? (
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                          <div className="flex items-center text-sm text-white/60">
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/20 border-t-white/60 mr-2"></div>
                            {t("loadingLocation") || "正在获取位置信息..."}
                          </div>
                        </div>
                      ) : (networkResult.testLocation || networkResult.userLocation || networkResult.distance) && (
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                          <div className="flex items-center flex-wrap gap-4 text-sm">
                            {/* 用户位置 */}
                            {networkResult.userLocation && (
                              <div className="flex items-center text-white/70">
                                <Navigation className="h-3.5 w-3.5 mr-1.5 text-green-400" />
                                <span className="text-white/50 mr-1">{t("userLocation") || "您"}:</span>
                                <span>
                                  {[networkResult.userLocation.city, networkResult.userLocation.country].filter(Boolean).join(', ') ||
                                   networkResult.userLocation.countryCode || '未知位置'}
                                </span>
                                {networkResult.userLocation.source === 'gps' && (
                                  <span className="ml-1 text-green-400 text-xs">GPS</span>
                                )}
                              </div>
                            )}

                            {/* 分隔符 */}
                            {networkResult.userLocation && (networkResult.testLocation || networkResult.distance) && (
                              <div className="text-white/30">•</div>
                            )}

                            {/* 服务器位置 */}
                            {networkResult.testLocation && (
                              <div className="flex items-center text-white/70">
                                <Server className="h-3.5 w-3.5 mr-1.5 text-blue-400" />
                                <span className="text-white/50 mr-1">{t("server") || "服务器"}:</span>
                                <span>
                                  {networkResult.testLocation.city && networkResult.testLocation.country ?
                                    `${networkResult.testLocation.city}, ${networkResult.testLocation.country}` :
                                    networkResult.testLocation.edgeLocation || 'Cloudflare'}
                                </span>
                              </div>
                            )}

                            {/* 分隔符 */}
                            {networkResult.testLocation && networkResult.distance && (
                              <div className="text-white/30">•</div>
                            )}

                            {/* 距离 */}
                            {networkResult.distance && (
                              <div className="flex items-center text-white/70">
                                <Ruler className="h-3.5 w-3.5 mr-1.5 text-yellow-400" />
                                <span className="text-white/50 mr-1">{t("distance") || "距离"}:</span>
                                <span className="text-yellow-400 font-medium">
                                  {formatDistance(networkResult.distance)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="text-center">
                    <div className={`inline-flex items-center px-6 py-3 rounded-full text-xl font-bold ${getQualityColor(networkResult.quality)} bg-white/15 border border-white/30 backdrop-blur-sm`}>
                      <Award className="h-6 w-6 mr-3" />
                      {networkResult.quality === 'excellent' && (t("excellent") || "优秀")}
                      {networkResult.quality === 'good' && (t("good") || "良好")}
                      {networkResult.quality === 'fair' && (t("fair") || "一般")}
                      {networkResult.quality === 'poor' && (t("poor") || "较差")}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 主要指标仪表盘网格 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* 下载速度仪表盘 */}
              <div className="bg-gradient-to-br from-green-900/30 to-green-800/20 rounded-2xl p-6 border border-green-400/30 relative overflow-hidden group hover:scale-105 transition-transform duration-300">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/0 via-green-400/10 to-green-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-center">
                  {/* 统一高度容器 */}
                  <div className="h-25 flex items-center justify-center mb-4">
                    <CircularProgress
                      percentage={Math.min((networkResult.downloadSpeed / 100) * 100, 100)}
                      size={100}
                      color="#10B981"
                      label={t("download") || "下载"}
                      value={formatSpeed(networkResult.downloadSpeed)}
                      unit=""
                    />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {formatSpeed(networkResult.downloadSpeed)}
                    </div>
                    <div className="text-green-400 text-sm font-medium">{t("download") || "下载速度"}</div>
                  </div>
                </div>
              </div>

              {/* 上传速度仪表盘 */}
              <div className="bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-2xl p-6 border border-blue-400/30 relative overflow-hidden group hover:scale-105 transition-transform duration-300">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-400/10 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-center">
                  {/* 统一高度容器 */}
                  <div className="h-25 flex items-center justify-center mb-4">
                    <CircularProgress
                      percentage={Math.min((networkResult.uploadSpeed / 50) * 100, 100)}
                      size={100}
                      color="#3B82F6"
                      label={t("upload") || "上传"}
                      value={formatSpeed(networkResult.uploadSpeed)}
                      unit=""
                    />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {formatSpeed(networkResult.uploadSpeed)}
                    </div>
                    <div className="text-blue-400 text-sm font-medium">{t("upload") || "上传速度"}</div>
                  </div>
                </div>
              </div>

              {/* 延迟仪表盘 */}
              <div className="bg-gradient-to-br from-yellow-900/30 to-yellow-800/20 rounded-2xl p-6 border border-yellow-400/30 relative overflow-hidden group hover:scale-105 transition-transform duration-300">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/0 via-yellow-400/10 to-yellow-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-center">
                  {/* 统一高度容器 */}
                  <div className="h-25 flex items-center justify-center mb-4">
                    <CircularProgress
                      percentage={Math.max(0, 100 - (networkResult.latency / 100) * 100)}
                      size={100}
                      color="#F59E0B"
                      label={t("latency") || "延迟"}
                      value={networkResult.latency.toFixed(0)}
                      unit="ms"
                    />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {networkResult.latency.toFixed(0)}ms
                    </div>
                    <div className="text-yellow-400 text-sm font-medium">{t("latency") || "网络延迟"}</div>
                  </div>
                </div>
              </div>

              {/* 抖动仪表盘 */}
              <div className="bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-2xl p-6 border border-purple-400/30 relative overflow-hidden group hover:scale-105 transition-transform duration-300">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-400/10 to-purple-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 text-center">
                  {/* 统一高度容器 */}
                  <div className="h-25 flex items-center justify-center mb-4">
                    <CircularProgress
                      percentage={Math.max(0, 100 - (networkResult.jitter / 50) * 100)}
                      size={100}
                      color="#8B5CF6"
                      label={t("jitter") || "抖动"}
                      value={networkResult.jitter.toFixed(0)}
                      unit="ms"
                    />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {networkResult.jitter.toFixed(0)}ms
                    </div>
                    <div className="text-purple-400 text-sm font-medium">{t("jitter") || "网络抖动"}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* AIM Scores (if available) */}
            {networkResult.aimScores && (
              <div className="bg-white/5 border border-white/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Gauge className="h-5 w-5 mr-2 text-cyan-400" />
                  {t("aimScores") || "AIM 使用场景评分"}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {networkResult.aimScores.gaming && (
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-xl font-bold text-white">
                        {typeof networkResult.aimScores.gaming === 'object' 
                          ? (networkResult.aimScores.gaming as any).points?.toFixed(0) || (networkResult.aimScores.gaming as any).classificationName || 'N/A'
                          : networkResult.aimScores.gaming}
                      </div>
                      {typeof networkResult.aimScores.gaming === 'object' && (networkResult.aimScores.gaming as any).classificationName && (
                        <div className="text-white/50 text-xs mt-1">{(networkResult.aimScores.gaming as any).classificationName}</div>
                      )}
                      <div className="text-white/70 text-sm">{t("gaming") || "游戏"}</div>
                    </div>
                  )}
                  {networkResult.aimScores.streaming && (
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-xl font-bold text-white">
                        {typeof networkResult.aimScores.streaming === 'object' 
                          ? (networkResult.aimScores.streaming as any).points?.toFixed(0) || (networkResult.aimScores.streaming as any).classificationName || 'N/A'
                          : networkResult.aimScores.streaming}
                      </div>
                      {typeof networkResult.aimScores.streaming === 'object' && (networkResult.aimScores.streaming as any).classificationName && (
                        <div className="text-white/50 text-xs mt-1">{(networkResult.aimScores.streaming as any).classificationName}</div>
                      )}
                      <div className="text-white/70 text-sm">{t("streaming") || "流媒体"}</div>
                    </div>
                  )}
                  {networkResult.aimScores.rtc && (
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-xl font-bold text-white">
                        {typeof networkResult.aimScores.rtc === 'object' 
                          ? (networkResult.aimScores.rtc as any).points?.toFixed(0) || (networkResult.aimScores.rtc as any).classificationName || 'N/A'
                          : networkResult.aimScores.rtc}
                      </div>
                      {typeof networkResult.aimScores.rtc === 'object' && (networkResult.aimScores.rtc as any).classificationName && (
                        <div className="text-white/50 text-xs mt-1">{(networkResult.aimScores.rtc as any).classificationName}</div>
                      )}
                      <div className="text-white/70 text-sm">{t("realTimeCommunication") || "实时通信"}</div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional Metrics (Loaded Latency, etc.) */}
            {(networkResult.loadedLatency || networkResult.loadedJitter) && (
              <div className="bg-white/5 border border-white/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">
                  {t("loadedMetrics") || "负载下的网络指标"}
                </h4>
                <div className="grid grid-cols-2 gap-6">
                  {networkResult.loadedLatency && (
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">{t("loadedLatency") || "负载延迟"}</span>
                      <span className="text-white font-medium">{networkResult.loadedLatency.toFixed(0)}ms</span>
                    </div>
                  )}
                  {networkResult.loadedJitter && (
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">{t("loadedJitter") || "负载抖动"}</span>
                      <span className="text-white font-medium">{networkResult.loadedJitter.toFixed(0)}ms</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Quality Recommendations */}
            <div className="bg-white/5 border border-white/20 rounded-xl p-6">
              <h4 className="text-white/90 font-medium mb-3">{t("recommendations") || "建议"}</h4>
              {networkResult.quality === 'excellent' && (
                <p className="text-green-200/80 text-sm leading-relaxed">
                  {t("networkExcellentDesc") || "您的网络连接质量优秀，适合所有类型的在线活动，包括高清视频通话、在线游戏和大文件传输。"}
                </p>
              )}
              {networkResult.quality === 'good' && (
                <p className="text-blue-200/80 text-sm leading-relaxed">
                  {t("networkGoodDesc") || "您的网络连接质量良好，可以满足大部分日常使用需求，包括视频通话和在线娱乐。"}
                </p>
              )}
              {networkResult.quality === 'fair' && (
                <p className="text-yellow-200/80 text-sm leading-relaxed">
                  {t("networkFairDesc") || "您的网络连接质量一般，建议检查网络设置或联系网络服务提供商以获得更好的体验。"}
                </p>
              )}
              {networkResult.quality === 'poor' && (
                <p className="text-red-200/80 text-sm leading-relaxed">
                  {t("networkPoorDesc") || "您的网络连接质量较差，建议检查网络连接或更换网络环境以确保设备正常工作。"}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Test Button */}
        <div className="flex justify-center">
          {!networkResult && !isTestingNetwork ? (
            <PrimaryButton onClick={startNetworkTest} size="lg" className="px-8 py-4 text-lg">
              <Wifi className="h-6 w-6 mr-3" />
              {t("startNetworkTest") || "开始网络测试"}
            </PrimaryButton>
          ) : !isTestingNetwork && (
            <PrimaryButton onClick={startNetworkTest} variant="secondary" size="lg" className="px-6 py-3">
              <Activity className="h-5 w-5 mr-2" />
              {t("retestNetwork") || "重新测试"}
            </PrimaryButton>
          )}
        </div>

        {/* Network Tips */}
        {!networkResult && !isTestingNetwork && (
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-400/30 rounded-xl p-6">
            <h4 className="text-blue-200 font-medium mb-3 flex items-center">
              <Gauge className="h-5 w-5 mr-2" />
              {t("networkOptimizationTips") || "网络优化建议"}
            </h4>
            <ul className="text-blue-200/80 text-sm space-y-2 leading-relaxed">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                {t("networkTip1") || "确保设备连接到 5GHz WiFi 频段（如果可用）"}
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                {t("networkTip2") || "关闭其他正在使用网络的应用程序"}
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                {t("networkTip3") || "尽量靠近路由器或使用有线连接"}
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                {t("networkTip4") || "测试期间避免下载或上传大文件"}
              </li>
            </ul>
          </div>
        )}
      </div>

      {/* 保留兼容性的导航按钮 - 仅在showNavigation为true且使用旧API时显示 */}
      {showNavigation && !onTestResult && (
        <div className="flex justify-between mt-12 pt-6 border-t border-white/20">
          <PrimaryButton onClick={onBack} variant="outline" className="px-6 py-3">
            {t("back") || "返回"}
          </PrimaryButton>
          <PrimaryButton onClick={() => {
            // 报告网络测试结果
            let testPassed = false;
            let failureReason = "";
            
            if (!networkResult) {
              failureReason = t("networkNotTested") || "未进行网络测试";
            } else {
              testPassed = ['excellent', 'good'].includes(networkResult.quality);
              if (!testPassed) {
                const issues = [];
                if (networkResult.latency > 150) issues.push(`${t("latencyTooHigh") || "延迟过高"}(${networkResult.latency.toFixed(0)}ms)`);
                if (networkResult.downloadSpeed < 1) issues.push(`${t("downloadSpeedTooSlow") || "下载速度过慢"}(${formatSpeed(networkResult.downloadSpeed)})`);
                if (networkResult.uploadSpeed < 0.5) issues.push(`${t("uploadSpeedTooSlow") || "上传速度过慢"}(${formatSpeed(networkResult.uploadSpeed)})`);
                if (networkResult.jitter > 50) issues.push(`${t("jitterTooHigh") || "抖动过高"}(${networkResult.jitter.toFixed(0)}ms)`);
                
                failureReason = issues.length > 0 ? issues.join(", ") : (t("networkQualityPoor") || "网络质量较差");
              }
            }
            
            onNext?.({
              passed: testPassed,
              failureReason: testPassed ? undefined : failureReason,
              details: networkResult ? {
                quality: networkResult.quality,
                latency: networkResult.latency,
                downloadSpeed: networkResult.downloadSpeed,
                uploadSpeed: networkResult.uploadSpeed,
                jitter: networkResult.jitter,
                aimScores: networkResult.aimScores
              } : null
            });
          }} className="px-6 py-3">
            {t("nextMicrophoneTest") || "下一步：麦克风测试"}
          </PrimaryButton>
        </div>
      )}

      {/* Custom CSS for shimmer animation is defined in global CSS */}
    </GlassCard>
  );
};