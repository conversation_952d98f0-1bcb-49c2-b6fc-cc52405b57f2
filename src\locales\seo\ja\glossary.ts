/**
 * 日本語 - 技術用語集
 * デバイステストに関連する技術用語の定義を含む
 */

import type { GlossaryTranslation } from '../types';

export const jaGlossary: GlossaryTranslation = {
  title: "技術用語集",
  terms: {
    resolution: {
      title: "解像度",
      description: "1920x1080などのビデオピクセル寸法、値が高いほど画質が良い"
    },
    frameRate: {
      title: "フレームレート",
      description: "1秒間に表示される画像フレーム数、通常fpsで表現され、ビデオの滑らかさに影響する"
    },
    latency: {
      title: "レイテンシ",
      description: "データ送信の時間遅延、ミリ秒（ms）で測定され、低いほど良い"
    },
    bandwidth: {
      title: "帯域幅",
      description: "ネットワーク送信容量、通常Mbpsで測定され、データ転送速度を決定する"
    },
    sampleRate: {
      title: "サンプルレート",
      description: "1秒間に収集されるオーディオサンプル数、一般的なレートには44.1kHz、48kHzがある"
    },
    bitRate: {
      title: "ビットレート",
      description: "オーディオまたはビデオデータの送信レート、品質とファイルサイズに影響する"
    },
    dpi: {
      title: "DPI",
      description: "マウス感度単位、1インチあたりの移動ピクセル数を表す"
    },
    pollingRate: {
      title: "ポーリングレート",
      description: "デバイスがコンピューターにステータスを報告する頻度、Hzで測定され、高いほど応答が速い"
    },
    fps: {
      title: "フレームレート（FPS）",
      description: "1秒間に表示される画像フレーム数、ビデオの滑らかさと品質に影響する"
    },
    megapixel: {
      title: "メガピクセル",
      description: "デジタル画像の基本単位、メガピクセルが画像の鮮明度を決定する"
    },
    exposure: {
      title: "露出",
      description: "カメラの光感度レベル、画像の明るさと鮮明度に影響する"
    },
    noiseReduction: {
      title: "ノイズリダクション",
      description: "オーディオからノイズと背景音を除去する技術"
    },
    sensitivity: {
      title: "感度",
      description: "マイクが音信号を検出する能力"
    },
    frequency: {
      title: "周波数",
      description: "音または電気信号の振動数、Hzで測定される"
    },
    impedance: {
      title: "インピーダンス",
      description: "オーディオデバイスの電流に対する抵抗、電力マッチングに影響する"
    },
    soundStage: {
      title: "サウンドステージ",
      description: "オーディオの空間感と位置付け、音質の層を反映する"
    },
    drivers: {
      title: "ドライバー",
      description: "ヘッドフォンの中核コンポーネントで、電気信号を音に変換する"
    },
    thd: {
      title: "全高調波歪み",
      description: "オーディオ信号歪みレベルの測定指標"
    },
    keyTravel: {
      title: "キートラベル",
      description: "キーが静止位置から作動まで移動する距離"
    },
    actuationForce: {
      title: "作動力",
      description: "キーを作動させるために必要な最小圧力"
    },
    tactile: {
      title: "タクタイル",
      description: "キーがトリガーされたときの触覚フィードバック"
    },
    linear: {
      title: "リニア",
      description: "キー圧力が移動距離に比例する特性"
    },
    polling: {
      title: "ポーリング",
      description: "システムが定期的にデバイスステータスをチェックするメカニズム"
    },
    acceleration: {
      title: "加速度",
      description: "高速移動時のマウス応答特性"
    },
    liftOffDistance: {
      title: "リフトオフ距離",
      description: "マウスがまだ検出している状態で持ち上げることができる最大高さ"
    },
    tracking: {
      title: "トラッキング",
      description: "マウスセンサーが動きを検出する能力"
    },
    jitter: {
      title: "ジッター",
      description: "ネットワークレイテンシの変動度、安定性に影響する"
    },
    packetLoss: {
      title: "パケット損失",
      description: "ネットワーク送信中に失われるデータパケットの割合"
    },
    throughput: {
      title: "スループット",
      description: "ネットワークデータ送信の実際のレート"
    },
    codec: {
      title: "コーデック",
      description: "オーディオ/ビデオデータを圧縮・展開するアルゴリズム"
    },
    compression: {
      title: "圧縮",
      description: "帯域幅節約のためにデータサイズを削減する技術"
    },
    inputLag: {
      title: "入力ラグ",
      description: "入力操作からシステム応答までの時間差"
    }
  }
};
