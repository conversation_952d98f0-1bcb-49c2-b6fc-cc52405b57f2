import { useState, useRef, useCallback, useEffect } from 'react';
import { useLanguage } from './useLanguage';

export interface AudioConstraints {
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
}

export interface TechnicalDetails {
  sampleRate: number;
  channels: number;
  bitDepth: number;
}

export interface UseMicrophoneProps {
  selectedDeviceId?: string;
  audioConstraints: AudioConstraints;
}

export interface AudioQualityMetrics {
  signalToNoiseRatio: number; // dB
  backgroundNoiseLevel: number; // dB
  clarity: 'excellent' | 'good' | 'fair' | 'poor';
  echoDetected: boolean;
  distortionLevel: number; // 0-1
}

export interface UseMicrophoneReturn {
  permissionState: 'idle' | 'pending' | 'granted' | 'denied';
  devices: MediaDeviceInfo[];
  volume: number;
  technicalDetails: TechnicalDetails | null;
  isRecording: boolean;
  audioUrl: string | null;
  analyserNode: AnalyserNode | null;
  frequencyData: Uint8Array | null;
  audioQuality: AudioQualityMetrics | null;
  startTest: () => Promise<void>;
  stopTest: () => void;
  startRecording: () => void;
  stopRecording: () => void;
  playRecording: () => void;
  error: string | null;
  // 录音计时器和播放进度
  recordingDuration: number;
  isPlaying: boolean;
  playbackProgress: number;
  playbackDuration: number;
  stopPlayback: () => void;
  // 实时监听功能
  isMonitoring: boolean;
  monitorVolume: number;
  startMonitoring: () => void;
  stopMonitoring: () => void;
  setMonitorVolume: (volume: number) => void;
  startMonitoringWithDelay: (delaySeconds: number) => void; // 带音频延迟的监听函数
  updateMonitoringDelay: (delaySeconds: number) => void; // 实时更新音频延迟
  // Legacy props for backward compatibility
  audioLevel: number;
  selectedDevice: string;
  setSelectedDevice: (deviceId: string) => void;
  hasPermission: boolean;
}

export const useMicrophone = ({
  selectedDeviceId,
  audioConstraints
}: UseMicrophoneProps = { audioConstraints: { echoCancellation: true, noiseSuppression: true, autoGainControl: true } }): UseMicrophoneReturn => {
  const { t } = useLanguage();
  const [permissionState, setPermissionState] = useState<'idle' | 'pending' | 'granted' | 'denied'>('idle');
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [volume, setVolume] = useState<number>(0);
  const [technicalDetails, setTechnicalDetails] = useState<TechnicalDetails | null>(null);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [frequencyData, setFrequencyData] = useState<Uint8Array | null>(null);
  const [audioQuality, setAudioQuality] = useState<AudioQualityMetrics | null>(null);
  
  // 录音计时器相关状态
  const [recordingDuration, setRecordingDuration] = useState<number>(0); // 录音时长（秒）
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [playbackProgress, setPlaybackProgress] = useState<number>(0); // 播放进度（0-1）
  const [playbackDuration, setPlaybackDuration] = useState<number>(0); // 录音总时长（秒）
  
  // 实时监听相关状态
  const [isMonitoring, setIsMonitoring] = useState<boolean>(false);
  const [monitorVolume, setMonitorVolume] = useState<number>(0.5); // 默认50%音量

  // Legacy states for backward compatibility
  const [audioLevel, setAudioLevel] = useState(0);
  const [selectedDevice, setSelectedDevice] = useState("");
  const [hasPermission, setHasPermission] = useState(false);

  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserNodeRef = useRef<AnalyserNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const animationFrameRef = useRef<number | null>(null);
  
  // 录音计时器和播放相关引用
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const recordingStartTimeRef = useRef<number>(0);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const playbackTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 实时监听相关引用
  const gainNodeRef = useRef<GainNode | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const delayNodeRef = useRef<DelayNode | null>(null); // 新增延迟节点引用
  
  console.log(
    '[useMicrophone] Hook re-rendering. isMonitoring:', 
    isMonitoring, 
    'permissionState:', 
    permissionState
  );

  // Get available audio devices
  const getDevices = useCallback(async () => {
    try {
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = deviceList.filter(device => device.kind === 'audioinput');
      setDevices(audioInputs);
      
      // Legacy compatibility
      const audioDevices = audioInputs.map(device => ({
        deviceId: device.deviceId,
        label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`
      }));
      
      if (audioDevices.length > 0 && !selectedDevice) {
        setSelectedDevice(audioDevices[0].deviceId);
      }
    } catch (err) {
      console.error('[useMicrophone] Error getting devices:', err);
      setError(t("microphoneAccessFailed"));
    }
  }, [selectedDevice]);

  // Audio quality analysis
  const analyzeAudioQuality = useCallback((frequencyData: Uint8Array, timeData: Uint8Array, rms: number) => {
    // Calculate background noise level (lowest 10% of frequency bins)
    const sortedFreq = [...frequencyData].sort((a, b) => a - b);
    const noiseFloor = sortedFreq.slice(0, Math.floor(sortedFreq.length * 0.1));
    const backgroundNoiseLevel = noiseFloor.reduce((sum, val) => sum + val, 0) / noiseFloor.length;
    const backgroundNoiseDb = backgroundNoiseLevel > 0 ? 20 * Math.log10(backgroundNoiseLevel / 255) : -60;

    // Calculate signal level (highest 10% of frequency bins)
    const signalPeaks = sortedFreq.slice(-Math.floor(sortedFreq.length * 0.1));
    const signalLevel = signalPeaks.reduce((sum, val) => sum + val, 0) / signalPeaks.length;
    const signalDb = signalLevel > 0 ? 20 * Math.log10(signalLevel / 255) : -60;

    // Signal-to-Noise Ratio
    const snr = signalDb - backgroundNoiseDb;

    // Detect distortion (high frequency content relative to fundamental)
    const lowFreq = frequencyData.slice(0, Math.floor(frequencyData.length * 0.3));
    const highFreq = frequencyData.slice(Math.floor(frequencyData.length * 0.7));
    const lowAvg = lowFreq.reduce((sum, val) => sum + val, 0) / lowFreq.length;
    const highAvg = highFreq.reduce((sum, val) => sum + val, 0) / highFreq.length;
    const distortionLevel = highAvg / (lowAvg + 1); // Avoid division by zero

    // Simple echo detection (look for periodic patterns in time domain)
    let echoDetected = false;
    const correlationThreshold = 0.7;
    for (let delay = 50; delay < timeData.length / 2; delay += 10) {
      let correlation = 0;
      for (let i = 0; i < timeData.length - delay; i++) {
        correlation += timeData[i] * timeData[i + delay];
      }
      correlation /= (timeData.length - delay);
      if (correlation > correlationThreshold) {
        echoDetected = true;
        break;
      }
    }

    // Determine overall clarity
    let clarity: AudioQualityMetrics['clarity'] = 'poor';
    if (snr > 20 && distortionLevel < 0.3 && !echoDetected) {
      clarity = 'excellent';
    } else if (snr > 15 && distortionLevel < 0.5) {
      clarity = 'good';
    } else if (snr > 10 && distortionLevel < 0.7) {
      clarity = 'fair';
    }

    const qualityMetrics: AudioQualityMetrics = {
      signalToNoiseRatio: Math.round(snr * 10) / 10,
      backgroundNoiseLevel: Math.round(backgroundNoiseDb * 10) / 10,
      clarity,
      echoDetected,
      distortionLevel: Math.round(distortionLevel * 100) / 100
    };

    setAudioQuality(qualityMetrics);
  }, []);

  // Audio analysis loop
  const analyzeAudio = useCallback(() => {
    if (!analyserNodeRef.current) return;

    const analyser = analyserNodeRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const timeDataArray = new Uint8Array(bufferLength);

    analyser.getByteFrequencyData(dataArray);
    analyser.getByteTimeDomainData(timeDataArray);

    // Calculate volume (RMS) and convert to dBFS
    let sum = 0;
    for (let i = 0; i < timeDataArray.length; i++) {
      const amplitude = (timeDataArray[i] - 128) / 128;
      sum += amplitude * amplitude;
    }
    const rms = Math.sqrt(sum / timeDataArray.length);
    
    // Convert RMS to dBFS (-60dB to 0dB range)
    let dbfs = -60; // Default to -60dB for silence
    if (rms > 0) {
      dbfs = Math.max(-60, 20 * Math.log10(rms));
    }
    
    // Store the actual dBFS value
    setVolume(dbfs);
    setFrequencyData(dataArray);
    
    // Analyze audio quality
    analyzeAudioQuality(dataArray, timeDataArray, rms);
    
    // Legacy compatibility
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
    setAudioLevel(average / 255);

    animationFrameRef.current = requestAnimationFrame(analyzeAudio);
  }, [analyzeAudioQuality]);

  // Start monitoring with audio delay (实时监听带音频延迟)
  const startMonitoringWithDelay = useCallback((delaySeconds: number = 0) => {
    console.log('[useMicrophone] startMonitoringWithDelay called with delay:', delaySeconds, 'seconds');
    
    if (!audioContextRef.current || !sourceNodeRef.current) {
      const errorMsg = t("microphoneAccessFailed");
      console.warn(`[useMicrophone] Cannot start monitoring: audio context not ready`);
      setError(errorMsg);
      return;
    }
    
    try {
      // Create gain node for volume control
      gainNodeRef.current = audioContextRef.current.createGain();
      gainNodeRef.current.gain.value = monitorVolume;
      
      // Connect the audio path with or without delay
      if (delaySeconds > 0) {
        // Create delay node for audio delay
        delayNodeRef.current = audioContextRef.current.createDelay(2.0); // Max 2 seconds delay
        delayNodeRef.current.delayTime.value = delaySeconds;
        
        console.log(`[useMicrophone] Setting up audio path with ${delaySeconds}s delay: Source -> Delay -> Gain -> Speaker`);
        sourceNodeRef.current.connect(delayNodeRef.current);
        delayNodeRef.current.connect(gainNodeRef.current);
        gainNodeRef.current.connect(audioContextRef.current.destination);
      } else {
        console.log('[useMicrophone] Setting up direct audio path: Source -> Gain -> Speaker');
        sourceNodeRef.current.connect(gainNodeRef.current);
        gainNodeRef.current.connect(audioContextRef.current.destination);
        delayNodeRef.current = null; // No delay node needed
      }
      
      setIsMonitoring(true);
      console.log('[useMicrophone] Monitoring started successfully with delay:', delaySeconds, 'seconds');
    } catch (err) {
      const errorMsg = `${t("microphoneAccessFailed")}: ${err instanceof Error ? err.message : 'Unknown error'}`;
      console.error(`[useMicrophone] Failed to start monitoring:`, err);
      setError(errorMsg);
    }
  }, [monitorVolume]);

  // Legacy startMonitoring function (for backward compatibility)
  const startMonitoring = useCallback(() => {
    startMonitoringWithDelay(0);
  }, [startMonitoringWithDelay]);

  // Stop monitoring (停止实时监听)
  const stopMonitoring = useCallback(() => {
    console.log('[useMicrophone] stopMonitoring called');
    if (sourceNodeRef.current) {
      try {
        // Disconnect all connections
        sourceNodeRef.current.disconnect();
        
        if (delayNodeRef.current) {
          delayNodeRef.current.disconnect();
          delayNodeRef.current = null;
        }
        
        if (gainNodeRef.current) {
          gainNodeRef.current.disconnect();
          gainNodeRef.current = null;
        }
        
        setIsMonitoring(false);
        console.log('[useMicrophone] Monitoring stopped successfully');
      } catch (err) {
        console.error('[useMicrophone] Error stopping monitoring:', err);
      }
    }
  }, []);

  // Update monitor volume
  const updateMonitorVolume = useCallback((volume: number) => {
    setMonitorVolume(volume);
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = volume;
    }
  }, []);

  // Update monitoring delay in real-time (实时更新音频延迟)
  const updateMonitoringDelay = useCallback((delaySeconds: number) => {
    console.log('[useMicrophone] updateMonitoringDelay called with delay:', delaySeconds, 'seconds');
    
    if (!isMonitoring || !audioContextRef.current || !sourceNodeRef.current || !gainNodeRef.current) {
      console.warn('[useMicrophone] Cannot update delay: not monitoring or audio nodes not available');
      return;
    }
    
    try {
      // If switching from no delay to delay, or vice versa, we need to reconnect
      const hasDelayNode = delayNodeRef.current !== null;
      const needsDelayNode = delaySeconds > 0;
      
      if (hasDelayNode && !needsDelayNode) {
        // Switching from delay to no delay
        console.log('[useMicrophone] Switching from delay to no delay');
        sourceNodeRef.current.disconnect();
        delayNodeRef.current!.disconnect();
        delayNodeRef.current = null;
        
        sourceNodeRef.current.connect(gainNodeRef.current);
      } else if (!hasDelayNode && needsDelayNode) {
        // Switching from no delay to delay
        console.log('[useMicrophone] Switching from no delay to delay');
        sourceNodeRef.current.disconnect();
        
        delayNodeRef.current = audioContextRef.current.createDelay(2.0);
        delayNodeRef.current.delayTime.value = delaySeconds;
        
        sourceNodeRef.current.connect(delayNodeRef.current);
        delayNodeRef.current.connect(gainNodeRef.current);
      } else if (hasDelayNode && needsDelayNode) {
        // Just update the delay time
        delayNodeRef.current.delayTime.value = delaySeconds;
      }
      
      console.log('[useMicrophone] Audio delay updated successfully to:', delaySeconds, 'seconds');
    } catch (err) {
      console.error('[useMicrophone] Error updating delay:', err);
    }
  }, [isMonitoring]);

  // Start microphone test
  const startTest = useCallback(async () => {
    console.log('[useMicrophone] startTest called');
    try {
      // Clean up previous state first
      if (sourceNodeRef.current) {
        try {
          sourceNodeRef.current.disconnect();
          if (delayNodeRef.current) {
            delayNodeRef.current.disconnect();
            delayNodeRef.current = null;
          }
          if (gainNodeRef.current) {
            gainNodeRef.current.disconnect();
            gainNodeRef.current = null;
          }
          setIsMonitoring(false);
          console.log('[useMicrophone] Stopped existing monitoring before restart');
        } catch (err) {
          console.warn('[useMicrophone] Error stopping existing monitoring:', err);
        }
      }
      
      // Stop animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      // Stop media stream
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }

      // Close audio context
      if (audioContextRef.current) {
        try {
          await audioContextRef.current.close();
        } catch (err) {
          console.warn('[useMicrophone] Error closing audio context:', err);
        }
        audioContextRef.current = null;
      }

      // Reset refs
      analyserNodeRef.current = null;
      sourceNodeRef.current = null;
      gainNodeRef.current = null;
      delayNodeRef.current = null;

      // Give browser time to release resources
      await new Promise(resolve => setTimeout(resolve, 50));

      setPermissionState('pending');
      setError(null);

      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
          echoCancellation: audioConstraints.echoCancellation,
          noiseSuppression: audioConstraints.noiseSuppression,
          autoGainControl: audioConstraints.autoGainControl,
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;

      // Create audio context and analyser
      audioContextRef.current = new AudioContext();
      
      // Resume audio context if it's suspended
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }
      
      analyserNodeRef.current = audioContextRef.current.createAnalyser();
      
      // Create source node for monitoring
      sourceNodeRef.current = audioContextRef.current.createMediaStreamSource(stream);
      sourceNodeRef.current.connect(analyserNodeRef.current);

      // Configure analyser
      analyserNodeRef.current.fftSize = 256;
      analyserNodeRef.current.smoothingTimeConstant = 0.8;

      // Get technical details
      const audioTrack = stream.getAudioTracks()[0];
      const settings = audioTrack.getSettings();
      
      setTechnicalDetails({
        sampleRate: audioContextRef.current.sampleRate,
        channels: settings.channelCount || 1,
        bitDepth: 16 // Default assumption
      });

      setPermissionState('granted');
      setHasPermission(true);
      
      // Start audio analysis
      analyzeAudio();

      // Get available devices
      await getDevices();

      console.log('[useMicrophone] Audio test started successfully');
      console.log('[useMicrophone] Audio context state:', audioContextRef.current.state);
      console.log('[useMicrophone] Source node created:', !!sourceNodeRef.current);

    } catch (err) {
      console.error('[useMicrophone] Error starting microphone test:', err);
      if (err instanceof Error && err.name === 'NotAllowedError') {
        setPermissionState('denied');
        setError(t("microphoneAccessDenied"));
      } else {
        setError(t("microphoneAccessFailed"));
      }
    }
  }, [selectedDeviceId, audioConstraints, analyzeAudio, getDevices]);

  // Stop microphone test
  const stopTest = useCallback(() => {
    console.log('[useMicrophone] stopTest called.');
    // Stop monitoring first
    stopMonitoring(); // It's safe to call unconditionally.

    // Stop recording if active
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }

    // Stop recording timer
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }

    // Stop playback if active
    if (audioElementRef.current) {
      audioElementRef.current.pause();
      audioElementRef.current = null;
    }
    if (playbackTimerRef.current) {
      clearInterval(playbackTimerRef.current);
      playbackTimerRef.current = null;
    }

    // Stop animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Stop media stream
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    // Close audio context
    if (audioContextRef.current) {
      audioContextRef.current.close().catch(err => console.warn('[useMicrophone] Error closing audio context:', err));
      audioContextRef.current = null;
    }

    analyserNodeRef.current = null;
    sourceNodeRef.current = null;
    gainNodeRef.current = null;
    delayNodeRef.current = null;
    mediaRecorderRef.current = null;
    audioElementRef.current = null;
    
    setPermissionState('idle');
    setVolume(0);
    setFrequencyData(null);
    setTechnicalDetails(null);
    setHasPermission(false);
    setAudioLevel(0);
    setIsMonitoring(false);
    setIsRecording(false);
    setRecordingDuration(0);
    setIsPlaying(false);
    setPlaybackProgress(0);
    setPlaybackDuration(0);
    setAudioUrl(null);
    console.log('[useMicrophone] Test stopped, state reset to idle.');
  }, [stopMonitoring]);

  // Start recording
  const startRecording = useCallback(() => {
    if (!mediaStreamRef.current) return;

    try {
      // 清理之前的录音数据
      audioChunksRef.current = [];
      setRecordingDuration(0);
      setPlaybackProgress(0);
      setPlaybackDuration(0);
      
      // 停止当前播放
      if (audioElementRef.current) {
        audioElementRef.current.pause();
        audioElementRef.current = null;
      }
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current);
        playbackTimerRef.current = null;
      }
      setIsPlaying(false);

      mediaRecorderRef.current = new MediaRecorder(mediaStreamRef.current);

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        // 停止录音计时器并获取最终录音时长
        if (recordingTimerRef.current) {
          clearInterval(recordingTimerRef.current);
          recordingTimerRef.current = null;
        }
        
        // 计算最终录音时长
        const finalDuration = (Date.now() - recordingStartTimeRef.current) / 1000;
        setRecordingDuration(finalDuration);
        setPlaybackDuration(finalDuration);
        console.log('[useMicrophone] Recording stopped, final duration:', finalDuration, 'seconds');
      };

      // 开始录音计时器
      recordingStartTimeRef.current = Date.now();
      recordingTimerRef.current = setInterval(() => {
        const elapsed = (Date.now() - recordingStartTimeRef.current) / 1000;
        setRecordingDuration(elapsed);
      }, 100); // 每100ms更新一次

      mediaRecorderRef.current.start();
      setIsRecording(true);
    } catch (err) {
      console.error('[useMicrophone] Error starting recording:', err);
      setError(t("recordingStartFailed"));
    }
  }, [recordingDuration]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
    
    // 停止录音计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
  }, []);

  // Play recording
  const playRecording = useCallback(() => {
    if (!audioUrl) return;

    try {
      // 停止当前播放
      if (audioElementRef.current) {
        audioElementRef.current.pause();
        audioElementRef.current = null;
      }
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current);
        playbackTimerRef.current = null;
      }

      const audio = new Audio(audioUrl);
      audioElementRef.current = audio;

      audio.onloadedmetadata = () => {
        // 只有当音频文件的duration有效时才更新playbackDuration
        if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
          setPlaybackDuration(audio.duration);
          console.log('[useMicrophone] Audio metadata loaded, duration:', audio.duration, 'seconds');
        } else {
          console.warn('[useMicrophone] Audio duration is invalid:', audio.duration);
        }
      };

      audio.onplay = () => {
        setIsPlaying(true);
        setPlaybackProgress(0);
        
        // 开始播放进度计时器
        playbackTimerRef.current = setInterval(() => {
          if (audio.currentTime !== undefined && audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
            const progress = audio.currentTime / audio.duration;
            if (!isNaN(progress) && isFinite(progress)) {
              setPlaybackProgress(Math.min(progress, 1)); // 确保进度不超过1
            }
          }
        }, 100); // 每100ms更新一次
      };

      audio.onpause = () => {
        setIsPlaying(false);
        if (playbackTimerRef.current) {
          clearInterval(playbackTimerRef.current);
          playbackTimerRef.current = null;
        }
      };

      audio.onended = () => {
        setIsPlaying(false);
        setPlaybackProgress(1);
        if (playbackTimerRef.current) {
          clearInterval(playbackTimerRef.current);
          playbackTimerRef.current = null;
        }
        audioElementRef.current = null;
      };

      audio.onerror = () => {
        setIsPlaying(false);
        setError(t("recordingStartFailed"));
        if (playbackTimerRef.current) {
          clearInterval(playbackTimerRef.current);
          playbackTimerRef.current = null;
        }
        audioElementRef.current = null;
      };

      audio.play().catch(err => {
        console.error('[useMicrophone] Error playing recording:', err);
        setError(t("recordingStartFailed"));
        setIsPlaying(false);
      });
    } catch (err) {
      console.error('[useMicrophone] Error setting up audio playback:', err);
      setError(t("recordingStartFailed"));
    }
  }, [audioUrl]);

  // Stop playback
  const stopPlayback = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current.pause();
      audioElementRef.current.currentTime = 0;
      audioElementRef.current = null;
    }
    if (playbackTimerRef.current) {
      clearInterval(playbackTimerRef.current);
      playbackTimerRef.current = null;
    }
    setIsPlaying(false);
    setPlaybackProgress(0);
  }, []);

  // Legacy function for backward compatibility
  const legacyStartRecording = useCallback(async () => {
    await startTest();
  }, [startTest]);

  const setSelectedDeviceWrapper = useCallback((deviceId: string) => {
    setSelectedDevice(deviceId);
  }, []);

  // Cleanup for audioUrl to prevent memory leaks
  useEffect(() => {
    // This effect runs its cleanup function whenever audioUrl changes,
    // revoking the old URL to prevent memory leaks.
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Cleanup on unmount
  useEffect(() => {
    // This effect's dependency `stopTest` is stable, so this cleanup function
    // will only run when the component unmounts.
    return () => {
      console.log('[useMicrophone] Cleanup on unmount: calling stopTest()');
      stopTest();
    };
  }, [stopTest]);

  // Legacy effect for backward compatibility
  useEffect(() => {
    // Check if we already have permission
    navigator.permissions?.query({ name: 'microphone' as PermissionName })
      .then(permission => {
        if (permission.state === 'granted') {
          setHasPermission(true);
          getDevices();
        }
      })
      .catch(() => {
        // Permissions API not supported, try to get devices anyway
        getDevices();
      });
  }, [getDevices]);

  return {
    permissionState,
    devices,
    volume,
    technicalDetails,
    isRecording,
    audioUrl,
    analyserNode: analyserNodeRef.current,
    frequencyData,
    audioQuality,
    startTest,
    stopTest,
    startRecording,
    stopRecording,
    playRecording,
    error,
    // 录音计时器和播放进度
    recordingDuration,
    isPlaying,
    playbackProgress,
    playbackDuration,
    stopPlayback,
    // 实时监听功能
    isMonitoring,
    monitorVolume,
    startMonitoring,
    stopMonitoring,
    setMonitorVolume: updateMonitorVolume,
    startMonitoringWithDelay, // 新增带延迟的监听函数
    updateMonitoringDelay, // 实时更新音频延迟
    // Legacy props for backward compatibility
    audioLevel,
    selectedDevice,
    setSelectedDevice: setSelectedDeviceWrapper,
    hasPermission,
  };
};