import { 
  GA4Event, 
  DeviceTestEvent, 
  PageViewEvent, 
  UserInteractionEvent,
  ConsentSettings 
} from '@/types/analytics';
import { ga4Config, shouldEnableAnalytics, debugLog, errorLog } from '@/config/analytics';

// GA4 初始化状态
let isInitialized = false;
let isScriptLoaded = false;

/**
 * 动态加载 GA4 脚本 - 优化版本
 */
const loadGA4Script = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isScriptLoaded) {
      resolve();
      return;
    }

    // 使用 requestIdleCallback 在浏览器空闲时加载
    const loadScript = () => {
      const script = document.createElement('script');
      script.async = true;
      script.defer = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${ga4Config.measurementId}`;

      // 添加资源提示以提高性能
      script.crossOrigin = 'anonymous';

      script.onload = () => {
        isScriptLoaded = true;
        debugLog('GA4 脚本加载成功');
        resolve();
      };

      script.onerror = () => {
        errorLog('GA4 脚本加载失败');
        reject(new Error('Failed to load GA4 script'));
      };

      document.head.appendChild(script);
    };

    // 使用 requestIdleCallback 延迟加载，如果不支持则使用 setTimeout
    if ('requestIdleCallback' in window) {
      requestIdleCallback(loadScript, { timeout: 2000 });
    } else {
      setTimeout(loadScript, 100);
    }
  });
};

/**
 * 初始化 GA4
 */
export const initializeGA4 = async (): Promise<void> => {
  if (!shouldEnableAnalytics()) {
    debugLog('GA4 分析功能已禁用');
    return;
  }

  if (isInitialized) {
    debugLog('GA4 已经初始化');
    return;
  }

  try {
    // 加载 GA4 脚本
    await loadGA4Script();
    
    // 初始化 dataLayer
    window.dataLayer = window.dataLayer || [];
    
    // 定义 gtag 函数
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    
    // 设置初始时间戳
    window.gtag('js', new Date());
    
    // 配置 GA4
    window.gtag('config', ga4Config.measurementId, {
      // 隐私保护设置
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false,
      cookie_flags: 'SameSite=None;Secure',
      // 调试模式
      debug_mode: ga4Config.debug,
      // 自定义参数
      custom_map: {
        'device_type': 'device_type',
        'test_result': 'test_result',
        'language': 'language'
      }
    });
    
    isInitialized = true;
    debugLog('GA4 初始化成功', { measurementId: ga4Config.measurementId });

    // 初始化性能跟踪
    initializePerformanceTracking();

  } catch (error) {
    errorLog('GA4 初始化失败', error);
    throw error;
  }
};

/**
 * 发送页面浏览事件
 */
export const trackPageView = (pageData: PageViewEvent): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  try {
    window.gtag('event', 'page_view', {
      page_title: pageData.page_title,
      page_location: pageData.page_location,
      page_path: pageData.page_path,
      language: pageData.language || 'en'
    });
    
    debugLog('页面浏览事件已发送', pageData);
  } catch (error) {
    errorLog('发送页面浏览事件失败', error);
  }
};

/**
 * 发送自定义事件
 */
export const trackEvent = (event: GA4Event): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  try {
    const eventData: any = {
      event_category: event.category || 'general',
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    };

    window.gtag('event', event.action, eventData);
    
    debugLog('自定义事件已发送', { action: event.action, data: eventData });
  } catch (error) {
    errorLog('发送自定义事件失败', error);
  }
};

/**
 * 发送设备测试事件
 */
export const trackDeviceTest = (event: DeviceTestEvent): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  try {
    const eventData = {
      event_category: 'device_test',
      device_type: event.device_type,
      test_result: event.test_result,
      test_duration: event.test_duration,
      error_message: event.error_message,
      value: event.value,
      ...event.custom_parameters
    };

    window.gtag('event', event.action, eventData);
    
    debugLog('设备测试事件已发送', eventData);
  } catch (error) {
    errorLog('发送设备测试事件失败', error);
  }
};

/**
 * 发送用户交互事件
 */
export const trackUserInteraction = (event: UserInteractionEvent): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  try {
    const eventData = {
      event_category: 'user_interaction',
      interaction_type: event.interaction_type,
      element_id: event.element_id,
      element_class: event.element_class,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    };

    window.gtag('event', event.action, eventData);
    
    debugLog('用户交互事件已发送', eventData);
  } catch (error) {
    errorLog('发送用户交互事件失败', error);
  }
};

/**
 * 设置用户同意状态
 */
export const setConsent = (consentSettings: Partial<ConsentSettings>): void => {
  if (!shouldEnableAnalytics()) {
    return;
  }

  try {
    window.gtag('consent', 'update', consentSettings);
    debugLog('用户同意状态已更新', consentSettings);
  } catch (error) {
    errorLog('更新用户同意状态失败', error);
  }
};

/**
 * 发送性能指标事件
 */
export const trackPerformance = (metrics: {
  name: string;
  value: number;
  unit?: string;
  category?: string;
}): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  try {
    window.gtag('event', 'performance_metric', {
      event_category: metrics.category || 'performance',
      metric_name: metrics.name,
      metric_value: metrics.value,
      metric_unit: metrics.unit || 'ms',
      value: metrics.value
    });

    debugLog('性能指标已发送', metrics);
  } catch (error) {
    errorLog('发送性能指标失败', error);
  }
};

/**
 * 自动收集和发送 Web Vitals 性能指标
 */
export const initializePerformanceTracking = (): void => {
  if (!isInitialized || !shouldEnableAnalytics()) {
    return;
  }

  // 监听页面加载性能
  if ('performance' in window && 'getEntriesByType' in performance) {
    // 等待页面加载完成
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

        if (navigation) {
          // 发送页面加载时间
          trackPerformance({
            name: 'page_load_time',
            value: Math.round(navigation.loadEventEnd - navigation.fetchStart),
            unit: 'ms',
            category: 'page_performance'
          });

          // 发送 DOM 内容加载时间
          trackPerformance({
            name: 'dom_content_loaded',
            value: Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart),
            unit: 'ms',
            category: 'page_performance'
          });

          // 发送首次内容绘制时间
          const paintEntries = performance.getEntriesByType('paint');
          const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
          if (fcp) {
            trackPerformance({
              name: 'first_contentful_paint',
              value: Math.round(fcp.startTime),
              unit: 'ms',
              category: 'page_performance'
            });
          }
        }
      }, 1000);
    });
  }
};

/**
 * 获取 GA4 初始化状态
 */
export const isGA4Initialized = (): boolean => {
  return isInitialized && shouldEnableAnalytics();
};
