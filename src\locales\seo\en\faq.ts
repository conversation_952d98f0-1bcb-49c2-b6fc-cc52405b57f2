/**
 * English - Frequently Asked Questions
 * Contains all frequently asked questions organized by device categories
 */

import type { FAQTranslation } from '../types';

export const enFAQ: FAQTranslation = {
  title: "Frequently Asked Questions",
  home: {
    q1: "Is this device testing tool free?",
    a1: "Yes, our device testing tool is completely free and requires no registration to use all features.",
    q2: "Does the testing process collect my personal information?",
    a2: "No, all tests are performed locally in your browser. We do not collect or store any personal data.",
    q3: "Which browsers are supported?",
    a3: "Compatible with modern browsers including Chrome, Firefox, Safari, Edge. Latest versions are recommended.",
    q4: "Why should I test my devices?",
    a4: "Device testing helps ensure your hardware works properly for online meetings, gaming, and other scenarios, helping identify and resolve issues proactively."
  },
  camera: {
    q1: "Why won't my camera start?",
    a1: "Check camera permission settings, ensure browser has camera access, and verify no other programs are using the camera.",
    q2: "What if my video is blurry?",
    a2: "Clean the camera lens, adjust lighting conditions, and ensure adequate lighting.",
    q3: "How can I improve video quality?",
    a3: "Ensure good lighting, clean camera lens, close unnecessary applications to free up system resources.",
    q4: "What camera resolutions are supported?",
    a4: "We support various resolutions from 480p to 4K depending on your camera's hardware specifications.",
    q5: "Can I test external cameras?",
    a5: "Yes, we support testing all external camera devices connected via USB."
  },
  microphone: {
    q1: "What if there's no sound from my microphone or low volume?",
    a1: "Check microphone permissions, volume settings, ensure device is properly connected and not muted, adjust input volume.",
    q2: "How to improve poor audio quality or noise?",
    a2: "Choose a quiet environment, adjust microphone distance, enable noise reduction, check device connections.",
    q3: "Why is there echo?",
    a3: "Use headphones instead of speakers to prevent speaker sound from being picked up by microphone, or enable echo cancellation.",
    q4: "Can I test external microphones?",
    a4: "Yes, we support testing USB microphones, headset microphones, and other external devices.",
    q5: "Is the recording feature safe?",
    a5: "Recording is performed completely locally and never uploaded to servers, protecting your privacy."
  },
  headphones: {
    q1: "What if I can't hear test audio or volume is low?",
    a1: "Check volume settings, ensure headphones are properly connected, select correct audio output device.",
    q2: "What about left/right channel imbalance?",
    a2: "Check headphone connections, test other audio devices, adjust system audio balance settings.",
    q3: "How to test audio quality?",
    a3: "Use our frequency response and stereo test features for comprehensive audio quality assessment.",
    q4: "Wireless headphone connection unstable?",
    a4: "Ensure sufficient battery, reduce interference sources, re-pair Bluetooth, check codec compatibility.",
    q5: "How to adjust headphone volume?",
    a5: "Adjust system and application volume, check headphone volume controls, ensure audio driver settings are correct."
  },
  keyboard: {
    q1: "Some keys not recognized?",
    a1: "Check keyboard connection, reinstall drivers, verify browser keyboard permissions, test other applications.",
    q2: "Key delay or repeat input issues?",
    a2: "Check keyboard polling rate, clean internal components, verify system load, keyboard replacement may be needed.",
    q3: "How to test gaming keyboard macros?",
    a3: "Our tool primarily tests basic key functions. Macro functionality should be tested in specific games or applications.",
    q4: "What's the difference between mechanical and membrane keyboards?",
    a4: "Mechanical keyboards offer faster response, longer lifespan, better feel. Membrane keyboards are quieter and lower cost.",
    q5: "Keyboard backlight not working?",
    a5: "Check keyboard settings, verify backlight shortcuts, update drivers and management software."
  },
  mouse: {
    q1: "Mouse pointer movement inaccurate?",
    a1: "Clean mouse sensor, use proper mouse pad, adjust DPI settings, check acceleration settings.",
    q2: "Mouse clicks not responding or double-click issues?",
    a2: "Adjust double-click speed, clean button switches, check microswitch lifespan, repair or replacement may be needed.",
    q3: "Mouse wheel not smooth?",
    a3: "Clean mouse wheel dust and debris, check encoder status, adjust scroll settings, avoid excessive force.",
    q4: "How to choose gaming mouse DPI?",
    a4: "Depends on game type and personal preference. FPS games typically use 400-1600 DPI, RTS games can be higher.",
    q5: "Mouse not working properly on certain surfaces?",
    a5: "Use dedicated mouse pad, avoid glass or reflective surfaces, choose surface material suitable for sensor type."
  },
  network: {
    q1: "Network speed test results inaccurate?",
    a1: "Close other network applications, use wired connection, select nearby test server, average multiple tests.",
    q2: "How to optimize high latency?",
    a2: "Use wired connection, choose better DNS servers, disable QoS limitations, ask network service provider for line quality check.",
    q3: "Unstable network with frequent disconnections?",
    a3: "Check network cable connections, restart router, update network drivers, avoid interference.",
    q4: "How to improve weak WiFi signal?",
    a4: "Move closer to router, reduce obstacles, switch to 5GHz band, use WiFi extender, upgrade router antenna.",
    q5: "Why is upload speed much slower than download speed?",
    a5: "This is normal. Most broadband packages design lower upload speeds than download speeds, suitable for general user needs."
  },
  meeting: {
    q1: "How to prepare for important online meetings?",
    a1: "Perform complete device testing 24 hours before meeting, check camera position and lighting, test microphone audio levels, ensure stable network connection.",
    q2: "What are optimal camera settings for meetings?",
    a2: "Position camera at eye level, ensure face is well-lit with front lighting, use neutral background, maintain 60-90cm distance from camera.",
    q3: "How to eliminate background noise during calls?",
    a3: "Use noise-canceling headphones or microphone, choose quiet environment, close windows and doors, mute when not speaking.",
    q4: "What network speed is needed for video conferencing?",
    a4: "HD video calls require minimum 1.5Mbps upload/download speed, group calls recommend 3Mbps+, high-quality video sharing needs 5Mbps+.",
    q5: "How to resolve connection issues during meetings?",
    a5: "Switch to wired connection if possible, close bandwidth-intensive applications, reduce video quality if needed, reconnect to meeting."
  },
  gaming: {
    q1: "What's ideal network latency for competitive gaming?",
    a1: "For FPS and competitive games: <50ms excellent, 50-100ms acceptable, >100ms may cause noticeable lag. Use wired connection for optimal performance.",
    q2: "How to optimize gaming peripherals for best performance?",
    a2: "Update all device drivers, adjust DPI settings for gaming style, set polling rate to highest supported value.",
    q3: "What are most important headphone features for gaming?",
    a3: "Low latency (wired preferred), good positional audio/soundstage, comfort for long sessions, clear microphone for team communication.",
    q4: "How to reduce input lag in gaming setup?",
    a4: "Use wired peripherals, enable game mode on display, disable unnecessary background processes, update graphics drivers.",
    q5: "Mechanical vs membrane keyboards for gaming?",
    a5: "Mechanical keyboards offer faster response times, better tactile feedback, higher durability, N-key rollover. Optimal for gaming despite higher cost and noise."
  },
  tools: {
    q1: "How do I choose the right testing tool?",
    a1: "Select tools based on your specific needs: camera test for video calls, microphone test for audio quality, keyboard/mouse tests for gaming, network test for connectivity issues.",
    q2: "Can I use multiple tools simultaneously?",
    a2: "For best results, use one tool at a time to avoid resource conflicts. Complete each test before moving to the next device.",
    q3: "How often should I test my devices?",
    a3: "Test devices monthly for regular use, weekly for professional/gaming setups, or immediately when experiencing issues.",
    q4: "Are the test results accurate?",
    a4: "Our tools provide professional-grade accuracy using industry-standard testing methods. Results are reliable for most use cases.",
    q5: "What should I do if a test fails?",
    a5: "Check device connections, update drivers, restart the browser, ensure proper permissions, and consult our troubleshooting guides for specific solutions."
  }
};
