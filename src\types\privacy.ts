// 隐私和 Cookie 同意相关类型定义

export interface CookieConsent {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
  timestamp: number;
  version: string;
}

export interface PrivacySettings {
  cookieConsent: CookieConsent | null;
  dataProcessing: boolean;
  thirdPartyServices: boolean;
  lastUpdated: number;
}

export interface CookieCategory {
  id: keyof Omit<CookieConsent, 'timestamp' | 'version'>;
  name: string;
  description: string;
  required: boolean;
  enabled: boolean;
}

export interface ConsentBannerProps {
  onAcceptAll: () => void;
  onRejectAll: () => void;
  onCustomize: () => void;
  onClose: () => void;
  isVisible: boolean;
}

export interface ConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (consent: Partial<CookieConsent>) => void;
  currentConsent: CookieConsent | null;
}

// Cookie 同意状态
export type ConsentStatus = 'pending' | 'accepted' | 'rejected' | 'customized';

// 隐私政策版本
export const PRIVACY_POLICY_VERSION = '1.0.0';

// 默认 Cookie 同意设置
export const DEFAULT_COOKIE_CONSENT: CookieConsent = {
  necessary: true,
  analytics: false,
  marketing: false,
  preferences: false,
  timestamp: Date.now(),
  version: PRIVACY_POLICY_VERSION
};
