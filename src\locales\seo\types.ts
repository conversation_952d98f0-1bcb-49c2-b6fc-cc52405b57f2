/**
 * SEO翻译模块的类型定义
 * 定义了所有SEO相关翻译内容的类型结构
 */

// 使用指南类型
export interface UsageGuideTranslation {
  title: string;
  tips: {
    title: string;
  };
  [key: string]: any;
}

// 技术规格类型
export interface TechnicalSpecsTranslation {
  title: string;
  systemRequirements: string;
  parameters: string;
  compatibility: string;
  [key: string]: any;
}

// 最佳实践类型
export interface BestPracticesTranslation {
  title: string;
  recommended: string;
  avoid: string;
  optimization: string;
  [key: string]: any;
}

// 行业标准类型
export interface IndustryStandardsTranslation {
  title: string;
  compliance: string;
  [key: string]: any;
}

// 增强功能翻译类型
export interface EnhancedTranslation {
  usageGuide: UsageGuideTranslation;
  technicalSpecs: TechnicalSpecsTranslation;
  bestPractices: BestPracticesTranslation;
  industryStandards: IndustryStandardsTranslation;
}

// SEO页脚翻译类型
export interface SEOFooterTranslation {
  relatedKeywords: string;
  technicalInfo: string;
  supportedBrowsers: string;
  browserCompatibility: string;
  systemRequirements: string;
  requirements: string;
  aboutTitle: string;
  about: {
    home: string;
    tools: string;
    camera: string;
    microphone: string;
    headphones: string;
    keyboard: string;
    mouse: string;
    network: string;
    meeting: string;
    gaming: string;
  };
}

// SEO关键词翻译类型
export interface SEOKeywordsTranslation {
  deviceTest: string;
  hardwareCheck: string;
  onlineMeeting: string;
  gamingSetup: string;
  cameraTest: string;
  webcamTest: string;
  videoQuality: string;
  videoCall: string;
  microphoneTest: string;
  audioQuality: string;
  voiceRecording: string;
  micTest: string;
  headphonesTest: string;
  speakerTest: string;
  audioOutput: string;
  stereoTest: string;
  keyboardTest: string;
  keyTest: string;
  typingTest: string;
  gamingKeyboard: string;
  mouseTest: string;
  clickTest: string;
  scrollTest: string;
  gamingMouse: string;
  networkTest: string;
  speedTest: string;
  latencyTest: string;
  internetSpeed: string;
  meetingTest: string;
  deviceCompatibility: string;
  gamingPeripherals: string;
  esports: string;
  gamingHardware: string;
}

// FAQ翻译类型
export interface FAQTranslation {
  title: string;
  [key: string]: any;
}

// 术语表翻译类型
export interface GlossaryTranslation {
  title: string;
  terms: {
    [key: string]: {
      title: string;
      description: string;
    };
  };
}

// 设备故障排查详细类型
export interface DeviceTroubleshootingDetail {
  // 基础问题（必需）
  issue1: string;
  solution1: string;
  issue2: string;
  solution2: string;
  issue3: string;
  solution3: string;

  // 扩展问题（可选）
  issue4?: string;
  solution4?: string;
  issue5?: string;
  solution5?: string;
  issue6?: string;
  solution6?: string;
  issue7?: string;
  solution7?: string;
  issue8?: string;
  solution8?: string;
  issue9?: string;
  solution9?: string;
  issue10?: string;
  solution10?: string;
  issue11?: string;
  solution11?: string;
  issue12?: string;
  solution12?: string;

  // 分步解决方案
  step1: string;
  step2: string;
  step3: string;
  step4: string;
  step5: string;
  step6?: string;
  step7?: string;
  step8?: string;
  step9?: string;
  step10?: string;

  // 跨平台操作步骤（可选）
  windowsSteps?: string[];
  macosSteps?: string[];
  linuxSteps?: string[];

  // 预防性维护（可选）
  maintenance?: string[];
}

// 故障排查翻译类型
export interface TroubleshootingTranslation {
  title: string;
  commonIssues: string;
  stepByStep: string;
  camera: DeviceTroubleshootingDetail;
  microphone: DeviceTroubleshootingDetail;
  headphones: DeviceTroubleshootingDetail;
  keyboard: DeviceTroubleshootingDetail;
  mouse: DeviceTroubleshootingDetail;
  network: DeviceTroubleshootingDetail;
  meeting?: DeviceTroubleshootingDetail;
  gaming?: DeviceTroubleshootingDetail;
  tools?: DeviceTroubleshootingDetail;
}

// 单个语言的SEO翻译类型
export interface LanguageSEOTranslation {
  enhanced: EnhancedTranslation;
  seoFooter: SEOFooterTranslation;
  seoKeywords: SEOKeywordsTranslation;
  faq: FAQTranslation;
  glossary: GlossaryTranslation;
  troubleshooting: TroubleshootingTranslation;
}

// 单个语言的SEO翻译类型别名（新的命名约定）
export type SEOTranslation = LanguageSEOTranslation;

// 完整的SEO翻译类型
export interface SEOTranslations {
  [language: string]: LanguageSEOTranslation;
}

// 支持的页面类型
export type PageType = 'home' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';

// 支持的语言类型
export type SupportedLanguage = 'zh' | 'en' | 'es' | 'de' | 'ja' | 'ko' | 'fr';
