import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { GlassCard } from "@/components/ui/GlassCard";
import { getSEOTranslation } from "@/locales/seo";

interface GlossaryProps {
  pageType: 'home' | 'tools' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';
}

export const Glossary: React.FC<GlossaryProps> = ({ pageType }) => {
  const { t, language } = useLanguage();

  const getGlossaryTerms = () => {
    const termMap = {
      home: ['resolution', 'latency', 'frameRate', 'bandwidth'],
      tools: ['compatibility', 'accuracy', 'realTime', 'calibration', 'benchmark'],
      camera: ['resolution', 'frameRate', 'fps', 'megapixel', 'exposure'],
      microphone: ['sampleRate', 'bitRate', 'noiseReduction', 'sensitivity', 'frequency'],
      headphones: ['frequency', 'impedance', 'soundStage', 'drivers', 'thd'],
      keyboard: ['keyTravel', 'actuationForce', 'tactile', 'linear', 'polling'],
      mouse: ['dpi', 'pollingRate', 'acceleration', 'liftOffDistance', 'tracking'],
      network: ['bandwidth', 'latency', 'jitter', 'packetLoss', 'throughput'],
      meeting: ['codec', 'compression', 'bandwidth', 'latency'],
      gaming: ['inputLag', 'polling', 'dpi', 'frameRate']
    };

    return termMap[pageType] || termMap.home;
  };

  const terms = getGlossaryTerms();

  return (
    <GlassCard className="p-6 mt-8">
      <h3 className="text-xl font-semibold text-white mb-6">
        {getSEOTranslation(language, 'glossary.title')}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {terms.map((term, index) => (
          <div key={index} className="border-l-2 border-blue-400/50 pl-4">
            <h4 className="text-white font-medium mb-1">
              {getSEOTranslation(language, `glossary.terms.${term}.title`)}
            </h4>
            <p className="text-white/70 text-sm">
              {getSEOTranslation(language, `glossary.terms.${term}.description`)}
            </p>
          </div>
        ))}
      </div>
    </GlassCard>
  );
};