import { TranslationKeys } from './types';

export const ja: TranslationKeys = {
  // 基本ナビゲーション
  home: "ホーム",
  tools: "ツール",
  meetingTest: "ミーティングチェック",
  keyboardTest: "キーボードテスト",
  mouseTest: "マウステスト",
  headphonesTest: "ヘッドフォンテスト",
  siteName: "Setup Check",
  siteSubtitle: "ハードウェアチェック",
  selectScenario: "テストシナリオを選択",
  onlineMeeting: "オンラインミーティングチェック",
  onlineMeetingDesc: "ビデオ通話用のマイク、スピーカー、カメラをテストします",
  startTest: "テスト開始",
  microphoneTest: "マイクテスト",
  speakerTest: "スピーカーテスト",
  cameraTest: "カメラテスト",
  next: "次へ",
  back: "戻る",
  finish: "完了",
  
  // ToolsPage 関連
  deviceTestingTools: "デバイステストツール",
  deviceTestingToolsDescription: "あらゆるシナリオでデバイスが完璧に動作することを確認するための包括的なハードウェアテストツールコレクション。",
  testingTools: "テストツール",
  hardwareTesting: "ハードウェアテスト",
  deviceTools: "デバイスツール",
  audioQuality: "音質",
  noiseLevel: "ノイズレベル",
  sensitivity: "感度",
  stereoBalance: "ステレオバランス",
  volumeLevel: "音量レベル",
  audioOutput: "オーディオ出力",
  keyResponse: "キー反応",
  keyMapping: "キーマッピング",
  typingSpeed: "タイピング速度",
  clickAccuracy: "クリック精度",
  scrollFunction: "スクロール機能",
  internetSpeed: "インターネット速度",
  connectionStability: "接続安定性",
  quickTest: "クイックテスト",
  needHelp: "ヘルプが必要ですか？",
  testingToolsDescription: "当社のテストツールはシンプルで正確に設計されています。各ツールはハードウェア設定を最適化するための詳細なフィードバックを提供します。",
  noRegistration: "登録不要",
  accuracy: "精度",
  free: "無料",
  
  // マイクテスト
  micTestTitle: "マイクをテストしてください",
  micTestDesc: "マイクに向かって話してください。オーディオレベルが反応するのが見えるはずです。",
  selectMicrophone: "マイクを選択",
  microphoneWorking: "マイクが動作しています！",
  microphoneNotDetected: "マイクが検出されませんでした",
  
  // スピーカーテスト
  speakerTestTitle: "スピーカーをテストしてください",
  speakerTestDesc: "下のボタンをクリックしてテスト音を再生してください。",
  playTestSound: "テスト音を再生",
  canYouHear: "テスト音が聞こえますか？",
  
  // カメラテスト
  cameraTestTitle: "カメラをテストしてください",
  cameraTestDesc: "カメラの映像が下に表示されるはずです。",
  selectCamera: "カメラを選択",
  cameraWorking: "カメラが動作しています！",
  cameraNotDetected: "カメラが検出されませんでした",
  cameraTestModule: "カメラテストモジュール",
  smartScorecard: "スマートスコアカード",
  startTestToSeeScore: "テストを開始してスコアを確認",
  resolution: "解像度",
  frameRate: "フレームレート",
  colorBrightness: "色と明度",
  overallRating: "総合評価",
  takePhoto: "写真を撮る",
  mirror: "ミラー",
  fullscreen: "フルスクリーン",
  downloadPhoto: "写真をダウンロード",
  deletePhoto: "写真を削除",
  photoPreview: "写真プレビュー",
  cameraCapture: "カメラキャプチャ",
  deviceName: "デバイス",
  realTimeInfo: "リアルタイム情報",
  
  // キーボードテスト
  keyboardTestTitle: "キーボードテスト",
  keyboardTestDesc: "キーボードの任意のキーを押してテストしてください",
  pressAnyKey: "任意のキーを押してテストを開始...",
  keyPressed: "押されたキー：",
  
  // マウステスト
  mouseTestTitle: "マウステスト",
  mouseTestDesc: "マウスボタンとスクロールホイールをテストしてください",
  leftClick: "左クリック",
  rightClick: "右クリック",
  middleClick: "中クリック/スクロール",
  scrollUp: "上にスクロール",
  scrollDown: "下にスクロール",
  clickButtons: "上のボタンをクリックするか、マウスを使用してください",
  
  // テスト結果
  testComplete: "テスト完了！",
  allTestsPassed: "すべてのテストが正常に完了しました",
  copyReport: "レポートをクリップボードにコピー",
  reportCopied: "レポートがクリップボードにコピーされました！",
  
  // ページコンテンツ
  gamingSetup: "ゲーミングセットアップチェック",
  gamingSetupDesc: "最適なゲーミング体験のために周辺機器をテストします",
  audioTest: "オーディオテスト",
  recommended: "推奨",
  comingSoon: "近日公開",
  individualDeviceTests: "個別デバイステスト",
  stepOf: "ステップ {current} / {total}",
  invalidScenario: "無効なシナリオ",
  runTestsAgain: "テストを再実行",
  
  // テストコンポーネント
  cameraTestTitle2: "カメラテスト",
  cameraTestDesc2: "会議中に他の人があなたを明確に見ることができるように、カメラをテストしてください。",
  selectCamera2: "カメラを選択",
  cameraPreview: "カメラプレビュー",
  cameraPlaceholder: "カメラプレビューがここに表示されます",
  startCameraTest: "カメラテストを開始",
  stopCamera: "カメラを停止",
  cameraWorking2: "✅ カメラが動作しています！",
  cameraWorkingNormally: "カメラが正常に動作しています",
  cameraStartedSuccessfully: "カメラが正常に開始されました",
  analyzingVideoQuality: "ビデオ品質を分析中",
  cameraWorkingDesc: "カメラが正常に動作しています。十分な照明があることを確認し、フレーム内で中央に位置してください。",
  cameraTips: "💡 カメラのコツ：",
  cameraTip1: "• あなたの前に十分な照明があることを確認してください",
  cameraTip2: "• カメラを目の高さに位置させてください",
  cameraTip3: "• より鮮明な画像のためにカメラレンズを清掃してください",
  cameraTip4: "• 安定したインターネット接続を確認してください",
  backSpeakerTest: "戻る：スピーカーテスト",
  finishTesting: "テストを完了",
  
  keyboardTestTitle2: "キーボードテスト",
  keyboardTestDesc2: "任意のキーを押してテストしてください。押されるとキーが光ります。",
  resetTest: "テストをリセット",
  backToHome: "ホームに戻る",
  recentKeyPresses: "最近のキー押下",
  space: "スペース",
  testingTips: "💡 テストのコツ：",
  keyboardTip1: "• 異なる種類のキーを押してください",
  keyboardTip2: "• Shift、Ctrl、Altなどの特殊キーをテストしてください",
  keyboardTip3: "• すべてのキーが適切に反応するかチェックしてください",
  keyboardTip4: "• スムーズな動作を確保するためにテキストを入力してみてください",
  
  micTestTitle2: "マイクテスト",
  micTestDesc2: "会議中に他の人があなたの声を明確に聞くことができるように、マイクをテストしてください。",
  audioLevel: "オーディオレベル",
  micInstructions: "マイクに向かって話してオーディオレベルを確認してください",
  micInstructionsStart: "'テストを開始'をクリックして始めてください",
  startMicTest: "マイクテストを開始",
  stopTest: "テストを停止",
  nextSpeakerTest: "次：スピーカーテスト",
  
  // マウステスト
  mouseTestTitle2: "マウステスト",
  mouseTestDesc2: "下のテストエリアでマウスを動かしてボタンをクリックしてください。",
  testArea: "テストエリア",
  moveClickScroll: "移動、クリック、スクロール",
  tryMouseButtons: "左、右、中ボタンを試してください",
  position: "位置：",
  buttonStatus: "ボタンの状態",
  leftButton: "左",
  wheelButton: "ホイール",
  middleButton: "中",
  rightButton: "右",
  sideButton1: "サイド1",
  sideButton2: "サイド2",
  pressed: "押下",
  released: "解放",
  eventHistory: "イベント履歴",
  noMouseEvents: "マウスイベントはまだありません",
  startMovingClicking: "移動とクリックを開始してイベントを確認してください",
  scrollDown2: "下にスクロール",
  scrollUp2: "上にスクロール",
  mouseTip1: "• すべてのマウスボタン（左、右、中）をテストしてください",
  mouseTip2: "• 両方向のスクロールホイールをチェックしてください",
  mouseTip3: "• マウス移動トラッキングを確認してください",
  mouseTip4: "• クリック・アンド・ドラッグ機能をテストしてください",
  mouseTip5: "• カーソルがスムーズに動くことを確認してください",
  
  // スピーカーテスト
  speakerTestTitle2: "スピーカーテスト",
  speakerTestDesc2: "会議中に音声を聞くことができるように、スピーカーまたはヘッドフォンをテストします。",
  volume: "音量：",
  testAudioPlayback: "オーディオ再生テスト",
  testAudioDesc: "下のボタンをクリックしてテストトーンを再生してください。スピーカーまたはヘッドフォンから明確な音が聞こえるはずです。",
  playingTestSound: "テスト音を再生中...",
  playTestSound2: "テスト音を再生",
  playingTone: "🔊 テストトーンを再生中 (440 Hz)",
  troubleshootingTips: "💡 トラブルシューティングのコツ：",
  speakerTip1: "• スピーカー/ヘッドフォンが接続されているかチェックしてください",
  speakerTip2: "• 必要に応じてシステム音量を調整してください",
  speakerTip3: "• 異なるオーディオ出力デバイスを試してください",
  speakerTip4: "• オーディオドライバーが最新であることを確認してください",
  backMicrophone: "戻る：マイク",
  nextCameraTest: "次：カメラテスト",
  
  // ヘッドフォン&スピーカーテスト
  headphonesTestTitle: "オンラインヘッドフォン&スピーカーテスト",
  headphonesTestDesc: "最適なリスニング体験のための包括的なオーディオテスト",
  outputDeviceSelector: "出力デバイスを選択",
  noOutputDevices: "オーディオ出力デバイスが見つかりません",
  leftRightChannelTest: "左右チャンネルテスト",
  leftRightChannelDesc: "チャンネル逆転や片側オーディオ問題を迅速に診断",
  playLeft: "▶ 左チャンネルを再生",
  playRight: "▶ 右チャンネルを再生",
  playingLeft: "左を再生中...",
  playingRight: "右を再生中...",
  frequencyResponseTest: "周波数応答テスト",
  frequencyResponseDesc: "ヘッドフォンの低音と高音のパフォーマンスを理解する",
  startSweep: "スイープを開始",
  sweepInProgress: "スイープ進行中",
  frequencyTestTip: "どの周波数で音が弱くなったり消えたりするかに注意してください",
  dynamicRangeTest: "ダイナミックレンジテスト",
  dynamicRangeDesc: "ヘッドフォンの細部再生能力をテスト",
  dynamicRangeTestTip: "静かな背景の詳細と大きな前景音の両方を聞くことができますか？",
  stereoImagingTest: "ステレオイメージング&ポジショニングテスト",
  stereoImagingDesc: "没入型3Dオーディオポジショニングを体験",
  play3dAudio: "3Dオーディオを再生",
  audioPlaying3d: "3Dオーディオ再生中",
  stereoImagingTestTip: "音があなたの頭の周りの3D空間で動くのを聞いてください",
  stopAllAudio: "すべてのオーディオを停止",
  testInProgress: "テスト進行中",
  testNotStarted: "テスト未開始",
  testFailed: "テスト失敗",
  testSkipped: "テストスキップ",
  step: "ステップ",
  of: "の",
  skip: "このステップをスキップ",
  confirmSkipTest: "テストスキップを確認",
  skipTestWarning: "このテストをスキップすると、デバイスの完全な状態評価を取得できません。本当にスキップしますか？",
  confirmSkip: "スキップを確認",
  cancel: "キャンセル",
  canSkipThisTest: "このテストをスキップできます",
  stopPlaying: "再生停止",
  audioError: "オーディオエラー",
  stopSweep: "スイープ停止",
  stopTestButton: "テスト停止",
  startTestButton: "テスト開始",
  testInstructions: "🎵 テスト説明",
  testInstructionItem1: "• スイープ中にどの周波数範囲で音が弱くなったり消えたりするかに注意してください",
  testInstructionItem2: "• 高品質なヘッドフォンは全周波数範囲でバランスの取れた性能を維持する必要があります",
  testInstructionItem3: "• 低音が強すぎると中音域をマスクし、高音が明るすぎると刺激的になる可能性があります",
  testInstructionItem4: "• 正確な結果を得るために静かな環境でのテストをお勧めします",
  testInstructionItem5: "• リアルタイム周波数表示とスペクトラムが現在のテスト周波数を直感的に理解するのに役立ちます",
  dynamicRangeTestInProgress: "ダイナミックレンジテスト中",
  quietSound: "静か",
  loudSound: "大きい",
  bassDetail: "低音の詳細",
  backgroundEffects: "背景効果",
  midrangeLayer: "中音域レイヤー",
  voiceDialogue: "音声対話",
  trebleImpact: "高音インパクト",
  explosionEffects: "爆発効果",
  frontDirection: "前",
  backDirection: "後",
  leftDirection: "左",
  rightDirection: "右",
  leftChannel: "左チャンネル",
  rightChannel: "右チャンネル",
  center: "中央",
  ultraLowBass: "超低音",
  lowBass: "低音",
  midLowBass: "中低音",
  midrange: "中音域",
  midTreble: "中高音",
  treble: "高音",
  ultraTreble: "超高音",
  bassDrumBass: "バスドラム、ベース",
  voiceBase: "音声基本周波数",
  voiceClarity: "音声の明瞭度",
  detailAiriness: "詳細、空気感",
  testProgress: "テスト進行状況",
  leftChannelTest: "左チャンネルテスト",
  rightChannelTest: "右チャンネルテスト",
  frequencyTest: "周波数テスト",
  dynamicRangeTest: "ダイナミックレンジテスト",
  stereoTest: "ステレオテスト",
  basicTestRequired: "まず左右チャンネルの基本テストを完了してください",
  basicTestCompleted: "基本テスト完了",
  // 要約とレポート
  testResultsSummary: "✅ テスト結果要約",
  microphoneTestResult: "マイクテスト：",
  speakerTestResult: "スピーカーテスト：",
  cameraTestResult: "カメラテスト：",
  testPassed: "合格",
  deviceTestReport: "デバイステストレポート",
  generated: "生成日時：",
  scenario: "シナリオ：",
  browserInfo: "ブラウザ情報：",
  testsCompleted: "完了したテスト：",
  allHardwareTestsComplete: "すべてのハードウェアテストが正常に完了しました。",
  deviceReadyForMeetings: "あなたのデバイスはオンラインミーティングの準備ができています！",
  copyFailed: "コピー失敗",
  copyFailedDesc: "クリップボードにコピーできません。もう一度お試しください。",
  
  // UIコンポーネント
  close: "閉じる",
  previousButton: "前へ",
  nextButton: "次へ",
  more: "もっと",
  previousSlide: "前のスライド",
  nextSlide: "次のスライド",
  morePages: "さらにページ",
  toggleSidebar: "サイドバーを切り替え",
  
  // 404ページ
  pageNotFound: "404",
  oopsPageNotFound: "おっと！ページが見つかりません",
  returnToHome: "ホームに戻る",
  
  // マイクモジュール固有
  microphoneAccess: "マイクアクセス",
  initializingMicrophone: "マイクを初期化中...",
  micAccessDenied: "マイクアクセスが拒否されました",
  micAccessDeniedDesc: "テストにはマイクの許可が必要です。ブラウザ設定でマイクアクセスを許可してください。",
  howToEnableMic: "💡 マイク許可を有効にする方法：",
  micPermissionStep1: "• アドレスバーの左側にあるロックアイコンをクリック",
  micPermissionStep2: "• マイク許可で「許可」を選択",
  micPermissionStep3: "• ページを更新して再試行",
  micPermissionStep4: "• またはブラウザ設定でマイク許可を手動で有効にする",
  retry: "再試行",
  readyToTest: "マイクをテストする準備ができました",
  readyToTestDesc: "オンラインミーティング時にあなたの声が明確に伝わるように、マイクの品質、音量レベル、リアルタイム応答性能をテストします。",
  testContent: "🎯 テスト内容：",
  testContentItem1: "• マイクデバイスの検出と許可取得",
  testContentItem2: "• リアルタイムオーディオキャプチャと音量監視",
  testContentItem3: "• オーディオ品質と明瞭度の評価",
  testContentItem4: "• 環境ノイズの検出と推奨事項",
  testContentItem5: "• デバイス互換性の確認",
  startMicrophoneTest: "マイクテストを開始",
  
  // マイクテストインターフェース
  audioVisualization: "オーディオ可視化",
  realTimeWaveform: "🎵 リアルタイムオーディオ波形 - 話すときに変動が見えます",
  startSpeaking: "話し始めてください...",
  usageTip: "💡 使用のコツ：マイクに向かって話すと、上の波形があなたの声に基づいてリアルタイムで変化します。波形がより活発になるほど、マイクがより良く動作しています。",
  microphoneStatus: "マイクステータス",
  micConnected: "🎤 マイクが接続されました",
  speakIntoMic: "マイクに向かって話してオーディオ入力効果をテストしてください",
  deviceWorking: "デバイスが正常に動作しています",
  volumeDetection: "音量検出",
  lowLevel: "低",
  goodLevel: "良好",
  overload: "オーバーロード！",
  dbLow: "-60dB 低",
  dbOverload: "0dB オーバーロード",
  goodRange: "-25dB ~ -8dB 良好な範囲",
  
  // リアルタイム監視
  realTimeMonitoring: "リアルタイム監視（自分の声を聞く）",
  monitoring: "監視中",
  closed: "閉じる",
  monitoringDesc: "有効にすると、リアルタイムで自分の声を聞くことができ、音量と音質の調整に役立ちます。ヘッドフォンでの使用を推奨します。",
  warningHeadphones: "⚠️ この機能はヘッドフォンでの使用を推奨",
  preventFeedback: "フィードバックを防ぐため、ヘッドフォンを使用するかスピーカー音量を下げてください",
  audioDelay: "オーディオ遅延",
  avoidOverlap: "音の重複を避ける",
  monitoringVolume: "監視音量",
  notAffectRecording: "録音に影響しません",
  speaker: "スピーカー",
  monitoringEnabled: "リアルタイム監視が有効",
  adjustSettings: "上記の設定をリアルタイムで調整できます",
  
  // 録音テスト
  recordingTest: "録音テスト",
  startRecording: "録音開始",
  stopRecording: "録音停止",
  stopPlayback: "再生停止",
  playRecording: "録音を再生",
  recording: "録音中",
  playing: "再生中",
  playbackProgress: "再生進行状況",
  playbackComplete: "再生完了",
  paused: "一時停止",
  recordingInstructions: "💡 使用方法：「録音開始」をクリックし、しばらく話してから停止し、「録音を再生」をクリックして効果を聞いてください。これによりマイクが正常に動作していることを確認できます。",
  advancedTest: "🔧 上級テスト：録音前に上記のオーディオ処理設定を調整し、録音して異なる設定での音質の違いを比較できます。",
  
  // デバイス情報
  deviceInfo: "デバイス情報",
  sampleRate: "サンプルレート：",
  channels: "チャンネル：",
  mono: "モノラル",
  stereo: "ステレオ",
  bitDepth: "ビット深度：",
  
  // 高度な設定
  advancedSettings: "高度な設定",
  whenToAdjust: "🎯 これらの設定をいつ調整する必要がありますか？",
  echoCancellation: "エコーキャンセレーション",
  echoCancellationDesc: "会議中に相手が自分の声のエコーを聞く場合 → この機能を有効にしてみてください",
  noiseSuppression: "ノイズ抑制",
  noiseSuppressionDesc: "背景ノイズが大きすぎて通話品質に影響する場合 → 有効にしてキーボード、ファンなどのノイズを減らします",
  autoGainControl: "自動ゲイン制御",
  autoGainControlDesc: "音声の音量が不安定で、時に大きく時に小さい場合 → 有効にして音量レベルを自動調整します",
  realTimeEffect: "⚡ リアルタイム効果：スイッチを切り替えた後、設定が即座に適用され、録音して前後の効果を比較できます",
  enabled: "有効",
  disabled: "無効",
  preventEcho: "相手が自分の声のエコーを聞くのを防ぎ、スピーカーシナリオに適しています",
  mayEcho: "⚠️ 無効にするとエコーが発生する可能性があります。ヘッドフォンの使用を推奨",
  filterNoise: "キーボード音、ファン音、エアコン音などの背景ノイズを知的にフィルタリング",
  improveQuality: "✅ 有効にすると通話品質を大幅に改善できます",
  autoAdjustVolume: "音量の強度を自動調整し、声が大きすぎたり小さすぎたりして聞く体験に影響するのを防ぎます",
  naturalVariation: "💡 無効にすると、自然な音量変動を聞くことができます",
  
  // テストの提案
  testSuggestions: "💡 テストの提案",
  defaultFirst: "まずデフォルト設定で録音してベースライン参照として使用",
  compareSettings: "異なる設定を一つずつ切り替え、毎回録音して効果の違いを比較",
  quietEnvironment: "静かな環境では「ノイズ抑制」をオフに、騒がしい環境ではオンに",
  speakerEcho: "スピーカー使用時は「エコーキャンセレーション」をオン、ヘッドフォン使用時はオフにできます",
  
  stopTestButton: "テストを停止",
  backToHomeButton: "ホームに戻る",
  nextSpeakerTestButton: "次：スピーカーテスト",
  
  // ダブルクリック検出機能
  startDoubleClickDetection: "ダブルクリック検出を開始",
  stopMonitoring: "監視を停止",
  doubleClickDetectionInProgress: "ダブルクリック検出中",
  duration: "継続時間:",
  detectedSevereHardwareFailure: "深刻なハードウェア障害を検出",
  mouseMayHaveIssues: "マウスに問題がある可能性があります",
  sporadicIssuesContinueMonitoring: "散発的な問題、監視を継続",
  mouseWorkingNormally: "マウスは正常に動作しています",
  detected: "検出",
  issues: "個の問題",
  performSingleClickTest: "シングルクリックテストを実行",
  tryNormalSingleClickOperations: "通常のシングルクリック操作を試してください",
  systemWillAutoDetectDoubleClickIssues: "システムが予期しないダブルクリック問題を自動的に検出します",
  doubleClickIssueDetection: "ダブルクリック問題検出",
  noDoubleClickIssuesDetected: "ダブルクリック問題は検出されませんでした",
  continueWithSingleClickTesting: "シングルクリックテストを続行",
  severeHardwareFailureShortInterval: "深刻なハードウェア障害 - 極短間隔ダブルクリック",
  possibleHardwareFailureUnexpectedDoubleClick: "ハードウェア障害の可能性 - 予期しないダブルクリック",
  potentialIssueFastDoubleClick: "潜在的な問題 - 高速ダブルクリック",
  interval: "間隔:",
  normal: "正常:",
  doubleClickDetectionInstructions: "ダブルクリック検出の説明",
  doubleClickTip1: "通常のシングルクリック操作を行うと、システムが予期しないダブルクリックを自動的に検出します",
  doubleClickTip2: "赤色警告：間隔<20ms、深刻なハードウェア障害",
  doubleClickTip3: "黄色警告：間隔<50ms、ハードウェア障害の可能性",
  doubleClickTip4: "青色通知：間隔は短いですが正常範囲内",
  doubleClickTip5: "上記のタイマーは問題の重症度診断に役立つミリ秒を表示します",
  doubleClickTip6: "検出完了後、通常のシングルクリックテストでマウス機能を確認できます",
  
  // TestWorkflowPage 専用翻訳キー
  networkQualityTest: "ネットワーク品質テスト",
  notTested: "未テスト",
  failed: "失敗",
  failureReason: "失敗理由",
  onlineMeetingDeviceReport: "オンラインミーティングデバイステストレポート",
  generatedTime: "生成時間",
  testScenario: "テストシナリオ",
  onlineMeetingScenario: "オンラインミーティングシナリオ",
  unknownScenario: "不明なシナリオ",
  testCompletionStatus: "テスト完了状況",
  testCompletionCount: "テスト完了",
  testResultsTitle: "テスト結果",
  allHardwareTestsPassed: "すべてのハードウェアテストが合格しました！デバイスはオンラインミーティングの準備ができています。",
  testsPassed: "テスト合格",
  partialTestsCompleted: "一部のテストが未完了です。すべてのテスト項目を完了することをお勧めします。",
  checkFailedTests: "失敗したテスト項目を確認することをお勧めします。",
  suggestions: "提案",
  suggestion1: "1. 安定したネットワーク接続を確保してください",
  suggestion2: "2. カメラを目の高さに配置してください",
  suggestion3: "3. 良好な照明条件を維持してください",
  suggestion4: "4. より良いオーディオ体験のためにヘッドフォンを使用してください",
  retestFailedDevices: "5. 失敗したデバイスについては、再テストまたはハードウェア接続の確認をお勧めします",

  testResults: "テスト結果",
  testsCompletedCount: "{completed}/{total} テスト完了",
  meetingDeviceTestReport: "ミーティングデバイステストレポート",
  reason: "理由",
  allDevicesReady: "すべてのデバイスが正常に動作しており、ミーティングを開始する準備ができています！",
  someTestsFailed: "一部のテストが失敗しました。関連デバイスを確認することをお勧めします。",
  completeAllTests: "包括的なデバイス状態評価のためにすべてのテストを完了してください。",
  
  // ネットワークテスト機能
  networkTestDesc: "ミーティング中の安定性を確保するためにネットワーク接続品質をテストします",
  networkQualityTestDesc: "ミーティング中の安定性を確保するためにネットワーク接続品質をテストします",
  testProgress: "テスト進行状況",
  testingNetworkLatency: "ネットワーク遅延、ダウンロードおよびアップロード速度をテスト中...",
  networkQualityAssessment: "ネットワーク品質評価",
  excellent: "優秀",
  good: "良好",
  fair: "普通",
  poor: "不良",
  latency: "遅延",
  download: "ダウンロード",
  upload: "アップロード",
  jitter: "ジッター",
  recommendations: "推奨事項",
  networkExcellentDesc: "優秀なネットワーク品質です！HD ビデオ通話とスムーズな画面共有をお楽しみいただけます。",
  networkGoodDesc: "良好なネットワーク品質で、ビデオ会議に適しています。他の帯域幅を消費するアプリを閉じることを検討してください。",
  networkFairDesc: "普通のネットワーク品質です。音声の安定性を確保するために、ビデオ品質を下げるかビデオをオフにすることを検討してください。",
  networkPoorDesc: "不良なネットワーク品質です。ネットワーク接続を確認するか、ネットワーク管理者に連絡することをお勧めします。",
  serverIP: "サーバーIP",
  timezone: "タイムゾーン",
  userLocation: "あなたの位置",
  serverLocation: "テストサーバーの位置",
  distance: "距離",
  approximateDistance: "おおよその距離",
  gpsLocation: "GPS位置情報",
  ipLocation: "IP位置情報",
  timezoneLocation: "タイムゾーン推定",
  unknownLocation: "不明なソース",
  loadingLocation: "位置情報を取得中...",
  server: "サーバー",
  startNetworkTest: "ネットワークテスト開始",
  retestNetwork: "ネットワーク再テスト",
  networkOptimizationTips: "ネットワーク最適化のヒント",
  networkTip1: "• より安定したネットワークのために WiFi ではなく有線接続を使用してください",
  networkTip2: "• 他の帯域幅を消費するアプリケーションを閉じてください",
  networkTip3: "• ルーターが近くにあり、信号が良好であることを確認してください",
  networkTip4: "• ネットワークのピーク時間帯に重要なミーティングを避けてください",
  testRegion: "テスト地域",
  nextMicrophoneTest: "次へ：マイクテスト",
  
  // 拡張スピーカーテスト
  enhancedSpeakerTest: "拡張スピーカーテスト",
  comprehensiveAudioTest: "オーディオ出力デバイスの包括的テスト",
  volumeControl: "ボリューム制御",
  stereoTest: "ステレオテスト",
  leftChannel: "左チャンネル",
  rightChannel: "右チャンネル",
  bothChannels: "両チャンネル",
  testLeftRightChannels: "左右のチャンネルが正常に動作するかテストします",
  confirmStereoTestPassed: "ステレオテストが合格したことを確認してください",
  frequencySweep: "周波数スイープ",
  sweeping: "スイープ中...",
  testTone1kHz: "1kHz テストトーン",
  testSpeakerFrequencyRange: "スピーカーの周波数応答範囲をテストします",
  confirmFrequencyTestPassed: "周波数テストが合格したことを確認してください",
  speakerTestComplete: "✅ スピーカーテスト完了",
  allAudioTestsPassed: "すべてのオーディオテストが合格しました。スピーカーが正常に動作しています！",
  audioOptimizationTips: "オーディオ最適化のヒント",
  audioTip1: "• ヘッドフォンを使用するとより良いオーディオ体験を提供できます",
  audioTip2: "• システムボリュームを適切なレベルに調整してください",
  audioTip3: "• オーディオドライバーが最新であることを確認してください",
  audioTip4: "• 最高の効果のために静かな環境でミーティングを行ってください",

  // 拡張カメラテスト
  enhancedCameraTest: "拡張カメラテスト",
  comprehensiveVideoTest: "ビデオ入力デバイスの包括的テスト",
  cameraPermissionDeniedDesc: "テストにはカメラ許可が必要です。ブラウザ設定でカメラアクセスを許可してください。",
  howToEnableCamera: "💡 カメラ許可を有効にする方法:",
  cameraPermissionStep1: "• アドレスバーの左側にあるロックアイコンをクリックしてください",
  cameraPermissionStep2: "• カメラ許可で「許可」を選択してください",
  cameraPermissionStep3: "• ページを更新して再試行してください",
  cameraPermissionStep4: "• またはブラウザ設定でカメラ許可を手動で有効にしてください",
  readyToTestCamera: "カメラテストの準備完了",
  readyToTestCameraDesc: "オンライン会議中に画像が明確に送信されることを確保するため、カメラの品質、解像度、リアルタイムパフォーマンスをテストします。",
  cameraTestContent: "🎯 テスト内容:",
  cameraTestContentItem1: "• カメラデバイスの検出と許可取得",
  cameraTestContentItem2: "• リアルタイムビデオキャプチャと品質監視",
  cameraTestContentItem3: "• ビデオ品質と鮮明度の評価",
  cameraTestContentItem4: "• 照明条件の検出と推奨事項",
  cameraTestContentItem5: "• デバイス互換性の確認",
  cameraStatus: "カメラステータス",
  cameraConnected: "📹 カメラ接続済み",
  lookIntoCameraTest: "ビデオ入力効果をテストするためにカメラを見てください",
  videoQualityAnalysis: "ビデオ品質分析",
  brightness: "明度",
  contrast: "コントラスト",
  sharpness: "シャープネス",
  videoQualityExcellentDesc: "優秀なビデオ品質！カメラ設定は会議使用に最適です。",
  videoQualityGoodDesc: "良好なビデオ品質、ほとんどの会議シナリオに適しています。",
  videoQualityFairDesc: "普通のビデオ品質、照明やカメラ位置の調整を検討してください。",
  videoQualityPoorDesc: "不良なビデオ品質、カメラ設定の確認またはデバイスの交換をお勧めします。",
  videoOptimizationTips: "ビデオ最適化のヒント",
  videoTip1: "• 前方に十分な照明を確保してください",
  videoTip2: "• カメラを目の高さに配置してください",
  videoTip3: "• より鮮明な画像のためにカメラレンズを清拭してください",
  videoTip4: "• 安定したネットワーク接続を維持してください",
  completeTest: "テスト完了",
  
 
  
  // 拡張マイクテスト
  enhancedMicrophoneTest: "拡張マイクテスト",
  comprehensiveMicTest: "オーディオ入力デバイスの包括的テスト",
  audioQualityAnalysis: "オーディオ品質分析",
  signalToNoiseRatio: "信号対雑音比:",
  backgroundNoiseLevel: "背景雑音レベル:",
  distortionLevel: "歪みレベル:",
  echoDetection: "エコー検出:",
  notDetected: "検出されませんでした",
  micExcellentDesc: "優秀なオーディオ品質です！マイク設定はミーティング使用に最適です。",
  micGoodDesc: "良好なオーディオ品質で、ほとんどのミーティングシナリオに適しています。",
  micFairDesc: "普通のオーディオ品質です。マイク位置の調整や背景雑音の軽減を検討してください。",
  micPoorDesc: "不良なオーディオ品質です。マイク設定を確認するか、デバイスを交換することをお勧めします。",
  echoDetectedWarning: "⚠️ エコーが検出されました。ヘッドフォンの使用またはスピーカー音量の調整をお勧めします。",
  micOptimizationTips: "マイク最適化のヒント",
  micTip1: "• マイクを口から15-20cm離してください",
  micTip2: "• 静かな環境でテストしてください",
  micTip3: "• エコー問題を避けるためにヘッドフォンを使用してください",
  micTip4: "• マイク音量を適切なレベルに調整してください",
  
  // テスト失敗理由
  networkNotTested: "ネットワークテストが実行されていません",
  latencyTooHigh: "遅延が高すぎます",
  downloadSpeedTooSlow: "ダウンロード速度が遅すぎます",
  uploadSpeedTooSlow: "アップロード速度が遅すぎます",
  jitterTooHigh: "ネットワークジッターが高すぎます",
  networkQualityPoor: "ネットワーク品質が不良で、ビデオ会議に適していません",
  micPermissionDenied: "マイク許可が拒否されました。ブラウザ設定でマイクアクセスを許可してください",
  micPermissionNotGranted: "マイク許可が付与されていません",
  noMicrophoneDevices: "利用可能なマイクデバイスが検出されませんでした",
  noAudioInput: "マイクからのオーディオ入力がありません。デバイス接続やボリューム設定を確認してください",
  audioQualityIssues: "オーディオ品質の問題",
  signalToNoiseRatioLow: "信号対雑音比が低すぎます",
  backgroundNoiseTooHigh: "背景雑音が高すぎます",
  audioDistortionHigh: "オーディオ歪みが高いです",
  echoDetected: "エコーが検出されました",
  audioQualityPoor: "オーディオ品質が不良です",
  requiredTestsNotCompleted: "必要なテスト項目が完了していません",
  stereoTestNotCompleted: "ステレオテストが完了していません",
  frequencyTestNotCompleted: "周波数応答テストが完了していません",
  cameraPermissionDenied: "カメラ許可が拒否されました。ブラウザ設定でカメラアクセスを許可してください",
  noCameraDevices: "利用可能なカメラデバイスが検出されませんでした",
  cameraNotStarted: "カメラが開始されていません。「カメラテスト開始」ボタンをクリックしてください",
  cameraError: "カメラエラー",
  videoQualityIssues: "ビデオ品質の問題",
  lightingTooDark: "照明が暗すぎます",
  lightingTooBright: "照明が明るすぎます",
  contrastTooLow: "コントラストが低すぎます",
  imageBlurry: "画像がぼやけています",
  videoQualityPoor: "ビデオ品質が不良です",
  
  // ミーティングデバイステスト完了
  meetingDeviceTestComplete: "ミーティングデバイステスト完了",
  
  // Gaming Setup Check
  gamingSetupCheckTitle: "ゲーミングセットアップチェック",
  gamingScenario: "ゲーミングシナリオ",
  gamingDeviceTestReport: "ゲーミングデバイステストレポート",
  gamingDeviceTestComplete: "ゲーミングデバイステスト完了",
  allGamingTestsPassed: "すべてのゲーミングテストが合格しました。セットアップの準備が整いました！",
  gamingNetworkTestDesc: "最適なゲーミングパフォーマンスのためにネットワーク接続品質をテストします",
  gamingKeyboardTestDesc: "ゲーミング応答性と機能性のためにキーボードをテストします",
  gamingMouseTestDesc: "ゲーミング用のマウス精度と応答性をテストします",
  gamingAudioTestDesc: "没入型ゲーミング体験のためにオーディオ出力をテストします",
  keyboardResponseTime: "キーボード応答時間",
  mouseAccuracy: "マウス精度",
  audioLatency: "オーディオレイテンシー",
  peripheralPerformance: "周辺機器パフォーマンス",
  allPeripheralsReady: "すべての周辺機器がゲーミング準備完了です！",
  somePeripheralsFailed: "一部の周辺機器に注意が必要です。上記の失敗したテストを確認してください。",
  completeAllGamingTests: "ゲーミングセットアップレポートを取得するには、すべてのテストを完了してください。",
  retestFailedPeripherals: "より良いパフォーマンスのために失敗した周辺機器を再テストすることを検討してください。",
  optimizeGamingSetup: "ゲーミングセットアップ最適化のヒント",
  gamingTip1: "• 最低レイテンシーのために有線周辺機器を使用する",
  gamingTip2: "• マウス感度を快適なレベルに保つ",
  gamingTip3: "• 適切な応答のためにすべてのキーボードキーをテストする",
  gamingTip4: "• オーディオに遅延や歪みがないことを確認する",
  gamingTip5: "• オンラインゲーミングのためにネットワーク安定性をチェックする",

  // SEO相関翻訳
  siteDescription: "カメラ、マイク、スピーカー、キーボード、マウス、ネットワーク品質のプロフェッショナルデバイステストプラットフォーム。オンライン会議とゲーミングセットアップに最適。",
  siteKeywords: "デバイステスト,カメラテスト,マイクテスト,スピーカーテスト,キーボードテスト,マウステスト,ネットワークテスト,オンライン会議,ハードウェアチェック,ゲーミングセットアップ",
  
  // ページSEO説明
  homePageDescription: "オンラインでデバイスをテスト - カメラ、マイク、スピーカー、キーボード、マウス、ネットワーク。オンライン会議とゲーミング用の無料ハードウェア互換性チェック。",
  toolsPageDescription: "プロフェッショナルデバイステストツールの包括的コレクション。カメラ、マイク、スピーカー、キーボード、マウス、ネットワーク品質を無料でオンラインテスト。",
  cameraTestDescription: "カメラの品質、解像度、パフォーマンスをオンラインでテスト。ビデオ通話とストリーミング用のカメラ互換性をチェック。",
  microphoneTestDescription: "マイクの音質、ノイズレベル、クリア度をテスト。オンライン会議とゲーミングで完璧な音声を確保。",
  headphonesTestDescription: "ヘッドフォンとスピーカーの音質、ステレオバランス、ボリュームレベルをテスト。オーディオ互換性をチェック。",
  keyboardTestDescription: "すべてのキーボードキー機能、応答時間、キーマッピングをテスト。タイピングとゲーミングの最適なパフォーマンスを確保。",
  mouseTestDescription: "マウスの精度、クリック応答性、スクロール機能、精密性をテスト。生産性とゲーミングを最適化。",
  networkTestDescription: "インターネット速度、レイテンシ、接続安定性をテスト。オンライン会議とゲーミングのネットワーク品質をチェック。",
  meetingTestDescription: "オンライン会議用の完全なデバイス互換性チェック。カメラ、マイク、スピーカー、ネットワーク品質をテスト。",
  gamingTestDescription: "完全なゲーミングセットアップチェック。キーボード、マウス、ヘッドフォン、マイク、ネットワークパフォーマンスをテストして最適なゲーミング体験を実現。",

  // ライブストリーミング＆コンテンツ作成シナリオ
  streamingScenario: "ライブストリーミング＆コンテンツ作成",
  streamingScenarioDesc: "ストリーマー、コンテンツクリエイター、オンライン教育者向けのプロフェッショナルグレードテスト",
  streamingSetupCheckTitle: "ライブストリーミング＆コンテンツ作成セットアップ",
  streamingDeviceTestReport: "ライブストリーミングデバイステストレポート",
  streamingDeviceTestComplete: "ストリーミングデバイステスト完了",
  allStreamingTestsPassed: "すべてのストリーミング機器テストに合格しました！プロフェッショナルなコンテンツ作成の準備が整いました。",
  streamingOptimizationTips: "ストリーミング最適化のヒント",
  streamingTip1: "• 安定した高速インターネット接続を確保（1080pストリーミングには最低5 Mbpsのアップロード速度が必要）",
  streamingTip2: "• より良い音質のために専用マイクを使用",
  streamingTip3: "• プロフェッショナルな外観のために照明とカメラの位置を最適化",
  streamingTip4: "• 音声レベルをテストし、背景ノイズを除去",
  streamingTip5: "• 音質をモニターするためにヘッドフォンの使用を検討",
  streamingTestDescription: "完全なストリーミングセットアップチェック。高品質なコンテンツ作成のためにカメラ、マイク、ヘッドフォン、ネットワークパフォーマンスをテスト。",

  // デバイス診断シナリオ
  diagnosticScenario: "完全デバイス診断",
  diagnosticScenarioDesc: "接続されたすべてのデバイスの包括的なハードウェアスキャンとトラブルシューティング",
  diagnosticSetupCheckTitle: "完全デバイス診断",
  diagnosticDeviceTestReport: "完全デバイス診断レポート",
  diagnosticDeviceTestComplete: "デバイス診断完了",
  allDiagnosticTestsPassed: "すべてのハードウェアコンポーネントが正常に機能しています。問題は検出されませんでした。",
  diagnosticTroubleshootingTips: "トラブルシューティング推奨事項",
  diagnosticTip1: "• デバイスドライバーをチェックし、必要に応じて更新",
  diagnosticTip2: "• すべてのハードウェア接続が安全であることを確認",
  diagnosticTip3: "• 問題を特定するために他のアプリケーションでデバイスをテスト",
  diagnosticTip4: "• 問題が続く場合はデバイスとブラウザを再起動",
  diagnosticTip5: "• 持続的なハードウェア障害については技術サポートに連絡",
  diagnosticTestDescription: "完全なハードウェア診断スキャン。包括的なシステム評価のためにカメラ、マイク、ヘッドフォン、キーボード、マウス、ネットワークを含むすべてのデバイスをテスト。",
  
  // Enhanced network test features (missing properties)
  testingLatency: "レイテンシーテスト中",
  testingDownload: "ダウンロード速度テスト中",
  testingUpload: "アップロード速度テスト中", 
  testingPacketLoss: "パケットロステスト中",
  testCompleted: "テスト完了",
  preparingTest: "テスト準備中",
  aimScores: "AIM使用シナリオスコア",
  gaming: "ゲーミング",
  streaming: "ストリーミング",
  realTimeCommunication: "リアルタイム通信",
  loadedMetrics: "読み込まれたネットワークメトリクス",
  loadedLatency: "読み込まれたレイテンシー",
  loadedJitter: "読み込まれたジッター",
  
  // Missing microphone test properties
  micPermissionDeniedTitle: "マイクアクセスが必要です",
  micPermissionDeniedMessage: "このテストを実行するにはマイクアクセスが必要です。",
  enableMicPermissionInstructions: "マイク権限を有効にする方法：",
  enableMicStep1: "1. ブラウザのアドレスバーのマイクアイコンをクリック",
  enableMicStep2: "2. 「常に許可」を選択",
  enableMicStep3: "3. ページを更新",
  retryTest: "再試行",
  waitingConnection: "接続待機中",
  startTestingButton: "テスト開始",
  waitingTesting: "テスト待機中",
  

  // マイクテストモジュール新規翻訳キー
  
  deviceReady: "デバイステスト準備完了",
  applyingNewSettings: "新しい設定を適用中",
  settingsApplied: "設定が適用されました",
  applySettingsError: "新しい設定の適用エラー、再試行してください",
  realTimeAudioDelay: "リアルタイムオーディオ遅延",
  seconds: "秒",
  preventSoundOverlap: "音の重複を防ぐ",
  adjustAnytime: "上記の設定はいつでも調整できます",
  playingInProgress: "再生中",
  recordingCompleted: "録音完了",
  playbackStopped: "再生停止",
  microphoneInformation: "マイク情報",
  gettingDeviceInfo: "デバイス情報を取得中...",
  pleaseTurnOnTest: "デバイス情報を取得するには、まずテストを開始してください",
  whyAdjustSettings: "🎯 これらの設定をいつ調整する必要がありますか？",
  echoWhenUsingSpeaker: "会議中に他の人が自分の声のエコーを聞く場合 → この機能を有効にしてみてください",
  noisyBackground: "背景雑音が通話品質に影響している場合 → キーボード、ファンの音などを減らすために有効にしてください",
  unstableVolume: "音量レベルが不安定な場合 → 自動的に音量を調整するために有効にしてください",
  settingsApplyImmediately: "⚡ リアルタイム効果: 設定は切り替え時に即座に適用されます。録音して効果を比較できます",
  toggleOn: "有効",
  toggleOff: "無効",
  useHeadphonesToPreventEcho: "⚠️ 無効にするとエコーが発生する可能性があります。ヘッドフォンの使用をお勧めします",
  improveCallQuality: "✅ 有効にすると通話品質が大幅に向上します",
  hearNaturalVolumeChanges: "💡 無効にすると自然な音量変化を聞くことができます",
  testSuggestionsTitle: "💡 テストの提案",
  defaultSettingsFirst: "まず参考として既定設定で録音してください",
  compareEachSetting: "異なる設定を一つずつ切り替え、毎回録音して効果を比較してください",
  noiseSuppressionTip: "静かな環境では「ノイズ抑制」をオフに、騒がしい環境ではオンにしてください",
  echoCancellationTip: "スピーカー使用時は「エコーキャンセレーション」をオンに、ヘッドフォン使用時はオフにできます",
  returnButton: "戻る",

  // エラーメッセージ - カメラ
  cameraAccessDenied: "カメラアクセスが拒否されました。カメラの許可を許可してください。",
  cameraNotFound: "カメラが見つかりません。カメラを接続してください。",
  cameraAccessFailed: "カメラへのアクセスに失敗しました",

  // エラーメッセージ - マイク
  microphoneAccessDenied: "マイクの許可が拒否されました。マイクアクセスを許可して再試行してください。",
  microphoneAccessFailed: "マイクへのアクセスに失敗しました。デバイスを確認して再試行してください。",
  recordingStartFailed: "録音の開始に失敗しました",

  // SEO検証メッセージ
  seoTitleMissing: "ページタイトルがありません",
  seoTitleTooLong: "ページタイトルが長すぎます（60文字以内を推奨）",
  seoTitleTooShort: "ページタイトルが短すぎます（10文字以上を推奨）",
  seoDescriptionMissing: "ページ説明がありません",
  seoDescriptionTooLong: "ページ説明が長すぎます（160文字以内を推奨）",
  seoDescriptionTooShort: "ページ説明が短すぎます（50文字以上を推奨）",

  // デフォルトSEO設定
  defaultSEOTitle: "デバイステストプラットフォーム",
  defaultSEODescription: "カメラ、マイク、スピーカー、キーボード、マウス、ネットワーク品質のプロフェッショナルデバイステストプラットフォーム。",

  // コンソールメッセージ
  setLanguageWarning: "LanguageProviderからsetLanguageが呼び出されました。代わりにuseLanguageNavigationフックを使用してください。",
  invalidLanguageCode: "無効な言語コード",

  // シナリオページラベル
  includedTests: "含まれるテスト：",
  completeHardwareCheck: "完全なハードウェア互換性チェック",
  needIndividualTesting: "個別デバイステストが必要ですか？",
  individualTestingDesc: "特定のハードウェアコンポーネントのみをテストする必要がある場合は、個別のテストツールに直接アクセスできます。",
  browseIndividualTools: "個別ツールを参照",
  selectScenarioDesc: "ニーズに最適なテストシナリオを選択してください。各シナリオには、特定の用途向けに設計された厳選されたテストセットが含まれています。",

  // シナリオ時間
  duration5to8: "5-8分",
  duration8to12: "8-12分",
  duration6to10: "6-10分",
  duration10to15: "10-15分",

  // 難易度レベル
  difficultyBeginner: "初級",
  difficultyAdvanced: "上級",
  difficultyIntermediate: "中級",
  difficultyComprehensive: "包括的",

  // 使用説明
  usageMeeting: "ビデオ通話、リモートワーク、オンライン会議に最適",
  usageGaming: "ゲーミングパフォーマンスと競技プレイ向けに最適化",
  usageStreaming: "高品質ストリーミングとコンテンツ作成向けに最適化",
  usageDiagnostic: "トラブルシューティングとデバイス検証のための完全なシステムチェック",

  // キーボードテストモジュール
  keyboardTestInstructions: "テスト手順",
  keyboardTestInstruction1: "• テスト開始をクリック後、よく使用するゲーミングキーを押してください",
  keyboardTestInstruction2: "• 推奨テスト：WASD、スペース、Shift、Ctrl、Q、E など",
  keyboardTestInstruction3: "• キー応答時間とカバレッジを測定します",
  keyboardTestInstruction4: "• 完了するには最低10回のキー入力をテストしてください",
  startKeyboardTest: "キーボードテスト開始",
  keyboardTestingActive: "キーボードをテスト中...",
  keyboardTestStopped: "キーボードテスト停止",
  keyPressCount: "キー入力回数",
  averageLatency: "平均レイテンシ",
  keyCoverage: "キーカバレッジ",
  keyTestStatus: "キーテストステータス",
  recentKeys: "最近のキー (レイテンシ)",
  stopKeyboardTest: "テスト停止",
  continueKeyboardTest: "テスト継続",
  resetKeyboardTest: "リセット",
  keyboardTestComplete: "キーボードテスト完了！",
  keyboardTestCompleteDesc: "さらにキーをテストするか、次のステップに進むことができます",
  insufficientKeyCoverage: "キーカバレッジ不足 - より多くのゲーミングキーをテストしてください",
  highInputLatency: "高い入力レイテンシが検出されました",
  notEnoughKeyPresses: "キー入力が不足 - より多くのキーをテストしてください",

  // キーボードレイテンシテスト (KeyboardTest.tsx)
  keyboardLatencyTest: "キーボードレイテンシテスト",
  testKeyboardResponseTime: "キーボードの応答時間と入力遅延をテストします",
  latencyShortestLatency: "最短レイテンシ",
  latencyAverageLatency: "平均レイテンシ",
  latencyScanRate: "スキャンレート",
  latencyConnection: "接続",
  latencyNoData: "データなし",
  latencyPresses: "回入力",
  latencyMaxLatency: "最大",
  latencyHistory: "レイテンシ履歴",
  latencyTestInstructions: "レイテンシテスト手順",
  latencyInstruction1: "• キーを押して離すことで応答時間を測定します",
  latencyInstruction2: "• レイテンシが短い = パフォーマンスが良い",
  latencyInstruction3: "• スキャンレートは最短キー押下時間から計算されます",
  latencyInstruction4: "• ゲーミングキーボードは通常 <5ms のレイテンシ",
  latencyInstruction5: "• 一般的なキーボードは平均 10-15ms のレイテンシ",
  latencyPerformanceAssessment: "パフォーマンス評価",
  latencyOverallRating: "総合評価",
  latencyRecommendation: "推奨事項",
  latencyKeyCharacteristics: "主要特性",
  latencyEstimatedPollingRate: "推定ポーリングレート：~1000Hz",
  latencyConnectionType: "接続タイプ",
  latencyResponseConsistency: "応答一貫性",
  latencyVeryConsistent: "非常に一貫している",
  latencyVariable: "変動あり",
  latencyExcellent: "優秀",
  latencyVeryGood: "とても良い",
  latencyGood: "良い",
  latencyAverage: "平均",
  latencyPoor: "悪い",
  latencyExcellentForGaming: "ゲームと専門用途に最適",
  latencyGoodForGeneral: "一般用途に適しており、カジュアルゲームに適している",
  latencyConsiderUpgrading: "より良いパフォーマンスのためにアップグレードを検討してください",
  latencyUsbEstimated: "USB（推定）",
  latencyUnknown: "不明",

  // プライバシーポリシーとクッキーポリシー
  privacyPolicy: "プライバシーポリシー",
  cookiePolicy: "クッキーポリシー",
  privacyPolicyTitle: "プライバシーポリシー",
  cookiePolicyTitle: "クッキーポリシー",
  lastUpdated: "最終更新",
  effectiveDate: "発効日",
  contactUs: "お問い合わせ",

  // クッキー同意
  cookieConsentTitle: "クッキー設定",
  cookieConsentDescription: "私たちはクッキーを使用してブラウジング体験を向上させ、サイトトラフィックを分析し、コンテンツをパーソナライズします。すべてのクッキーを受け入れるか、設定をカスタマイズすることができます。",
  cookieSettingsTitle: "クッキー設定",
  cookieSettingsDescription: "私たちは異なる種類のクッキーを使用してあなたの体験を最適化します。各カテゴリを有効または無効にすることができますが、一部の機能が影響を受ける可能性があることにご注意ください。",
  acceptAll: "すべて受け入れる",
  rejectAll: "すべて拒否",
  customize: "カスタマイズ",
  saveSettings: "設定を保存",
  required: "必須",

  // クッキーカテゴリ
  cookieNecessaryTitle: "必要なクッキー",
  cookieNecessaryDesc: "これらのクッキーはウェブサイトの適切な機能に不可欠であり、無効にすることはできません。通常、プライバシー設定の設定、ログイン、フォームの記入など、あなたが行った操作に応じてのみ設定されます。",
  cookieAnalyticsTitle: "分析クッキー",
  cookieAnalyticsDesc: "これらのクッキーは、訪問者が私たちのウェブサイトとどのように相互作用するかを理解するのに役立ち、匿名情報を収集して報告します。これにより、ウェブサイトのパフォーマンスとユーザーエクスペリエンスの向上に役立ちます。",
  cookieMarketingTitle: "マーケティングクッキー",
  cookieMarketingDesc: "これらのクッキーは、関連性のあるパーソナライズされた広告を表示する目的で、ウェブサイト間で訪問者を追跡するために使用されます。",
  cookiePreferencesTitle: "設定クッキー",
  cookiePreferencesDesc: "これらのクッキーにより、ウェブサイトはあなたが行った選択（ユーザー名、言語、地域など）を記憶し、強化されたよりパーソナライズされた機能を提供できます。",

  // プライバシーポリシー内容
  privacyIntroduction: "Setup Check（私たち、当社、または会社）は、あなたのプライバシーを保護することをお約束します。このプライバシーポリシーは、あなたが私たちのデバイステストサービスを使用する際に、私たちがどのように情報を収集、使用、保護するかを説明します。",
  dataCollectionTitle: "収集する情報",
  dataCollectionContent: "私たちは以下の種類の情報を収集します：\n\n• **デバイス情報**：ブラウザタイプ、オペレーティングシステム、デバイスモデル、画面解像度\n• **使用データ**：テスト結果やインタラクションパターンを含む、サービスの使用方法\n• **技術データ**：IPアドレス（匿名化）、アクセス時間、ページビュー\n• **クッキーと類似技術**：ユーザーエクスペリエンスの向上とウェブサイトパフォーマンスの分析に使用\n\n自発的に提供されない限り、名前、メールアドレス、電話番号などの個人識別情報は収集しません。",
  dataUsageTitle: "情報の使用方法",
  dataUsageContent: "収集した情報は以下の目的で使用します：\n\n• **サービス提供**：デバイステストの実行と結果の表示\n• **サービス改善**：ユーザーエクスペリエンスを最適化するための使用パターンの分析\n• **技術サポート**：技術的問題の診断と解決\n• **セキュリティ**：悪用や悪意のある活動の検出と防止\n• **コンプライアンス**：法的義務と規制要件の遵守",
  thirdPartyServicesTitle: "第三者サービス",
  thirdPartyServicesContent: "私たちは以下の第三者サービスを使用しています：\n\n• **Google Analytics 4**：ウェブサイト分析とパフォーマンス監視のため。Googleは匿名の使用データを収集する場合があります。クッキー設定を通じてこのデータ収集を制御できます。\n• **コンテンツ配信ネットワーク（CDN）**：ウェブサイトの読み込み速度向上のため\n\nこれらのサービスには独自のプライバシーポリシーがあり、データ処理方法を理解するためにそれらのポリシーを確認することをお勧めします。",
  userRightsTitle: "あなたの権利",
  userRightsContent: "適用されるデータ保護法の下で、あなたには以下の権利があります：\n\n• **アクセス権**：私たちが保有するあなたに関する情報へのアクセスを要求\n• **訂正権**：不正確な情報の訂正を要求\n• **削除権**：個人情報の削除を要求\n• **制限権**：情報の処理の制限を要求\n• **ポータビリティ権**：構造化された形式でデータの受け取りを要求\n• **異議権**：情報の処理に異議を申し立て\n\nこれらの権利を行使するには、以下の連絡先情報を使用してお問い合わせください。",
  dataSecurityTitle: "データセキュリティ",
  dataSecurityContent: "私たちはあなたの情報を保護するために適切な技術的および組織的措置を実施しています：\n\n• **暗号化**：すべてのデータ送信でHTTPS暗号化を使用\n• **アクセス制御**：認可された担当者のみにデータアクセスを制限\n• **定期監査**：セキュリティ措置の定期的な見直し\n• **データ最小化**：必要な情報のみを収集\n• **匿名化**：可能な場合はデータを匿名化",
  contactInformationTitle: "連絡先情報",
  contactInformationContent: "このプライバシーポリシーについてご質問がある場合、または権利を行使する必要がある場合は、お問い合わせください：\n\n• **メール**：<EMAIL>\n• **住所**：[会社住所]\n\n受領後30日以内にご要求にお応えします。",

  // クッキーポリシー内容
  cookieIntroduction: "このクッキーポリシーは、Setup Checkがクッキーと類似技術を使用して、あなたが私たちのウェブサイトを訪問した際にあなたを認識する方法を説明します。これらの技術が何であり、なぜ私たちがそれらを使用するのか、そして私たちの使用を制御するあなたの権利について説明します。",
  whatAreCookiesTitle: "クッキーとは",
  whatAreCookiesContent: "クッキーは、ウェブサイトを訪問した際にコンピュータやモバイルデバイスに配置される小さなデータファイルです。クッキーは、ウェブサイトを機能させる、またはより効率的に機能させるため、および報告情報を提供するために、ウェブサイト所有者によって広く使用されています。\n\nウェブサイト所有者（この場合はSetup Check）によって設定されるクッキーは「ファーストパーティクッキー」と呼ばれます。ウェブサイト所有者以外の当事者によって設定されるクッキーは「サードパーティクッキー」と呼ばれます。サードパーティクッキーにより、ウェブサイト上またはウェブサイトを通じてサードパーティの機能や機能性を提供できます（例：広告、インタラクティブコンテンツ、分析）。",
  cookieTypesTitle: "使用するクッキーの種類",
  cookieTypesContent: "• **必要なクッキー**：これらのクッキーは私たちのウェブサイトの運営に厳密に必要です。ウェブサイトをナビゲートし、ウェブサイトの安全な領域へのアクセスなどの機能を使用することを可能にします。これらのクッキーなしでは、要求されたサービスを提供できません。\n\n• **分析クッキー**：これらのクッキーは、訪問者が私たちのウェブサイトをどのように使用するかについての情報を収集します。例えば、訪問者が最も頻繁に訪問するページや、ウェブページからエラーメッセージを受け取るかどうかなどです。これらのクッキーは訪問者を識別する情報を収集しません。これらのクッキーが収集するすべての情報は集約されており、したがって匿名です。\n\n• **機能クッキー**：これらのクッキーにより、私たちのウェブサイトはあなたが行った選択（ユーザー名、言語、またはあなたがいる地域など）を記憶し、強化されたよりパーソナルな機能を提供できます。\n\n• **マーケティングクッキー**：これらのクッキーは、ウェブサイト間で訪問者を追跡するために使用されます。その目的は、個々のユーザーに関連性があり魅力的な広告を表示することであり、したがって出版社やサードパーティ広告主にとってより価値があります。",
  manageCookiesTitle: "クッキーの管理方法",
  manageCookiesContent: "クッキーはいくつかの方法で管理できます：\n\n• **クッキー設定**：私たちのウェブサイトのクッキー同意バナーを使用して設定を選択\n• **ブラウザ設定**：ほとんどのウェブブラウザでは、ブラウザ設定を通じてクッキーを制御できます\n• **オプトアウトツール**：さまざまなオンラインツールを使用して特定の追跡をオプトアウトできます\n\n特定のクッキーを無効にすると、ウェブサイトの機能とユーザーエクスペリエンスに影響を与える可能性があることにご注意ください。",
  thirdPartyCookiesTitle: "サードパーティクッキー",
  thirdPartyCookiesContent: "私たちはGoogle Analyticsを使用してウェブサイトの使用を分析しています。Google Analyticsはクッキーを使用して、ユーザーがサイトをどのように使用するかを分析するのに役立ちます。クッキーによって生成されるウェブサイトの使用に関する情報（IPアドレスを含む）は、Googleに送信され、米国のサーバーにGoogleによって保存されます。\n\nGoogleは、ウェブサイトの使用を評価し、ウェブサイト運営者向けのウェブサイト活動レポートを編集し、ウェブサイト活動とインターネット使用に関連するその他のサービスを提供する目的でこの情報を使用します。\n\nブラウザで適切な設定を選択することでクッキーの使用を拒否できますが、これを行うとこのウェブサイトの完全な機能を使用できない可能性があることにご注意ください。",

  // お問い合わせ - 追加翻訳
  userFeedback: "ユーザーフィードバック",
  contactEmailTemplate: "問題や提案について詳しく説明してください：\n\n[ここに問題や提案を詳しく記載してください]\n\n技術的な問題の場合は、以下の情報も含めてください：\n- 使用しているデバイスの種類\n- 問題が発生した具体的な手順\n- エラーメッセージ（該当する場合）",
  systemInfo: "システム情報",
  browser: "ブラウザ",
  language: "言語",
  timestamp: "タイムスタンプ",
  contactTitle: "お問い合わせ",
  contactDescription: "問題や提案がございますか？皆様のフィードバックをお待ちしております！メールでお問い合わせいただければ、できるだけ早くご返信いたします。",
  feedbackTypes: "フィードバックの種類",
  bugReport: "バグ報告",
  featureRequest: "機能リクエスト",
  generalInquiry: "一般的なお問い合わせ",
  technicalSupport: "技術サポート",
};