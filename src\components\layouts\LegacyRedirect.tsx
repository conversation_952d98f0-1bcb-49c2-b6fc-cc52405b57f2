import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getPreferredLanguage } from '@/config/languages';

const LegacyRedirect = () => {
    const navigate = useNavigate();
    const location = useLocation();
    
    useEffect(() => {
        const userLang = getPreferredLanguage();
        const newPath = `/${userLang}${location.pathname}${location.search}${location.hash}`;
        navigate(newPath, { replace: true });
    }, [navigate, location]);

    return null; // Or a loading spinner
};

export default LegacyRedirect; 