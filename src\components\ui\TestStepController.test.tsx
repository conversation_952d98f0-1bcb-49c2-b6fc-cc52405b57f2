import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TestStepController } from './TestStepController';
import { NavigationAction, NavigationControl, TestStatus, EnhancedTestResult } from '@/types/testWorkflow';

// Mock useLanguage hook
jest.mock('@/hooks/useLanguage', () => ({
  useLanguage: () => ({
    t: (key: string) => key
  })
}));

describe('TestStepController', () => {
  const mockOnAction = jest.fn();
  
  const defaultNavigationControl: NavigationControl = {
    canGoNext: true,
    canGoBack: true,
    canRetry: false,
    canSkip: false,
    nextButtonText: 'next',
    backButtonText: 'back',
    showRetryButton: false,
    showSkipButton: false
  };

  const defaultTestResult: EnhancedTestResult = {
    status: TestStatus.COMPLETED,
    passed: true,
    timestamp: new Date()
  };

  beforeEach(() => {
    mockOnAction.mockClear();
  });

  it('renders children correctly', () => {
    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('shows progress bar when enabled', () => {
    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
        showProgress={true}
        currentStep={2}
        totalSteps={5}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    expect(screen.getByText('step 2 of 5')).toBeInTheDocument();
  });

  it('displays test status correctly', () => {
    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
        testResult={defaultTestResult}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    expect(screen.getByText('testPassed')).toBeInTheDocument();
  });

  it('handles next button click', () => {
    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
        testResult={defaultTestResult}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    const nextButton = screen.getByText('next');
    fireEvent.click(nextButton);

    expect(mockOnAction).toHaveBeenCalledWith(NavigationAction.NEXT, defaultTestResult);
  });

  it('handles back button click', () => {
    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    const backButton = screen.getByText('back');
    fireEvent.click(backButton);

    expect(mockOnAction).toHaveBeenCalledWith(NavigationAction.BACK);
  });

  it('shows retry button when enabled', () => {
    const navigationControlWithRetry: NavigationControl = {
      ...defaultNavigationControl,
      showRetryButton: true,
      canRetry: true
    };

    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={navigationControlWithRetry}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    const retryButton = screen.getByText('retry');
    expect(retryButton).toBeInTheDocument();
    
    fireEvent.click(retryButton);
    expect(mockOnAction).toHaveBeenCalledWith(NavigationAction.RETRY);
  });

  it('shows skip button when enabled', () => {
    const navigationControlWithSkip: NavigationControl = {
      ...defaultNavigationControl,
      showSkipButton: true,
      canSkip: true
    };

    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={navigationControlWithSkip}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    const skipButton = screen.getByText('skip');
    expect(skipButton).toBeInTheDocument();
    
    fireEvent.click(skipButton);
    expect(mockOnAction).toHaveBeenCalledWith(NavigationAction.SKIP);
  });

  it('disables buttons based on navigation control', () => {
    const disabledNavigationControl: NavigationControl = {
      canGoNext: false,
      canGoBack: false,
      canRetry: false,
      canSkip: false,
      showRetryButton: false,
      showSkipButton: false
    };

    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={disabledNavigationControl}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    const nextButton = screen.getByText('next');
    const backButton = screen.getByText('back');

    expect(nextButton).toBeDisabled();
    expect(backButton).toBeDisabled();
  });

  it('displays failure reason when test fails', () => {
    const failedTestResult: EnhancedTestResult = {
      status: TestStatus.FAILED,
      passed: false,
      failureReason: 'Network connection failed',
      timestamp: new Date()
    };

    render(
      <TestStepController
        onAction={mockOnAction}
        navigationControl={defaultNavigationControl}
        testResult={failedTestResult}
      >
        <div>Test Content</div>
      </TestStepController>
    );

    expect(screen.getByText('Network connection failed')).toBeInTheDocument();
  });
});
