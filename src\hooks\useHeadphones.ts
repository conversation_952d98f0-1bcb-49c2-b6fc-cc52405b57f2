import { useState, useRef, useCallback, useEffect } from 'react';

export interface AudioDevice {
  deviceId: string;
  label: string;
  kind: MediaDeviceKind;
}

export interface UseHeadphonesReturn {
  // Device management
  outputDevices: AudioDevice[];
  selectedOutputDevice: string;
  setSelectedOutputDevice: (deviceId: string) => void;
  
  // Audio playback
  isPlaying: boolean;
  currentTest: string | null;
  
  // Test functions
  playLeftChannel: () => Promise<void>;
  playRightChannel: () => Promise<void>;
  playFrequencySweep: () => Promise<void>;
  playDynamicRangeTest: () => Promise<void>;
  play3DAudio: () => Promise<void>;
  stopAllAudio: () => void;
  
  // Frequency sweep specific
  currentFrequency: number;
  sweepProgress: number;
  
  // 3D positioning specific
  currentPanPosition: number;
  
  // Initialization
  requestPermissions: () => Promise<void>;
  error: string | null;
}

export const useHeadphones = (): UseHeadphonesReturn => {
  const [outputDevices, setOutputDevices] = useState<AudioDevice[]>([]);
  const [selectedOutputDevice, setSelectedOutputDevice] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [currentFrequency, setCurrentFrequency] = useState(0);
  const [sweepProgress, setSweepProgress] = useState(0);
  const [currentPanPosition, setCurrentPanPosition] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const audioContextRef = useRef<AudioContext | null>(null);
  const currentSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const pannerNodeRef = useRef<StereoPannerNode | null>(null);
  const oscillatorRef = useRef<OscillatorNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const isCleanedUpRef = useRef(false);

  // Initialize audio context with better error handling
  const initializeAudioContext = useCallback(async () => {
    console.log('initializeAudioContext called');
    
    try {
      // Check if we're in cleanup state
      if (isCleanedUpRef.current) {
        console.log('Audio context is in cleanup state');
        throw new Error('Audio context has been cleaned up');
      }

      // Close existing context if it exists and is not in a good state
      if (audioContextRef.current && audioContextRef.current.state === 'closed') {
        console.log('Closing existing audio context');
        audioContextRef.current = null;
        gainNodeRef.current = null;
        pannerNodeRef.current = null;
      }

      if (!audioContextRef.current) {
        console.log('Creating new audio context...');
        // Create audio context with better browser compatibility
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        if (!AudioContextClass) {
          throw new Error('Web Audio API is not supported in this browser');
        }
        
        audioContextRef.current = new AudioContextClass();
        
        // Check if context was created successfully
        if (audioContextRef.current.state === 'closed') {
          throw new Error('Failed to create audio context');
        }
        
        gainNodeRef.current = audioContextRef.current.createGain();
        pannerNodeRef.current = audioContextRef.current.createStereoPanner();
        
        // Set initial gain to prevent audio being too loud
        gainNodeRef.current.gain.setValueAtTime(0.3, audioContextRef.current.currentTime);
        
        gainNodeRef.current.connect(pannerNodeRef.current);
        pannerNodeRef.current.connect(audioContextRef.current.destination);
      }
      
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }
    } catch (err) {
      console.error('Failed to initialize audio context:', err);
      setError('Failed to initialize audio system. Please check your browser permissions.');
      throw err;
    }
  }, []);

  // Get available output devices with better error handling
  const getOutputDevices = useCallback(async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        throw new Error('Media devices API is not supported');
      }

      // For audio output devices, we might need to request microphone permission first
      // to get device labels in some browsers
      let devices: MediaDeviceInfo[];
      try {
        devices = await navigator.mediaDevices.enumerateDevices();
      } catch (permissionErr) {
        console.log('Direct device enumeration failed, trying with permission request');
        // Try to get permission first, then enumerate
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          stream.getTracks().forEach(track => track.stop()); // Stop immediately
          devices = await navigator.mediaDevices.enumerateDevices();
        } catch (streamErr) {
          console.log('Permission request failed, using basic enumeration');
          devices = await navigator.mediaDevices.enumerateDevices();
        }
      }

      const audioOutputs = devices.filter((device: MediaDeviceInfo) => device.kind === 'audiooutput');

      const deviceList: AudioDevice[] = audioOutputs.map((device: MediaDeviceInfo) => ({
        deviceId: device.deviceId || 'default',
        label: device.label || `Audio Output ${(device.deviceId || 'default').slice(0, 8)}`,
        kind: device.kind
      }));

      // Add default device if no devices found
      if (deviceList.length === 0) {
        deviceList.push({
          deviceId: 'default',
          label: 'Default Audio Output',
          kind: 'audiooutput'
        });
      }

      setOutputDevices(deviceList);

      // Use functional update to avoid dependency on selectedOutputDevice
      setSelectedOutputDevice(prevSelected => {
        const newDeviceId = deviceList.length > 0 ? deviceList[0].deviceId : 'default';
        if (deviceList.length > 0 && (!prevSelected || prevSelected === '')) {
          return newDeviceId;
        }
        return prevSelected || 'default';
      });
    } catch (err) {
      console.error('Error getting output devices:', err);
      // Set default device as fallback
      const defaultDevice = {
        deviceId: 'default',
        label: 'Default Audio Output',
        kind: 'audiooutput' as MediaDeviceKind
      };
      setOutputDevices([defaultDevice]);
      setSelectedOutputDevice('default');
    }
  }, []); // Remove selectedOutputDevice dependency

  // Request permissions and initialize
  const requestPermissions = useCallback(async () => {
    try {
      setError(null);
      isCleanedUpRef.current = false;
      await initializeAudioContext();
      await getOutputDevices();
    } catch (err) {
      console.error('Error requesting permissions:', err);
      setError('Failed to initialize audio system. Please check your browser supports Web Audio API.');
    }
  }, [initializeAudioContext, getOutputDevices]);

  // Stop all audio with better cleanup
  const stopAllAudio = useCallback(() => {
    try {
      if (currentSourceRef.current) {
        try {
          currentSourceRef.current.stop();
          currentSourceRef.current.disconnect();
        } catch (e) {
          // Ignore errors when stopping already stopped sources
        }
        currentSourceRef.current = null;
      }
      
      if (oscillatorRef.current) {
        try {
          oscillatorRef.current.stop();
          oscillatorRef.current.disconnect();
        } catch (e) {
          // Ignore errors when stopping already stopped oscillators
        }
        oscillatorRef.current = null;
      }
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      
      setIsPlaying(false);
      setCurrentTest(null);
      setCurrentFrequency(0);
      setSweepProgress(0);
      setCurrentPanPosition(0);
    } catch (err) {
      console.error('Error stopping audio:', err);
      // Force reset state even if stopping fails
      setIsPlaying(false);
      setCurrentTest(null);
      setCurrentFrequency(0);
      setSweepProgress(0);
    }
  }, []);

  // Generate test tone with better error handling
  const generateTestTone = useCallback(async (frequency: number, duration: number, panValue: number = 0) => {
    console.log('generateTestTone called with:', { frequency, duration, panValue });
    
    try {
      console.log('Initializing audio context...');
      await initializeAudioContext();
      
      if (isCleanedUpRef.current) {
        console.log('Audio context has been cleaned up');
        throw new Error('Audio context has been cleaned up');
      }
      
      console.log('Audio context state:', audioContextRef.current?.state);
      console.log('Gain node exists:', !!gainNodeRef.current);
      console.log('Panner node exists:', !!pannerNodeRef.current);
      
      if (!audioContextRef.current || audioContextRef.current.state === 'closed' || !gainNodeRef.current || !pannerNodeRef.current) {
        console.log('Audio context not properly initialized');
        throw new Error('Audio context not initialized or closed');
      }

      console.log('Stopping previous audio without resetting state...');
      // Stop previous audio without resetting the playing state
      if (currentSourceRef.current) {
        try {
          currentSourceRef.current.stop();
          currentSourceRef.current.disconnect();
        } catch (e) {
          // Ignore errors when stopping already stopped sources
        }
        currentSourceRef.current = null;
      }
      
      if (oscillatorRef.current) {
        try {
          oscillatorRef.current.stop();
          oscillatorRef.current.disconnect();
        } catch (e) {
          // Ignore errors when stopping already stopped oscillators
        }
        oscillatorRef.current = null;
      }
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      const oscillator = audioContextRef.current.createOscillator();
      const envelope = audioContextRef.current.createGain();
      
      oscillator.connect(envelope);
      envelope.connect(gainNodeRef.current);
      
      oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
      oscillator.type = 'sine';
      
      // Set stereo panning
      pannerNodeRef.current.pan.setValueAtTime(panValue, audioContextRef.current.currentTime);
      
      // Envelope to prevent clicks
      envelope.gain.setValueAtTime(0, audioContextRef.current.currentTime);
      envelope.gain.linearRampToValueAtTime(0.3, audioContextRef.current.currentTime + 0.05);
      envelope.gain.linearRampToValueAtTime(0.3, audioContextRef.current.currentTime + duration - 0.05);
      envelope.gain.linearRampToValueAtTime(0, audioContextRef.current.currentTime + duration);
      
      oscillator.start(audioContextRef.current.currentTime);
      oscillator.stop(audioContextRef.current.currentTime + duration);
      
      oscillatorRef.current = oscillator;
      
      oscillator.onended = () => {
        if (!isCleanedUpRef.current) {
          setIsPlaying(false);
          setCurrentTest(null);
        }
        oscillatorRef.current = null;
      };
    } catch (err) {
      console.error('Error generating test tone:', err);
      setError('Failed to generate test tone. Please try again.');
      setIsPlaying(false);
      setCurrentTest(null);
    }
  }, [initializeAudioContext]);

  // Play left channel test
  const playLeftChannel = useCallback(async () => {
    console.log('playLeftChannel called');
    console.log('isPlaying:', isPlaying, 'isCleanedUpRef.current:', isCleanedUpRef.current);
    
    if (isPlaying || isCleanedUpRef.current) {
      console.log('Returning early - already playing or cleaned up');
      return;
    }
    
    try {
      console.log('Setting isPlaying to true and currentTest to left-channel');
      setIsPlaying(true);
      setCurrentTest('left-channel');
      console.log('Calling generateTestTone for left channel');
      await generateTestTone(440, 2, -1); // Pan fully left
      console.log('Left channel test tone generation completed');
    } catch (err) {
      console.error('Error playing left channel:', err);
      setError('Failed to play left channel test');
      setIsPlaying(false);
      setCurrentTest(null);
    }
  }, [generateTestTone]);

  // Play right channel test
  const playRightChannel = useCallback(async () => {
    console.log('playRightChannel called');
    console.log('isPlaying:', isPlaying, 'isCleanedUpRef.current:', isCleanedUpRef.current);
    
    if (isPlaying || isCleanedUpRef.current) {
      console.log('Returning early - already playing or cleaned up');
      return;
    }
    
    try {
      console.log('Setting isPlaying to true and currentTest to right-channel');
      setIsPlaying(true);
      setCurrentTest('right-channel');
      console.log('Calling generateTestTone for right channel');
      await generateTestTone(440, 2, 1); // Pan fully right
      console.log('Right channel test tone generation completed');
    } catch (err) {
      console.error('Error playing right channel:', err);
      setError('Failed to play right channel test');
      setIsPlaying(false);
      setCurrentTest(null);
    }
  }, [generateTestTone]);

  // Play frequency sweep with better error handling
  const playFrequencySweep = useCallback(async () => {
    if (isPlaying || isCleanedUpRef.current) return;
    
    try {
      await initializeAudioContext();
      
      if (isCleanedUpRef.current) {
        throw new Error('Audio context has been cleaned up');
      }
      
      if (!audioContextRef.current || audioContextRef.current.state === 'closed' || !gainNodeRef.current || !pannerNodeRef.current) {
        throw new Error('Audio context not initialized or closed');
      }

      stopAllAudio();
      setIsPlaying(true);
      setCurrentTest('frequency-sweep');

      const oscillator = audioContextRef.current.createOscillator();
      const envelope = audioContextRef.current.createGain();
      
      oscillator.connect(envelope);
      envelope.connect(gainNodeRef.current);
      
      oscillator.type = 'sine';
      pannerNodeRef.current.pan.setValueAtTime(0, audioContextRef.current.currentTime);
      
      // Frequency sweep from 20Hz to 20kHz over 12 seconds (extended for better experience)
      const startTime = audioContextRef.current.currentTime;
      const duration = 12;
      const startFreq = 20;
      const endFreq = 20000;
      
      oscillator.frequency.setValueAtTime(startFreq, startTime);
      oscillator.frequency.exponentialRampToValueAtTime(endFreq, startTime + duration);
      
      // Envelope with smoother transitions
      envelope.gain.setValueAtTime(0, startTime);
      envelope.gain.linearRampToValueAtTime(0.25, startTime + 0.2);
      envelope.gain.linearRampToValueAtTime(0.25, startTime + duration - 0.2);
      envelope.gain.linearRampToValueAtTime(0, startTime + duration);
      
      oscillator.start(startTime);
      oscillator.stop(startTime + duration);
      
      oscillatorRef.current = oscillator;
      
      // Update frequency display with smoother animation
      const updateFrequency = () => {
        if (!oscillatorRef.current || !audioContextRef.current || isCleanedUpRef.current) return;
        
        const elapsed = audioContextRef.current.currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Use exponential scale for more natural frequency progression
        const currentFreq = startFreq * Math.pow(endFreq / startFreq, progress);
        
        // Update with smooth transitions - 更高频率的更新以实现流畅的实时显示
        setCurrentFrequency(Math.round(currentFreq));
        setSweepProgress(progress);
        
        // 实时更新振荡器频率以保持同步
        if (oscillatorRef.current && oscillatorRef.current.frequency) {
          try {
            oscillatorRef.current.frequency.setValueAtTime(currentFreq, audioContextRef.current.currentTime);
          } catch (err) {
            // 忽略频率设置错误，继续更新显示
          }
        }
        
        if (progress < 1 && oscillatorRef.current && !isCleanedUpRef.current) {
          // 使用更短的间隔以获得更流畅的动画效果
          animationFrameRef.current = requestAnimationFrame(updateFrequency);
        }
      };
      
      // Start frequency update animation
      animationFrameRef.current = requestAnimationFrame(updateFrequency);
      
      oscillator.onended = () => {
        if (!isCleanedUpRef.current) {
          setIsPlaying(false);
          setCurrentTest(null);
          setCurrentFrequency(0);
          setSweepProgress(0);
        }
        oscillatorRef.current = null;
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      };
    } catch (err) {
      console.error('Error playing frequency sweep:', err);
      setError('Failed to play frequency sweep test');
      setIsPlaying(false);
      setCurrentTest(null);
      setCurrentFrequency(0);
      setSweepProgress(0);
    }
  }, [isPlaying, initializeAudioContext, stopAllAudio]);

  // Play dynamic range test with better error handling
  const playDynamicRangeTest = useCallback(async () => {
    if (isPlaying || isCleanedUpRef.current) return;
    
    try {
      setIsPlaying(true);
      setCurrentTest('dynamic-range');
      
      await initializeAudioContext();
      
      if (isCleanedUpRef.current) {
        throw new Error('Audio context has been cleaned up');
      }
      
      if (!audioContextRef.current || audioContextRef.current.state === 'closed' || !gainNodeRef.current) {
        throw new Error('Audio context not initialized or closed');
      }

      // Create a complex audio with quiet background and loud foreground
      const duration = 8;
      const ctx = audioContextRef.current;
      
      // Background white noise (quiet)
      const bufferSize = ctx.sampleRate * duration;
      const buffer = ctx.createBuffer(2, bufferSize, ctx.sampleRate);
      
      for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
        const channelData = buffer.getChannelData(channel);
        for (let i = 0; i < bufferSize; i++) {
          channelData[i] = (Math.random() - 0.5) * 0.05; // Very quiet noise
        }
      }
      
      const source = ctx.createBufferSource();
      source.buffer = buffer;
      source.connect(gainNodeRef.current);
      
      // Add periodic loud tones
      const createTone = (time: number, freq: number, volume: number) => {
        const oscillator = ctx.createOscillator();
        const gain = ctx.createGain();
        
        oscillator.connect(gain);
        gain.connect(gainNodeRef.current!);
        
        oscillator.frequency.setValueAtTime(freq, time);
        oscillator.type = 'sine';
        
        gain.gain.setValueAtTime(0, time);
        gain.gain.linearRampToValueAtTime(volume, time + 0.1);
        gain.gain.linearRampToValueAtTime(volume, time + 0.4);
        gain.gain.linearRampToValueAtTime(0, time + 0.5);
        
        oscillator.start(time);
        oscillator.stop(time + 0.5);
      };
      
      const startTime = ctx.currentTime;
      createTone(startTime + 1, 440, 0.3);  // A4
      createTone(startTime + 3, 880, 0.4);  // A5
      createTone(startTime + 5, 220, 0.2);  // A3
      createTone(startTime + 7, 1760, 0.35); // A6
      
      source.start(startTime);
      source.stop(startTime + duration);
      
      currentSourceRef.current = source;
      
      source.onended = () => {
        if (!isCleanedUpRef.current) {
          setIsPlaying(false);
          setCurrentTest(null);
        }
        currentSourceRef.current = null;
      };
    } catch (err) {
      console.error('Error playing dynamic range test:', err);
      setError('Failed to play dynamic range test');
      setIsPlaying(false);
      setCurrentTest(null);
    }
  }, [isPlaying, initializeAudioContext]);

  // Play 3D audio positioning test
  const play3DAudio = useCallback(async () => {
    if (isPlaying || isCleanedUpRef.current) return;
    
    try {
      setIsPlaying(true);
      setCurrentTest('3d-positioning');
      
      await initializeAudioContext();
      
      if (isCleanedUpRef.current) {
        throw new Error('Audio context has been cleaned up');
      }
      
      if (!audioContextRef.current || audioContextRef.current.state === 'closed' || !gainNodeRef.current || !pannerNodeRef.current) {
        throw new Error('Audio context not initialized or closed');
      }

      const oscillator = audioContextRef.current.createOscillator();
      const envelope = audioContextRef.current.createGain();
      
      oscillator.connect(envelope);
      envelope.connect(gainNodeRef.current);
      
      oscillator.frequency.setValueAtTime(440, audioContextRef.current.currentTime);
      oscillator.type = 'sine';
      
      const startTime = audioContextRef.current.currentTime;
      const duration = 8;
      
      // Envelope
      envelope.gain.setValueAtTime(0, startTime);
      envelope.gain.linearRampToValueAtTime(0.3, startTime + 0.1);
      envelope.gain.linearRampToValueAtTime(0.3, startTime + duration - 0.1);
      envelope.gain.linearRampToValueAtTime(0, startTime + duration);
      
      // Animated panning (left -> right -> center -> left)
      pannerNodeRef.current.pan.setValueAtTime(-1, startTime);
      pannerNodeRef.current.pan.linearRampToValueAtTime(1, startTime + 2);
      pannerNodeRef.current.pan.linearRampToValueAtTime(0, startTime + 4);
      pannerNodeRef.current.pan.linearRampToValueAtTime(-1, startTime + 6);
      pannerNodeRef.current.pan.linearRampToValueAtTime(0, startTime + duration);
      
      oscillator.start(startTime);
      oscillator.stop(startTime + duration);
      
      oscillatorRef.current = oscillator;
      
      // Update pan position in real-time
      const updatePanPosition = () => {
        if (!audioContextRef.current || isCleanedUpRef.current) return;
        
        const currentTime = audioContextRef.current.currentTime;
        const elapsed = currentTime - startTime;
        
        let panPosition = 0;
        
        if (elapsed < 2) {
          // Left to right: -1 to 1
          panPosition = -1 + (elapsed / 2) * 2;
        } else if (elapsed < 4) {
          // Right to center: 1 to 0
          panPosition = 1 - ((elapsed - 2) / 2) * 1;
        } else if (elapsed < 6) {
          // Center to left: 0 to -1
          panPosition = 0 - ((elapsed - 4) / 2) * 1;
        } else if (elapsed < duration) {
          // Left to center: -1 to 0
          panPosition = -1 + ((elapsed - 6) / 2) * 1;
        }
        
        setCurrentPanPosition(panPosition);
        
        if (elapsed < duration && !isCleanedUpRef.current) {
          requestAnimationFrame(updatePanPosition);
        }
      };
      
      // Start position update
      requestAnimationFrame(updatePanPosition);
      
      oscillator.onended = () => {
        if (!isCleanedUpRef.current) {
          setIsPlaying(false);
          setCurrentTest(null);
          setCurrentPanPosition(0);
        }
        oscillatorRef.current = null;
      };
    } catch (err) {
      console.error('Error playing 3D audio:', err);
      setError('Failed to play 3D audio test');
      setIsPlaying(false);
      setCurrentTest(null);
      setCurrentPanPosition(0);
    }
  }, [isPlaying, initializeAudioContext]);

  // Initialize devices on mount
  useEffect(() => {
    // Try to get devices immediately on mount, even without permissions
    // This helps with page refresh scenarios
    const initializeDevices = async () => {
      try {
        await getOutputDevices();
      } catch (err) {
        console.log('Initial device enumeration failed, will retry with permissions');
      }
    };

    initializeDevices();
  }, [getOutputDevices]);

  // Cleanup
  useEffect(() => {
    return () => {
      isCleanedUpRef.current = true;
      stopAllAudio();
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close().catch(console.error);
      }
    };
  }, [stopAllAudio]);

  return {
    outputDevices,
    selectedOutputDevice,
    setSelectedOutputDevice,
    isPlaying,
    currentTest,
    playLeftChannel,
    playRightChannel,
    playFrequencySweep,
    playDynamicRangeTest,
    play3DAudio,
    stopAllAudio,
    currentFrequency,
    sweepProgress,
    currentPanPosition,
    requestPermissions,
    error
  };
};