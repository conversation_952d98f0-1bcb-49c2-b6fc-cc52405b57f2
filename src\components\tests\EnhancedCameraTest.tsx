import React, { useEffect, useRef, useState, useCallback } from "react";
import { Camera, CameraOff, Monitor, Settings, CheckCircle, AlertTriangle } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useCamera } from "@/hooks/useCamera";
import { useLanguage } from "@/hooks/useLanguage";

interface EnhancedCameraTestProps {
  onNext?: (result?: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack?: () => void;
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
}

interface VideoQualityMetrics {
  resolution: string;
  frameRate: number;
  brightness: number; // 0-255
  contrast: number; // 0-1
  sharpness: number; // 0-1
  quality: 'excellent' | 'good' | 'fair' | 'poor';
}

export const EnhancedCameraTest: React.FC<EnhancedCameraTestProps> = ({ onNext, onBack, onTestResult }) => {
  const { t } = useLanguage();
  const {
    isActive,
    devices,
    selectedDevice,
    error,
    videoRef,
    startCamera,
    stopCamera,
    setSelectedDevice,
    hasPermission,
  } = useCamera();

  const [videoQuality, setVideoQuality] = useState<VideoQualityMetrics | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Analyze video quality
  const analyzeVideoQuality = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isActive) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Get image data for analysis
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Calculate brightness
    let totalBrightness = 0;
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      totalBrightness += (r + g + b) / 3;
    }
    const brightness = totalBrightness / (data.length / 4);

    // Calculate contrast (standard deviation of brightness)
    let varianceSum = 0;
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const pixelBrightness = (r + g + b) / 3;
      varianceSum += Math.pow(pixelBrightness - brightness, 2);
    }
    const contrast = Math.sqrt(varianceSum / (data.length / 4)) / 255;

    // Calculate sharpness (edge detection using Sobel operator)
    const grayscale = new Array(canvas.width * canvas.height);
    for (let i = 0; i < data.length; i += 4) {
      const gray = (data[i] + data[i + 1] + data[i + 2]) / 3;
      grayscale[i / 4] = gray;
    }

    let edgeSum = 0;
    for (let y = 1; y < canvas.height - 1; y++) {
      for (let x = 1; x < canvas.width - 1; x++) {
        const idx = y * canvas.width + x;
        
        // Sobel X
        const sobelX = 
          -1 * grayscale[idx - canvas.width - 1] + 1 * grayscale[idx - canvas.width + 1] +
          -2 * grayscale[idx - 1] + 2 * grayscale[idx + 1] +
          -1 * grayscale[idx + canvas.width - 1] + 1 * grayscale[idx + canvas.width + 1];
        
        // Sobel Y
        const sobelY = 
          -1 * grayscale[idx - canvas.width - 1] + -2 * grayscale[idx - canvas.width] + -1 * grayscale[idx - canvas.width + 1] +
          1 * grayscale[idx + canvas.width - 1] + 2 * grayscale[idx + canvas.width] + 1 * grayscale[idx + canvas.width + 1];
        
        edgeSum += Math.sqrt(sobelX * sobelX + sobelY * sobelY);
      }
    }
    const sharpness = edgeSum / ((canvas.width - 2) * (canvas.height - 2) * 255);

    // Get video settings
    const resolution = `${video.videoWidth}x${video.videoHeight}`;
    
    // Estimate frame rate (simplified)
    const frameRate = 30; // Most webcams default to 30fps

    // Determine overall quality
    let quality: VideoQualityMetrics['quality'] = 'poor';
    if (brightness > 80 && brightness < 200 && contrast > 0.3 && sharpness > 0.1) {
      quality = 'excellent';
    } else if (brightness > 60 && brightness < 220 && contrast > 0.2 && sharpness > 0.05) {
      quality = 'good';
    } else if (brightness > 40 && brightness < 240 && contrast > 0.1) {
      quality = 'fair';
    }

    const metrics: VideoQualityMetrics = {
      resolution,
      frameRate,
      brightness: Math.round(brightness),
      contrast: Math.round(contrast * 100) / 100,
      sharpness: Math.round(sharpness * 100) / 100,
      quality
    };

    setVideoQuality(metrics);
  }, [isActive]);

  // Start video quality analysis automatically when camera is active
  useEffect(() => {
    if (isActive) {
      // 摄像头激活后自动开始分析
      setIsAnalyzing(true);
      analyzeVideoQuality(); // 立即执行一次分析
      analysisIntervalRef.current = setInterval(analyzeVideoQuality, 1000);
    } else {
      // 摄像头停止时停止分析
      setIsAnalyzing(false);
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
        analysisIntervalRef.current = null;
      }
    }

    return () => {
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
      }
    };
  }, [isActive, analyzeVideoQuality]);

  // 移除手动控制分析的函数，因为现在是自动分析

  // 自动报告测试结果
  useEffect(() => {
    if (onTestResult && isActive && videoQuality) {
      const testPassed = videoQuality.quality === 'excellent' || videoQuality.quality === 'good';
      let failureReason;

      if (!testPassed) {
        const issues = [];
        if (videoQuality.brightness < 40) issues.push(t("lightingTooDark"));
        if (videoQuality.brightness > 240) issues.push(t("lightingTooBright"));
        if (videoQuality.contrast < 0.1) issues.push(t("contrastTooLow"));
        if (videoQuality.sharpness < 0.05) issues.push(t("imageBlurry"));
        failureReason = issues.length > 0 ? `${t("videoQualityIssues")}: ${issues.join(", ")}` : t("videoQualityPoor");
      }

      onTestResult({
        passed: testPassed,
        failureReason: failureReason,
        details: {
          videoQuality: videoQuality,
          selectedDevice: selectedDevice?.label,
          deviceCount: devices.length
        }
      });
    }
  }, [onTestResult, isActive, videoQuality, selectedDevice, devices, t]);

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'fair': return 'text-yellow-400';
      case 'poor': return 'text-red-400';
      default: return 'text-white/60';
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case 'excellent': return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'good': return <CheckCircle className="h-5 w-5 text-blue-400" />;
      case 'fair': return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'poor': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default: return <Settings className="h-5 w-5 text-white/60" />;
    }
  };

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {isActive ? (
            <Camera className="h-12 w-12 text-green-400" />
          ) : (
            <CameraOff className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("enhancedCameraTest")}</h2>
        <p className="text-white/70">
          {t("comprehensiveVideoTest")}
        </p>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {devices.length > 0 && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("selectCamera")}:
            </label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50"
            >
              {devices.map((device) => (
                <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                  {device.label}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="relative">
          <label className="block text-white/80 text-sm font-medium mb-2">
            {t("cameraPreview")}:
          </label>
          <div className="relative bg-black/30 rounded-xl overflow-hidden border border-white/20 aspect-video">
            {isActive ? (
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
                onLoadedMetadata={() => {
                  // 确保视频在元数据加载后开始播放
                  if (videoRef.current) {
                    videoRef.current.play().catch(console.warn);
                  }
                }}
                onCanPlay={() => {
                  // 当视频可以播放时确保开始播放
                  if (videoRef.current) {
                    videoRef.current.play().catch(console.warn);
                  }
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Monitor className="h-16 w-16 text-white/30 mx-auto mb-4" />
                  <p className="text-white/50">{t("cameraPreview")}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Video Quality Analysis - 自动显示 */}
        {isActive && videoQuality && (
          <div className="space-y-4">
            <div className="bg-white/5 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-white">{t("videoQualityAnalysis")}</h3>
                <div className="flex items-center space-x-2">
                  {getQualityIcon(videoQuality.quality)}
                  <span className={`font-medium ${getQualityColor(videoQuality.quality)}`}>
                    {videoQuality.quality === 'excellent' && t("excellent")}
                    {videoQuality.quality === 'good' && t("good")}
                    {videoQuality.quality === 'fair' && t("fair")}
                    {videoQuality.quality === 'poor' && t("poor")}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">{t("resolution")}:</span>
                    <span className="text-white font-medium">{videoQuality.resolution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t("frameRate")}:</span>
                    <span className="text-white font-medium">{videoQuality.frameRate} fps</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t("brightness")}:</span>
                    <span className="text-white font-medium">{videoQuality.brightness}/255</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">{t("contrast")}:</span>
                    <span className="text-white font-medium">{videoQuality.contrast}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t("sharpness")}:</span>
                    <span className="text-white font-medium">{videoQuality.sharpness}</span>
                  </div>
                </div>
              </div>

              {/* Quality Recommendations */}
              <div className="mt-4 pt-4 border-t border-white/10">
                <h4 className="text-white/90 font-medium mb-2">{t("recommendations")}</h4>
                {videoQuality.quality === 'excellent' && (
                  <p className="text-green-200/80 text-sm">
                    {t("videoQualityExcellentDesc")}
                  </p>
                )}
                {videoQuality.quality === 'good' && (
                  <p className="text-blue-200/80 text-sm">
                    {t("videoQualityGoodDesc")}
                  </p>
                )}
                {videoQuality.quality === 'fair' && (
                  <p className="text-yellow-200/80 text-sm">
                    {t("videoQualityFairDesc")}
                  </p>
                )}
                {videoQuality.quality === 'poor' && (
                  <p className="text-red-200/80 text-sm">
                    {t("videoQualityPoorDesc")}
                  </p>
                )}

                {videoQuality.brightness < 80 && (
                  <p className="text-yellow-200/80 text-sm mt-2">
                    {t("lightingTooLow")}
                  </p>
                )}
                {videoQuality.brightness > 200 && (
                  <p className="text-yellow-200/80 text-sm mt-2">
                    {t("lightingTooHigh")}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {isActive && !videoQuality && (
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 animate-spin text-blue-400" />
              <span className="text-blue-200 font-medium">{t("analyzingVideoQuality")}</span>
            </div>
          </div>
        )}

        <div className="flex justify-center">
          {!isActive ? (
            <PrimaryButton onClick={startCamera} size="lg">
              <Camera className="h-5 w-5" />
              {t("startCameraTest")}
            </PrimaryButton>
          ) : (
            <PrimaryButton onClick={stopCamera} variant="secondary" size="lg">
              <CameraOff className="h-5 w-5" />
              {t("stopCamera")}
            </PrimaryButton>
          )}
        </div>

        {isActive && (
          <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4">
            <h4 className="text-green-200 font-medium mb-2">{t("cameraWorkingNormally")}</h4>
            <p className="text-green-200/80 text-sm">
              {t("cameraStartedSuccessfully")}
            </p>
          </div>
        )}

        {!isActive && (
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">{t("videoOptimizationTips")}</h4>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>{t("videoTip1")}</li>
              <li>{t("videoTip2")}</li>
              <li>{t("videoTip3")}</li>
              <li>{t("videoTip4")}</li>
            </ul>
          </div>
        )}
      </div>

      {/* Hidden canvas for video analysis */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {/* 保留兼容性的导航按钮 - 仅在使用旧API时显示 */}
      {!onTestResult && (
        <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
          <PrimaryButton onClick={onBack} variant="outline">
            {t("back")}
          </PrimaryButton>
          <PrimaryButton onClick={() => {
            // 报告摄像头测试结果
            let testPassed = false;
            let failureReason = "";
            
            if (!hasPermission) {
              failureReason = t("cameraPermissionDenied");
            } else if (devices.length === 0) {
              failureReason = t("noCameraDevices");
            } else if (!isActive) {
              failureReason = t("cameraNotStarted");
            } else if (error) {
              failureReason = `${t("cameraError")}: ${error}`;
            } else if (videoQuality && videoQuality.quality === 'poor') {
              const issues = [];
              if (videoQuality.brightness < 40) issues.push(t("lightingTooDark"));
              if (videoQuality.brightness > 240) issues.push(t("lightingTooBright"));
              if (videoQuality.contrast < 0.1) issues.push(t("contrastTooLow"));
              if (videoQuality.sharpness < 0.05) issues.push(t("imageBlurry"));
              failureReason = issues.length > 0 ? `${t("videoQualityIssues")}: ${issues.join(", ")}` : t("videoQualityPoor");
            } else {
              testPassed = true;
            }
            
            onNext?.({
              passed: testPassed,
              failureReason: testPassed ? undefined : failureReason,
              details: {
                permissionGranted: hasPermission,
                cameraActive: isActive,
                videoQuality: videoQuality,
                deviceCount: devices.length,
                selectedDevice: selectedDevice,
                error: error
              }
            });
          }}>
            {t("completeTest")}
          </PrimaryButton>
        </div>
      )}
    </GlassCard>
  );
};