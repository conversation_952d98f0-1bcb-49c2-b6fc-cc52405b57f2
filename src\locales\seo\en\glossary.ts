/**
 * English - Technical Glossary
 * Contains definitions of technical terms related to device testing
 */

import type { GlossaryTranslation } from '../types';

export const enGlossary: GlossaryTranslation = {
  title: "Technical Glossary",
  terms: {
    resolution: {
      title: "Resolution",
      description: "Video pixel dimensions like 1920x1080, higher values mean clearer picture quality"
    },
    frameRate: {
      title: "Frame Rate",
      description: "Number of image frames displayed per second, usually expressed in fps, affects video smoothness"
    },
    latency: {
      title: "Latency",
      description: "Time delay in data transmission, measured in milliseconds (ms), lower is better"
    },
    bandwidth: {
      title: "Bandwidth",
      description: "Network transmission capacity, usually measured in Mbps, determines data transfer speed"
    },
    sampleRate: {
      title: "Sample Rate",
      description: "Number of audio samples collected per second, common rates include 44.1kHz, 48kHz"
    },
    bitRate: {
      title: "Bit Rate",
      description: "Audio or video data transmission rate, affects quality and file size"
    },
    dpi: {
      title: "DPI",
      description: "Mouse sensitivity unit, represents pixels moved per inch"
    },
    pollingRate: {
      title: "Polling Rate",
      description: "Frequency at which device reports status to computer, measured in Hz, higher means faster response"
    },
    fps: {
      title: "Frames Per Second (FPS)",
      description: "Number of image frames displayed per second, affects video smoothness and quality"
    },
    megapixel: {
      title: "Megapixel",
      description: "Basic unit of digital images, megapixels determine image sharpness"
    },
    exposure: {
      title: "Exposure",
      description: "Camera's light sensitivity level, affects image brightness and clarity"
    },
    noiseReduction: {
      title: "Noise Reduction",
      description: "Technology to remove noise and background sounds from audio"
    },
    sensitivity: {
      title: "Sensitivity",
      description: "Microphone's ability to detect sound signals"
    },
    frequency: {
      title: "Frequency",
      description: "Number of vibrations of sound or electrical signals, measured in Hz"
    },
    impedance: {
      title: "Impedance",
      description: "Audio device's resistance to current, affects power matching"
    },
    soundStage: {
      title: "Sound Stage",
      description: "Audio's sense of space and positioning, reflects sound quality layers"
    },
    drivers: {
      title: "Drivers",
      description: "Core components of headphones that convert electrical signals to sound"
    },
    thd: {
      title: "Total Harmonic Distortion",
      description: "Measurement indicator of audio signal distortion level"
    },
    keyTravel: {
      title: "Key Travel",
      description: "Distance a key moves from rest position to actuation"
    },
    actuationForce: {
      title: "Actuation Force",
      description: "Minimum pressure required to actuate a key"
    },
    tactile: {
      title: "Tactile",
      description: "Tactile feedback when a key is triggered"
    },
    linear: {
      title: "Linear",
      description: "Characteristic where key pressure is proportional to travel distance"
    },
    polling: {
      title: "Polling",
      description: "Mechanism where system periodically checks device status"
    },
    acceleration: {
      title: "Acceleration",
      description: "Mouse response characteristics during fast movement"
    },
    liftOffDistance: {
      title: "Lift-off Distance",
      description: "Maximum height mouse can be lifted while still being detected"
    },
    tracking: {
      title: "Tracking",
      description: "Mouse sensor's ability to detect movement"
    },
    jitter: {
      title: "Jitter",
      description: "Degree of network latency variation, affects stability"
    },
    packetLoss: {
      title: "Packet Loss",
      description: "Percentage of data packets lost during network transmission"
    },
    throughput: {
      title: "Throughput",
      description: "Actual rate of network data transmission"
    },
    codec: {
      title: "Codec",
      description: "Algorithm for compressing and decompressing audio/video data"
    },
    compression: {
      title: "Compression",
      description: "Technology to reduce data size for bandwidth savings"
    },
    inputLag: {
      title: "Input Lag",
      description: "Time difference between input action and system response"
    },
    compatibility: {
      title: "Compatibility",
      description: "Ability of hardware devices to work properly with different systems and software"
    },
    accuracy: {
      title: "Accuracy",
      description: "Precision and reliability of test results, indicating how close measurements are to actual values"
    },
    realTime: {
      title: "Real-time",
      description: "Processing and response occurring immediately without noticeable delay"
    },
    calibration: {
      title: "Calibration",
      description: "Process of adjusting device settings to ensure accurate and optimal performance"
    },
    benchmark: {
      title: "Benchmark",
      description: "Standardized test used to measure and compare device performance against established criteria"
    }
  }
};
