import React from "react";
import { PrimaryButton } from "./PrimaryButton";
import { useLanguage } from "@/hooks/useLanguage";
import {
  NavigationControl,
  EnhancedTestResult,
  TestStatus,
  NavigationAction
} from "@/types/testWorkflow";
import {
  <PERSON><PERSON>ircle,
  XCircle,
  Clock,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON><PERSON><PERSON>c<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Loader2
} from "lucide-react";

interface TestStepControllerProps {
  children: React.ReactNode;
  onAction: (action: NavigationAction, result?: EnhancedTestResult) => void;
  navigationControl: NavigationControl;
  testResult?: EnhancedTestResult;
  stepTitle?: string;
  stepDescription?: string;
  className?: string;
  showProgress?: boolean;
  currentStep?: number;
  totalSteps?: number;
}

export const TestStepController: React.FC<TestStepControllerProps> = ({
  children,
  onAction,
  navigationControl,
  testResult,
  stepTitle,
  stepDescription,
  className = "",
  showProgress = true,
  currentStep,
  totalSteps
}) => {
  const { t } = useLanguage();

  const handleNext = () => {
    onAction(NavigationAction.NEXT, testResult);
  };

  const handleBack = () => {
    onAction(NavigationAction.BACK);
  };

  const handleRetry = () => {
    onAction(NavigationAction.RETRY);
  };

  const handleSkip = () => {
    // 直接跳过，不需要确认
    onAction(NavigationAction.SKIP);
  };

  const getStatusIcon = () => {
    if (!testResult) {
      return <Clock className="h-5 w-5 text-blue-400" />;
    }

    switch (testResult.status) {
      case TestStatus.COMPLETED:
        return testResult.passed
          ? <CheckCircle className="h-5 w-5 text-green-400" />
          : <XCircle className="h-5 w-5 text-red-400" />;
      case TestStatus.FAILED:
        return <XCircle className="h-5 w-5 text-red-400" />;
      case TestStatus.IN_PROGRESS:
        return <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />;
      case TestStatus.SKIPPED:
        return <SkipForward className="h-5 w-5 text-yellow-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    if (!testResult) {
      return t("testNotStarted");
    }

    switch (testResult.status) {
      case TestStatus.COMPLETED:
        return testResult.passed ? t("testPassed") : t("testFailed");
      case TestStatus.FAILED:
        return t("testFailed");
      case TestStatus.IN_PROGRESS:
        return t("testInProgress");
      case TestStatus.SKIPPED:
        return t("testSkipped");
      default:
        return t("testNotStarted");
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 步骤标题和进度 */}
      {(stepTitle || showProgress) && (
        <div className="mb-6">
          {showProgress && currentStep && totalSteps && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-white/60 mb-2">
                <span>{t("step")} {currentStep} {t("of")} {totalSteps}</span>
                <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                />
              </div>
            </div>
          )}

          {stepTitle && (
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-2">{stepTitle}</h2>
              {stepDescription && (
                <p className="text-white/70">{stepDescription}</p>
              )}
            </div>
          )}
        </div>
      )}

      {/* 测试内容区域 */}
      <div className="mb-8">
        {children}
      </div>

      {/* 测试状态指示器 */}
      {testResult && (
        <div className="bg-white/5 rounded-xl p-4 mb-6">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div className="flex-1">
              <div className="text-white font-medium">{getStatusText()}</div>
              {testResult.failureReason && (
                <div className="text-red-300 text-sm mt-1">
                  {testResult.failureReason}
                </div>
              )}
            </div>
          </div>
        </div>
      )}



      {/* 控制按钮区域 */}
      <div className="flex justify-between items-center pt-6 border-t border-white/10">
        <PrimaryButton
          onClick={handleBack}
          variant="outline"
          className="min-w-[120px]"
          disabled={!navigationControl.canGoBack}
        >
          {navigationControl.backButtonText ? t(navigationControl.backButtonText) : t("back")}
        </PrimaryButton>

        <div className="flex items-center space-x-3">
          {/* 重试按钮 */}
          {navigationControl.showRetryButton && (
            <PrimaryButton
              onClick={handleRetry}
              variant="outline"
              className="min-w-[100px]"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {t("retry")}
            </PrimaryButton>
          )}

          {/* 跳过按钮 */}
          {navigationControl.showSkipButton && (
            <PrimaryButton
              onClick={handleSkip}
              variant="outline"
              className="min-w-[100px] border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 hover:border-yellow-400"
            >
              <SkipForward className="h-4 w-4 mr-2" />
              {t("skip")}
            </PrimaryButton>
          )}

          {/* 下一步按钮 */}
          <PrimaryButton
            onClick={handleNext}
            disabled={!navigationControl.canGoNext}
            className="min-w-[120px]"
          >
            {navigationControl.nextButtonText ? t(navigationControl.nextButtonText) : t("next")}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};