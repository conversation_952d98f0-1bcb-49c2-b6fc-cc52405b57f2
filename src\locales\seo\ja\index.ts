/**
 * 日本語 - メインSEO翻訳エクスポート
 * すべての日本語翻訳を統一されたオブジェクトに結合
 */

import { jaEnhanced } from './enhanced';
import { jaFooter } from './footer';
import { jaKeywords } from './keywords';
import { jaFAQ } from './faq';
import { jaGlossary } from './glossary';
import { jaTroubleshooting } from './troubleshooting';
import type { SEOTranslation } from '../types';

export const jaTranslation: SEOTranslation = {
  enhanced: jaEnhanced,
  seoFooter: jaFooter,
  seoKeywords: jaKeywords,
  faq: jaFAQ,
  glossary: jaGlossary,
  troubleshooting: jaTroubleshooting
};

// 直接使用のための個別モジュールをエクスポート
export {
  jaEnhanced,
  jaFooter,
  jaKeywords,
  jaFAQ,
  jaGlossary,
  jaTroubleshooting
};
