# Google Analytics 4 (GA4) 集成指南

本指南详细说明了如何在项目中配置和使用 Google Analytics 4。

## 🚀 快速开始

### 5 分钟快速配置

1. **获取 GA4 测量 ID**:
   - 访问 [Google Analytics](https://analytics.google.com/)
   - 创建 GA4 属性
   - 复制测量 ID（格式：G-XXXXXXXXXX）

2. **配置环境变量**:
   ```env
   VITE_GA4_MEASUREMENT_ID=G-XXXXXXXXXX  # 您的测量 ID
   VITE_GA4_ENABLED=true
   ```

3. **验证集成**:
   - 启动开发服务器：`npm run dev`
   - 查看右下角的"GA4 调试器"
   - 点击"运行诊断"确认配置正确

4. **测试事件**:
   - 在调试器中点击"测试事件"
   - 在 Google Analytics 实时报告中查看数据

✅ **完成！** GA4 现在已集成到您的项目中。

## 📋 详细目录

1. [Google Analytics 账户设置](#google-analytics-账户设置)
2. [项目配置](#项目配置)
3. [验证集成](#验证集成)
4. [事件跟踪](#事件跟踪)
5. [隐私合规](#隐私合规)
6. [性能优化](#性能优化)
7. [故障排除](#故障排除)

## 🚀 Google Analytics 账户设置

### 步骤 1：创建 Google Analytics 账户

1. 访问 [Google Analytics](https://analytics.google.com/)
2. 点击"开始使用"
3. 创建账户或使用现有的 Google 账户登录

### 步骤 2：创建 GA4 属性

1. 在 Google Analytics 中，点击"管理"（齿轮图标）
2. 在"账户"列中，选择或创建账户
3. 在"属性"列中，点击"创建属性"
4. 选择"GA4"作为属性类型
5. 填写属性详细信息：
   - **属性名称**: Setup Check（或您的网站名称）
   - **报告时区**: 选择您的时区
   - **货币**: 选择适当的货币

### 步骤 3：设置数据流

1. 在属性设置中，点击"数据流"
2. 点击"添加流" > "网站"
3. 填写网站信息：
   - **网站 URL**: 您的网站域名
   - **流名称**: Setup Check Web
4. 点击"创建流"

### 步骤 4：获取测量 ID

1. 在数据流详情页面，找到"测量 ID"
2. 复制测量 ID（格式：G-XXXXXXXXXX）
3. 保存此 ID，稍后需要在项目中配置

## ⚙️ 项目配置

### 步骤 1：环境变量配置

1. 打开项目根目录下的 `.env` 文件
2. 将 `VITE_GA4_MEASUREMENT_ID` 的值替换为您的测量 ID：

```env
# Google Analytics 4 配置
VITE_GA4_MEASUREMENT_ID=G-XXXXXXXXXX  # 替换为您的实际测量 ID
VITE_GA4_ENABLED=true
VITE_GA4_DEBUG=false  # 生产环境设置为 false
```

### 步骤 2：开发环境配置

对于开发环境，您可以：

1. 创建单独的 GA4 属性用于测试
2. 或者在开发时禁用 GA4：

```env
VITE_GA4_ENABLED=false  # 开发时禁用
VITE_GA4_DEBUG=true     # 启用调试信息
```

## ✅ 验证集成

### 方法 1：使用内置调试器

1. 在开发环境下，页面右下角会显示"GA4 调试器"
2. 点击"运行诊断"检查配置状态
3. 点击"测试事件"发送测试事件

### 方法 2：浏览器开发者工具

1. 打开浏览器开发者工具（F12）
2. 切换到"网络"标签页
3. 过滤请求：搜索 "google-analytics" 或 "collect"
4. 浏览网站，应该能看到发送到 GA4 的请求

### 方法 3：Google Analytics 实时报告

1. 在 Google Analytics 中，转到"报告" > "实时"
2. 在网站上进行操作（页面浏览、点击等）
3. 实时报告应该显示活动用户和事件

### 方法 4：Google Tag Assistant

1. 安装 [Google Tag Assistant](https://tagassistant.google.com/) 浏览器扩展
2. 访问您的网站
3. 点击扩展图标查看 GA4 标签状态

## 📊 事件跟踪

### 自动跟踪的事件

项目已自动配置以下事件跟踪：

1. **页面浏览** - 自动跟踪所有页面访问
2. **设备测试事件**：
   - 测试开始：`test_start`
   - 测试完成：`test_complete`
   - 测试错误：`test_error`
3. **用户交互**：
   - 按钮点击：`click`
   - 表单提交：`form_submit`
   - 外部链接点击：`external_link_click`

### 自定义事件示例

```typescript
import { useAnalytics } from '@/hooks/useAnalytics';

const { track } = useAnalytics();

// 发送自定义事件
track({
  action: 'custom_action',
  category: 'user_engagement',
  label: 'feature_usage',
  value: 1
});
```

## 🔒 隐私合规

### GDPR 合规

项目已集成 GDPR 合规的 Cookie 同意机制：

1. **Cookie 横幅** - 首次访问时显示
2. **同意管理** - 用户可以自定义 Cookie 偏好
3. **数据控制** - 用户可以随时更改同意设置

### Cookie 类别

- **必要 Cookie** - 网站正常运行必需（始终启用）
- **分析 Cookie** - 用于 GA4 数据收集
- **营销 Cookie** - 用于广告和营销
- **偏好 Cookie** - 用于个性化设置

### 隐私设置

GA4 配置了以下隐私保护措施：

- IP 地址匿名化
- 禁用广告功能
- 禁用个性化广告信号
- 安全的 Cookie 设置

## ⚡ 性能优化

### 已实现的优化

1. **延迟加载** - GA4 脚本在页面加载完成后加载
2. **空闲时初始化** - 使用 `requestIdleCallback` 在浏览器空闲时初始化
3. **预连接** - DNS 预取和预连接优化
4. **条件加载** - 只在用户同意时加载 GA4

### 性能监控

项目自动收集以下性能指标：

- 页面加载时间
- DOM 内容加载时间
- 首次内容绘制时间

## 🔧 故障排除

### 常见问题

#### 1. GA4 未初始化

**症状**: 调试器显示"GA4 未初始化"

**解决方案**:
- 检查测量 ID 是否正确配置
- 确认用户已同意分析 Cookie
- 检查网络连接和防火墙设置

#### 2. 事件未发送

**症状**: 实时报告中看不到事件

**解决方案**:
- 使用浏览器开发者工具检查网络请求
- 确认 GA4 已正确初始化
- 检查事件参数是否正确

#### 3. Cookie 同意问题

**症状**: Cookie 横幅不显示或同意设置无效

**解决方案**:
- 清除浏览器本地存储
- 检查隐私政策版本是否更新
- 确认同意组件正确加载

### 调试技巧

1. **启用调试模式**:
   ```env
   VITE_GA4_DEBUG=true
   ```

2. **查看控制台日志**:
   - 搜索 "[GA4 Debug]" 或 "[GA4 Error]"

3. **使用 GA4 调试视图**:
   - 在 Google Analytics 中启用调试视图
   - 发送带有 `debug_mode: true` 的事件

### 联系支持

如果遇到无法解决的问题：

1. 检查 [Google Analytics 帮助中心](https://support.google.com/analytics/)
2. 查看项目的 GitHub Issues
3. 联系开发团队

## 📈 最佳实践

1. **定期检查数据质量** - 使用 GA4 的数据质量报告
2. **设置转化目标** - 配置重要的业务指标
3. **创建自定义报告** - 根据业务需求创建专门的报告
4. **监控性能影响** - 定期检查 GA4 对网站性能的影响
5. **保持合规性** - 定期审查隐私政策和 Cookie 设置

---

**注意**: 本指南基于 Google Analytics 4 的当前版本。Google 可能会更新其界面和功能，请参考官方文档获取最新信息。
