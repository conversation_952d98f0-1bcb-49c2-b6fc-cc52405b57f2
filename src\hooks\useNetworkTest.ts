import { useState, useCallback, useRef } from 'react';
// @ts-ignore - Cloudflare speedtest 可能没有类型定义
import SpeedTest from '@cloudflare/speedtest';

// 用户位置信息接口
interface UserLocation {
  latitude?: number;
  longitude?: number;
  city?: string;
  country?: string;
  countryCode?: string;
  region?: string;
  accuracy?: number; // 精度（米）
  source: 'gps' | 'ip' | 'timezone'; // 位置来源
}

// 计算两个地理坐标之间的距离（使用Haversine公式）
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// 格式化距离显示
const formatDistance = (distance: number): string => {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  } else if (distance < 100) {
    return `${distance.toFixed(1)}km`;
  } else {
    return `${Math.round(distance)}km`;
  }
};

// 通过多个免费API获取用户位置信息（备选方案）
const getUserLocationByIP = async (): Promise<UserLocation | null> => {
  // 尝试多个真正免费且支持CORS的地理位置API
  const apis = [
    {
      url: 'http://ip-api.com/json/',
      parser: (data: any) => ({
        latitude: data.lat,
        longitude: data.lon,
        city: data.city,
        country: data.country,
        countryCode: data.countryCode,
        region: data.regionName,
        source: 'ip' as const
      })
    },
    {
      url: 'https://ipinfo.io/json',
      parser: (data: any) => {
        const [lat, lon] = (data.loc || '').split(',').map(Number);
        return {
          latitude: lat || undefined,
          longitude: lon || undefined,
          city: data.city,
          country: data.country,
          countryCode: data.country,
          region: data.region,
          source: 'ip' as const
        };
      }
    },
    {
      url: 'https://api.country.is/',
      parser: (data: any) => ({
        latitude: undefined,
        longitude: undefined,
        city: undefined,
        country: data.country,
        countryCode: data.country,
        region: undefined,
        source: 'ip' as const
      })
    }
  ];

  for (const api of apis) {
    try {
      const response = await fetch(api.url);
      if (response.ok) {
        const data = await response.json();
        const result = api.parser(data);
        if (result && (result.latitude || result.country)) {
          console.log('Successfully got location from API:', api.url, result);
          return result;
        }
      }
    } catch (error) {
      console.warn(`Failed to get location from ${api.url}:`, error);
      continue;
    }
  }

  return null;
};

// 通过时区和语言推断用户位置（最后备选方案）
const getUserLocationByTimezone = (): UserLocation | null => {
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const language = navigator.language.toLowerCase();

    // 基于时区的简单位置推断
    const timezoneMap: Record<string, Partial<UserLocation>> = {
      'Asia/Shanghai': { city: '上海', country: '中国', countryCode: 'CN' },
      'Asia/Beijing': { city: '北京', country: '中国', countryCode: 'CN' },
      'Asia/Hong_Kong': { city: '香港', country: '中国香港', countryCode: 'HK' },
      'Asia/Taipei': { city: '台北', country: '中国台湾', countryCode: 'TW' },
      'Asia/Tokyo': { city: '东京', country: '日本', countryCode: 'JP' },
      'Asia/Seoul': { city: '首尔', country: '韩国', countryCode: 'KR' },
      'Asia/Singapore': { city: '新加坡', country: '新加坡', countryCode: 'SG' },
      'Europe/London': { city: '伦敦', country: '英国', countryCode: 'GB' },
      'Europe/Paris': { city: '巴黎', country: '法国', countryCode: 'FR' },
      'Europe/Berlin': { city: '柏林', country: '德国', countryCode: 'DE' },
      'America/New_York': { city: '纽约', country: '美国', countryCode: 'US' },
      'America/Los_Angeles': { city: '洛杉矶', country: '美国', countryCode: 'US' },
      'America/Chicago': { city: '芝加哥', country: '美国', countryCode: 'US' }
    };

    const locationInfo = timezoneMap[timezone];
    if (locationInfo) {
      return {
        ...locationInfo,
        source: 'timezone'
      } as UserLocation;
    }

    // 基于语言的简单推断
    const languageMap: Record<string, Partial<UserLocation>> = {
      'zh-cn': { country: '中国', countryCode: 'CN' },
      'zh-tw': { country: '中国台湾', countryCode: 'TW' },
      'zh-hk': { country: '中国香港', countryCode: 'HK' },
      'ja': { country: '日本', countryCode: 'JP' },
      'ko': { country: '韩国', countryCode: 'KR' },
      'en-us': { country: '美国', countryCode: 'US' },
      'en-gb': { country: '英国', countryCode: 'GB' },
      'de': { country: '德国', countryCode: 'DE' },
      'fr': { country: '法国', countryCode: 'FR' },
      'es': { country: '西班牙', countryCode: 'ES' }
    };

    const langInfo = languageMap[language] || languageMap[language.split('-')[0]];
    if (langInfo) {
      return {
        ...langInfo,
        source: 'timezone'
      } as UserLocation;
    }

  } catch (error) {
    console.warn('Failed to get location by timezone:', error);
  }

  return null;
};

// 获取用户地理位置
const getUserLocation = async (): Promise<UserLocation | null> => {
  // 首先尝试使用浏览器地理位置API
  if ('geolocation' in navigator) {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5分钟缓存
          }
        );
      });

      // 使用反向地理编码获取地址信息 - 使用可靠的免费API
      let addressInfo: Partial<UserLocation> = {};
      const reverseGeoApis = [
        {
          url: `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=zh`,
          parser: (data: any) => ({
            city: data.city || data.locality,
            country: data.countryName,
            countryCode: data.countryCode,
            region: data.principalSubdivision
          })
        },
        {
          url: `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}&accept-language=zh&zoom=10`,
          parser: (data: any) => ({
            city: data.address?.city || data.address?.town || data.address?.village,
            country: data.address?.country,
            countryCode: data.address?.country_code?.toUpperCase(),
            region: data.address?.state || data.address?.province
          })
        }
      ];

      for (const api of reverseGeoApis) {
        try {
          const response = await fetch(api.url, {
            headers: {
              'User-Agent': 'GlassEcho-Device-Check/1.0'
            }
          });
          if (response.ok) {
            const data = await response.json();
            const parsed = api.parser(data);
            if (parsed.city || parsed.country) {
              addressInfo = parsed;
              console.log('Got address info from', api.url, ':', addressInfo);
              break;
            }
          }
        } catch (error) {
          console.warn(`Failed to get address from ${api.url}:`, error);
          continue;
        }
      }

      return {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        source: 'gps',
        ...addressInfo
      };
    } catch (error) {
      console.warn('GPS location failed, trying IP location:', error);
    }
  }

  // 如果GPS失败，尝试IP位置
  console.log('GPS failed, trying IP location...');
  const ipLocation = await getUserLocationByIP();
  if (ipLocation) {
    return ipLocation;
  }

  // 如果IP位置也失败，使用时区推断
  console.log('IP location failed, trying timezone inference...');
  const timezoneLocation = getUserLocationByTimezone();
  if (timezoneLocation) {
    return timezoneLocation;
  }

  // 最后备选：至少返回一个基本的位置信息
  console.log('All location methods failed, using basic fallback...');
  return {
    city: undefined,
    country: '未知',
    countryCode: 'XX',
    region: undefined,
    latitude: undefined,
    longitude: undefined,
    source: 'timezone'
  };
};

// 获取 Cloudflare 位置信息的函数
const getCloudflareLocationInfo = async () => {
  try {
    // 使用 Cloudflare 的 trace API 获取位置信息
    const traceResponse = await fetch('https://cloudflare.com/cdn-cgi/trace');
    const traceText = await traceResponse.text();
    
    // 解析 trace 数据
    const traceData: Record<string, string> = {};
    traceText.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        traceData[key] = value;
      }
    });

    console.log('Cloudflare trace data:', traceData);

    // 获取详细的位置信息 - 使用简单可靠的API
    let detailedLocation = null;
    if (traceData.ip) {
      // 尝试几个可靠的地理位置API
      const geoApis = [
        {
          url: `http://ip-api.com/json/${traceData.ip}`,
          parser: (data: any) => ({
            city: data.city,
            country_code: data.countryCode,
            latitude: data.lat,
            longitude: data.lon,
            region: data.regionName
          })
        },
        {
          url: `https://ipinfo.io/${traceData.ip}/json`,
          parser: (data: any) => {
            const [lat, lon] = (data.loc || '').split(',').map(Number);
            return {
              city: data.city,
              country_code: data.country,
              latitude: lat || undefined,
              longitude: lon || undefined,
              region: data.region
            };
          }
        }
      ];

      for (const api of geoApis) {
        try {
          const locationResponse = await fetch(api.url);
          if (locationResponse.ok) {
            const data = await locationResponse.json();
            detailedLocation = api.parser(data);

            if (detailedLocation && (detailedLocation.city || detailedLocation.latitude)) {
              console.log('Detailed location data from', api.url, ':', detailedLocation);
              break;
            }
          }
        } catch (err) {
          console.warn(`Failed to get location from ${api.url}:`, err);
          continue;
        }
      }
    }

    return {
      serverIP: traceData.ip,
      edgeLocation: traceData.colo,
      colo: traceData.colo,
      country: traceData.loc,
      timezone: traceData.tz,
      city: detailedLocation?.city,
      countryCode: detailedLocation?.country_code,
      region: detailedLocation?.region,
      latitude: detailedLocation?.latitude,
      longitude: detailedLocation?.longitude
    };
  } catch (error) {
    console.error('Failed to get Cloudflare location info:', error);
    return null;
  }
};

export interface NetworkTestResult {
  latency: number; // ms - unloaded latency
  downloadSpeed: number; // Mbps
  uploadSpeed: number; // Mbps
  packetLoss: number; // percentage
  jitter: number; // ms - unloaded jitter
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  testRegion?: string; // Cloudflare edge location
  // Geographic information
  testLocation?: {
    city?: string;
    country?: string;
    countryCode?: string;
    edgeLocation?: string;
    serverIP?: string;
    colo?: string;
    timezone?: string;
    latitude?: number;
    longitude?: number;
  };
  // User location information
  userLocation?: UserLocation;
  // Distance between user and test server
  distance?: number; // km
  // Additional Cloudflare metrics
  loadedLatency?: number; // ms - loaded latency
  loadedJitter?: number; // ms - loaded jitter
  aimScores?: {
    gaming?: number | { points: number; classificationIdx: number; classificationName: string };
    streaming?: number | { points: number; classificationIdx: number; classificationName: string };
    rtc?: number | { points: number; classificationIdx: number; classificationName: string };
  };
}

export interface UseNetworkTestReturn {
  isTestingNetwork: boolean;
  networkResult: NetworkTestResult | null;
  error: string | null;
  startNetworkTest: () => Promise<void>;
  testProgress: number; // 0-100
  currentPhase: 'idle' | 'latency' | 'download' | 'upload' | 'packetLoss' | 'completed';
  // 实时数据状态
  realTimeData: {
    downloadSpeed: number; // Mbps
    uploadSpeed: number; // Mbps
    latency: number; // ms
    jitter: number; // ms
  };
  // 位置信息加载状态
  isLoadingLocation: boolean;
}

export const useNetworkTest = (): UseNetworkTestReturn => {
  const [isTestingNetwork, setIsTestingNetwork] = useState(false);
  const [networkResult, setNetworkResult] = useState<NetworkTestResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [testProgress, setTestProgress] = useState(0);
  const [currentPhase, setCurrentPhase] = useState<UseNetworkTestReturn['currentPhase']>('idle');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  
  // 实时数据状态
  const [realTimeData, setRealTimeData] = useState({
    downloadSpeed: 0,
    uploadSpeed: 0,
    latency: 0,
    jitter: 0
  });

  const speedTestRef = useRef<any>(null);

  const calculateQuality = (result: Omit<NetworkTestResult, 'quality'>): NetworkTestResult['quality'] => {
    const { latency, downloadSpeed, uploadSpeed, jitter } = result;

    // Cloudflare-like quality assessment
    let score = 0;

    // Latency scoring (40% weight) - More strict for real-time apps
    if (latency < 20) score += 40;
    else if (latency < 50) score += 35;
    else if (latency < 100) score += 25;
    else if (latency < 150) score += 15;
    else if (latency < 300) score += 5;

    // Download speed scoring (30% weight)
    if (downloadSpeed > 25) score += 30;
    else if (downloadSpeed > 10) score += 25;
    else if (downloadSpeed > 5) score += 20;
    else if (downloadSpeed > 1) score += 15;
    else if (downloadSpeed > 0.5) score += 10;

    // Upload speed scoring (20% weight)
    if (uploadSpeed > 5) score += 20;
    else if (uploadSpeed > 2) score += 15;
    else if (uploadSpeed > 1) score += 12;
    else if (uploadSpeed > 0.5) score += 8;
    else if (uploadSpeed > 0.1) score += 5;

    // Jitter scoring (10% weight)
    if (jitter < 5) score += 10;
    else if (jitter < 15) score += 8;
    else if (jitter < 30) score += 5;
    else if (jitter < 50) score += 2;

    // Quality classification
    if (score >= 85) return 'excellent';
    else if (score >= 70) return 'good';
    else if (score >= 50) return 'fair';
    else return 'poor';
  };

  const startNetworkTest = useCallback(async () => {
    setIsTestingNetwork(true);
    setError(null);
    setTestProgress(0);
    setCurrentPhase('latency');

    // 重置实时数据
    setRealTimeData({
      downloadSpeed: 0,
      uploadSpeed: 0,
      latency: 0,
      jitter: 0
    });

    // 获取用户位置（异步，不阻塞测试）
    let userLocationPromise: Promise<UserLocation | null>;
    try {
      userLocationPromise = getUserLocation();
    } catch (error) {
      console.warn('Failed to start user location detection:', error);
      userLocationPromise = Promise.resolve(null);
    }

    try {
      // Initialize Cloudflare SpeedTest with custom configuration
      const speedTest = new SpeedTest({
        autoStart: false, // We want to control the start
        measurements: [
          // Latency measurements
          { type: 'latency', numPackets: 20 },
          
          // Download measurements - progressive sizing
          { type: 'download', bytes: 1e6, count: 3 }, // 1MB x 3
          { type: 'download', bytes: 1e7, count: 3 }, // 10MB x 3
          { type: 'download', bytes: 2.5e7, count: 2 }, // 25MB x 2
          
          // Upload measurements - progressive sizing
          { type: 'upload', bytes: 1e6, count: 3 }, // 1MB x 3
          { type: 'upload', bytes: 1e7, count: 2 }, // 10MB x 2
          
          // Packet loss measurement (requires TURN server)
          // { type: 'packetLoss', numPackets: 100 },
        ],
        measureDownloadLoadedLatency: true,
        measureUploadLoadedLatency: true,
      });

      speedTestRef.current = speedTest;

      // Track results changes to update progress
      speedTest.onResultsChange = (change: any) => {
        console.log('Results changed:', change);
        
        // Update progress based on change type
        if (change.type === 'latency') {
          setCurrentPhase('latency');
          setTestProgress(15);
          
          // 🎯 显示真实延迟数据（滑动平均）
          const latencyPoints = speedTest.results.getUnloadedLatencyPoints();
          if (latencyPoints && latencyPoints.length > 0) {
            const recentLatency = latencyPoints.slice(-3); // 最近3次测量
            const avgLatency = recentLatency.reduce((sum, lat) => sum + lat, 0) / recentLatency.length;
            
            // 更新抖动（如果有足够的数据点）
            let currentJitter = 0;
            if (latencyPoints.length >= 2) {
              const jitter = speedTest.results.getUnloadedJitter();
              if (jitter !== null && jitter !== undefined) {
                currentJitter = jitter;
              }
            }
            
            // 更新实时数据状态
            setRealTimeData(prev => ({
              ...prev,
              latency: avgLatency,
              jitter: currentJitter
            }));
          }
          
        } else if (change.type === 'download') {
          setCurrentPhase('download');
          const downloadPoints = speedTest.results.getDownloadBandwidthPoints();
          setTestProgress(30 + Math.min(30, downloadPoints.length * 5));
          
          // 🎯 显示真实下载速度（滑动平均）
          if (downloadPoints && downloadPoints.length > 0) {
            const recentPoints = downloadPoints.slice(-3); // 最近3次测量
            const avgSpeed = recentPoints.reduce((sum, point) => 
              sum + (point.bps / (1024 * 1024 * 8)), 0) / recentPoints.length;
            
            // 更新实时数据状态
            setRealTimeData(prev => ({ ...prev, downloadSpeed: avgSpeed }));
            
            console.log(`实时下载速度（3次平均）: ${avgSpeed.toFixed(2)} Mbps`, {
              totalPoints: downloadPoints.length,
              recentPoints: recentPoints.map(p => ({
                speed: (p.bps / (1024 * 1024 * 8)).toFixed(2) + ' Mbps',
                duration: p.duration + 'ms',
                bytes: (p.bytes / (1024 * 1024)).toFixed(1) + 'MB'
              }))
            });
          }
          
        } else if (change.type === 'upload') {
          setCurrentPhase('upload');
          const uploadPoints = speedTest.results.getUploadBandwidthPoints();
          setTestProgress(60 + Math.min(30, uploadPoints.length * 5));
          
          // 🎯 显示真实上传速度（滑动平均）
          if (uploadPoints && uploadPoints.length > 0) {
            const recentPoints = uploadPoints.slice(-3); // 最近3次测量
            const avgSpeed = recentPoints.reduce((sum, point) => 
              sum + (point.bps / (1024 * 1024 * 8)), 0) / recentPoints.length;
            
            // 更新实时数据状态
            setRealTimeData(prev => ({ ...prev, uploadSpeed: avgSpeed }));
            
            console.log(`实时上传速度（3次平均）: ${avgSpeed.toFixed(2)} Mbps`, {
              totalPoints: uploadPoints.length,
              recentPoints: recentPoints.map(p => ({
                speed: (p.bps / (1024 * 1024 * 8)).toFixed(2) + ' Mbps',
                duration: p.duration + 'ms',
                bytes: (p.bytes / (1024 * 1024)).toFixed(1) + 'MB'
              }))
            });
          }
          
        } else if (change.type === 'packetLoss') {
          setCurrentPhase('packetLoss');
          setTestProgress(95);
        }
      };

      // Handle completion
      speedTest.onFinish = async (results: any) => {
        try {
          setCurrentPhase('completed');
          setTestProgress(100);

          console.log('Cloudflare SpeedTest completed, processing results...');

          // 先创建基本的测试结果，立即显示给用户
          const basicNetworkResult: NetworkTestResult = {
            latency: results.getUnloadedLatency() || 0,
            downloadSpeed: (results.getDownloadBandwidth() || 0) / (1024 * 1024 * 8), // Convert bps to Mbps
            uploadSpeed: (results.getUploadBandwidth() || 0) / (1024 * 1024 * 8), // Convert bps to Mbps
            packetLoss: (results.getPacketLoss() || 0) * 100, // Convert ratio to percentage
            jitter: results.getUnloadedJitter() || 0,
            testRegion: 'Cloudflare Edge Network',
            loadedLatency: results.getDownLoadedLatency() || results.getUpLoadedLatency(),
            loadedJitter: results.getDownLoadedJitter() || results.getUpLoadedJitter(),
            aimScores: (() => {
              try {
                const scores = results.getScores();
                console.log('Raw AIM scores from Cloudflare:', scores);
                return scores;
              } catch (err) {
                console.warn('Failed to get AIM scores:', err);
                return undefined;
              }
            })(),
            quality: 'good' // Will be calculated below
          };

          // Calculate quality based on all metrics
          basicNetworkResult.quality = calculateQuality(basicNetworkResult);

          // 立即设置基本结果，让用户看到测试完成
          setNetworkResult(basicNetworkResult);
          setIsTestingNetwork(false);

          console.log('Basic SpeedTest results:', {
            summary: results.getSummary(),
            detailed: basicNetworkResult
          });

          // 异步加载位置信息
          setIsLoadingLocation(true);
          console.log('Loading location information asynchronously...');

          Promise.all([
            getCloudflareLocationInfo(),
            userLocationPromise
          ]).then(([locationInfo, userLocation]) => {
            // 计算距离（如果两个位置都有坐标）
            let distance: number | undefined;
            if (userLocation?.latitude && userLocation?.longitude &&
                locationInfo?.latitude && locationInfo?.longitude) {
              distance = calculateDistance(
                userLocation.latitude,
                userLocation.longitude,
                locationInfo.latitude,
                locationInfo.longitude
              );
            }

            // 更新结果，添加位置信息
            const updatedResult: NetworkTestResult = {
              ...basicNetworkResult,
              testRegion: locationInfo?.edgeLocation ?
                `Cloudflare ${locationInfo.city || locationInfo.edgeLocation} (${locationInfo.edgeLocation})` :
                'Cloudflare Edge Network',
              testLocation: locationInfo || undefined,
              userLocation: userLocation || undefined,
              distance: distance
            };

            setNetworkResult(updatedResult);
            setIsLoadingLocation(false);

            console.log('Location information loaded:', {
              locationInfo,
              userLocation,
              distance
            });
          }).catch((err) => {
            console.warn('Failed to load location information:', err);
            setIsLoadingLocation(false);
          });

        } catch (err) {
          console.error('Error processing SpeedTest results:', err);
          setError('Failed to process test results: ' + (err as Error).message);
          setIsTestingNetwork(false);
          setCurrentPhase('idle');
          setIsLoadingLocation(false);
        }
      };

      // Handle errors
      speedTest.onError = (err: any) => {
        console.error('Cloudflare SpeedTest error:', err);
        setError('SpeedTest failed: ' + (err.message || 'Unknown error'));
        setIsTestingNetwork(false);
        setCurrentPhase('idle');
      };

      // Handle running state changes
      speedTest.onRunningChange = (isRunning: boolean) => {
        console.log('SpeedTest running state changed:', isRunning);
        if (!isRunning && !speedTest.isFinished) {
          // Test stopped but not finished - might be an error
          setIsTestingNetwork(false);
          if (!networkResult) {
            setCurrentPhase('idle');
          }
        }
      };

      // Start the test using play() method
      console.log('Starting Cloudflare SpeedTest...');
      speedTest.play();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Failed to initialize SpeedTest:', err);
      setError('Failed to start network test: ' + errorMessage);
      setIsTestingNetwork(false);
      setCurrentPhase('idle');
    }
  }, [networkResult]);

  // Cleanup function to abort ongoing tests
  const abortNetworkTest = useCallback(() => {
    if (speedTestRef.current) {
      try {
        speedTestRef.current.pause();
      } catch (err) {
        console.warn('Error pausing SpeedTest:', err);
      }
      speedTestRef.current = null;
    }
    
    setIsTestingNetwork(false);
    setCurrentPhase('idle');
    setTestProgress(0);
  }, []);

  return {
    isTestingNetwork,
    networkResult,
    error,
    startNetworkTest,
    testProgress,
    currentPhase,
    realTimeData,
    isLoadingLocation,
    // Expose abort function for cleanup
    abortNetworkTest
  } as UseNetworkTestReturn & { abortNetworkTest: () => void };
};