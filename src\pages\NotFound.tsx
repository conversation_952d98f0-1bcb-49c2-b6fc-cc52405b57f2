import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { useLanguage } from "@/hooks/useLanguage";

const NotFound = () => {
  const location = useLocation();
  
  // Safely access language context with fallback
  let t: (key: string) => string;
  try {
    const languageContext = useLanguage();
    t = languageContext.t;
  } catch (error) {
    // Fallback when LanguageProvider is not available
    t = (key: string) => {
      // Use English as fallback
      const fallbackTexts: Record<string, string> = {
        pageNotFound: "404",
        oopsPageNotFound: "Oops! Page not found",
        returnToHome: "Return to Home"
      };
      return fallbackTexts[key] || key;
    };
  }

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">{t("pageNotFound")}</h1>
        <p className="text-xl text-gray-600 mb-4">{t("oopsPageNotFound")}</p>
        <a href="/" className="text-blue-500 hover:text-blue-700 underline">
          {t("returnToHome")}
        </a>
      </div>
    </div>
  );
};

export default NotFound;
