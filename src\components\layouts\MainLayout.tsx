import React from "react";
import { Navigation } from "../ui/Navigation";
import { SEOBreadcrumb } from "../ui/SEOBreadcrumb";
import { cn } from "@/lib/utils";
import { useSEO, SEOConfig } from "@/hooks/useSEO";
import { useLanguage } from "@/hooks/useLanguage";

interface MainLayoutProps {
  children: React.ReactNode;
  fullWidth?: boolean;
  seoConfig?: SEOConfig;
  showBreadcrumb?: boolean;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  fullWidth = false,
  seoConfig,
  showBreadcrumb = true,
}) => {
  const { t } = useLanguage();

  // 使用SEO Hook，如果没有提供seoConfig则使用默认配置
  const defaultSEOConfig: SEOConfig = {
    title: t("defaultSEOTitle"),
    description: t("defaultSEODescription"),
  };

  useSEO(seoConfig || defaultSEOConfig);

  return (
    <div className="min-h-screen w-full">
      <Navigation />
      <main
        className={cn(
          "px-4 sm:px-6 lg:px-8 py-6 sm:py-8 pt-24 sm:pt-28",
          !fullWidth && "container mx-auto max-w-6xl"
        )}
      >
      {showBreadcrumb && <SEOBreadcrumb />}
        {children}
      </main>
    </div>
  );
};