// 翻译对象的类型定义
export interface TranslationKeys {
  // 基础导航
  home: string;
  tools: string;
  meetingTest: string;
  keyboardTest: string;
  mouseTest: string;
  headphonesTest: string;
  siteName: string;
  siteSubtitle: string;
  selectScenario: string;
  onlineMeeting: string;
  onlineMeetingDesc: string;
  startTest: string;
  microphoneTest: string;
  speakerTest: string;
  cameraTest: string;
  next: string;
  back: string;
  finish: string;
  
  // ToolsPage 相关
  deviceTestingTools: string;
  deviceTestingToolsDescription: string;
  testingTools: string;
  hardwareTesting: string;
  deviceTools: string;
  audioQuality: string;
  noiseLevel: string;
  sensitivity: string;
  stereoBalance: string;
  volumeLevel: string;
  audioOutput: string;
  keyResponse: string;
  keyMapping: string;
  typingSpeed: string;
  clickAccuracy: string;
  scrollFunction: string;
  internetSpeed: string;
  connectionStability: string;
  quickTest: string;
  needHelp: string;
  testingToolsDescription: string;
  noRegistration: string;
  accuracy: string;
  free: string;
  
  // 麦克风测试
  micTestTitle: string;
  micTestDesc: string;
  selectMicrophone: string;
  microphoneWorking: string;
  microphoneNotDetected: string;
  
  // 扬声器测试
  speakerTestTitle: string;
  speakerTestDesc: string;
  playTestSound: string;
  canYouHear: string;
  
  // 摄像头测试
  cameraTestTitle: string;
  cameraTestDesc: string;
  selectCamera: string;
  cameraWorking: string;
  cameraNotDetected: string;
  cameraTestModule: string;
  smartScorecard: string;
  startTestToSeeScore: string;
  resolution: string;
  frameRate: string;
  colorBrightness: string;
  overallRating: string;
  takePhoto: string;
  mirror: string;
  fullscreen: string;
  downloadPhoto: string;
  deletePhoto: string;
  photoPreview: string;
  cameraCapture: string;
  deviceName: string;
  realTimeInfo: string;
  
  // 键盘测试
  keyboardTestTitle: string;
  keyboardTestDesc: string;
  pressAnyKey: string;
  keyPressed: string;
  
  // 鼠标测试
  mouseTestTitle: string;
  mouseTestDesc: string;
  leftClick: string;
  rightClick: string;
  middleClick: string;
  scrollUp: string;
  scrollDown: string;
  clickButtons: string;
  
  // 测试结果
  testComplete: string;
  allTestsPassed: string;
  copyReport: string;
  reportCopied: string;
  
  // 页面内容
  gamingSetup: string;
  gamingSetupDesc: string;
  audioTest: string;
  recommended: string;
  comingSoon: string;
  individualDeviceTests: string;
  stepOf: string;
  invalidScenario: string;
  runTestsAgain: string;
  
  // 测试组件
  cameraTestTitle2: string;
  cameraTestDesc2: string;
  selectCamera2: string;
  cameraPreview: string;
  cameraPlaceholder: string;
  startCameraTest: string;
  stopCamera: string;
  cameraWorking2: string;
  cameraWorkingNormally: string;
  cameraStartedSuccessfully: string;
  analyzingVideoQuality: string;
  cameraWorkingDesc: string;
  cameraTips: string;
  cameraTip1: string;
  cameraTip2: string;
  cameraTip3: string;
  cameraTip4: string;
  backSpeakerTest: string;
  finishTesting: string;
  
  keyboardTestTitle2: string;
  keyboardTestDesc2: string;
  resetTest: string;
  backToHome: string;
  recentKeyPresses: string;
  space: string;
  testingTips: string;
  keyboardTip1: string;
  keyboardTip2: string;
  keyboardTip3: string;
  keyboardTip4: string;
  
  micTestTitle2: string;
  micTestDesc2: string;
  audioLevel: string;
  micInstructions: string;
  micInstructionsStart: string;
  startMicTest: string;
  stopTest: string;
  nextSpeakerTest: string;
  
  // 鼠标测试
  mouseTestTitle2: string;
  mouseTestDesc2: string;
  testArea: string;
  moveClickScroll: string;
  tryMouseButtons: string;
  position: string;
  buttonStatus: string;
  leftButton: string;
  wheelButton: string;
  middleButton: string;
  rightButton: string;
  sideButton1: string;
  sideButton2: string;
  pressed: string;
  released: string;
  eventHistory: string;
  noMouseEvents: string;
  startMovingClicking: string;
  scrollDown2: string;
  scrollUp2: string;
  mouseTip1: string;
  mouseTip2: string;
  mouseTip3: string;
  mouseTip4: string;
  mouseTip5: string;
  
  // 扬声器测试
  speakerTestTitle2: string;
  speakerTestDesc2: string;
  volumeLevel: string;
  volume: string;
  testAudioPlayback: string;
  testAudioDesc: string;
  playingTestSound: string;
  playTestSound2: string;
  playingTone: string;
  troubleshootingTips: string;
  speakerTip1: string;
  speakerTip2: string;
  speakerTip3: string;
  speakerTip4: string;
  backMicrophone: string;
  nextCameraTest: string;
  
  // 耳机与扬声器测试
  headphonesTestTitle: string;
  headphonesTestDesc: string;
  outputDeviceSelector: string;
  noOutputDevices: string;
  leftRightChannelTest: string;
  leftRightChannelDesc: string;
  playLeft: string;
  playRight: string;
  playingLeft: string;
  playingRight: string;
  frequencyResponseTest: string;
  frequencyResponseDesc: string;
  startSweep: string;
  sweepInProgress: string;
  frequencyTestTip: string;
  dynamicRangeTest: string;
  dynamicRangeDesc: string;
  dynamicRangeTestTip: string;
  stereoImagingTest: string;
  stereoImagingDesc: string;
  play3dAudio: string;
  audioPlaying3d: string;
  stereoImagingTestTip: string;
  stopAllAudio: string;
  testInProgress: string;
  testNotStarted: string;
  testFailed: string;
  testSkipped: string;
  step: string;
  of: string;
  skip: string;
  confirmSkipTest: string;
  skipTestWarning: string;
  confirmSkip: string;
  cancel: string;
  canSkipThisTest: string;
  stopPlaying: string;
  audioError: string;
  stopSweep: string;
  stopTestButton: string;
  startTestButton: string;
  testInstructions: string;
  testInstructionItem1: string;
  testInstructionItem2: string;
  testInstructionItem3: string;
  testInstructionItem4: string;
  testInstructionItem5: string;
  dynamicRangeTestInProgress: string;
  quietSound: string;
  loudSound: string;
  bassDetail: string;
  backgroundEffects: string;
  midrangeLayer: string;
  voiceDialogue: string;
  trebleImpact: string;
  explosionEffects: string;
  frontDirection: string;
  backDirection: string;
  leftDirection: string;
  rightDirection: string;
  leftChannel: string;
  rightChannel: string;
  center: string;
  ultraLowBass: string;
  lowBass: string;
  midLowBass: string;
  midrange: string;
  midTreble: string;
  treble: string;
  ultraTreble: string;
  bassDrumBass: string;
  voiceBase: string;
  voiceClarity: string;
  detailAiriness: string;
  testProgress: string;
  leftChannelTest: string;
  rightChannelTest: string;
  frequencyTest: string;
  dynamicRangeTest: string;
  stereoTest: string;
  basicTestRequired: string;
  basicTestCompleted: string;
  
  // 总结和报告
  testResultsSummary: string;
  microphoneTestResult: string;
  speakerTestResult: string;
  cameraTestResult: string;
  testPassed: string;
  deviceTestReport: string;
  generated: string;
  scenario: string;
  browserInfo: string;
  testsCompleted: string;
  allHardwareTestsComplete: string;
  deviceReadyForMeetings: string;
  copyFailed: string;
  copyFailedDesc: string;
  
  // UI 组件
  close: string;
  previousButton: string;
  nextButton: string;
  more: string;
  previousSlide: string;
  nextSlide: string;
  morePages: string;
  toggleSidebar: string;
  
  // 404 页面
  pageNotFound: string;
  oopsPageNotFound: string;
  returnToHome: string;
  
  // 麦克风模块特定
  microphoneAccess: string;
  initializingMicrophone: string;
  micAccessDenied: string;
  micAccessDeniedDesc: string;
  howToEnableMic: string;
  micPermissionStep1: string;
  micPermissionStep2: string;
  micPermissionStep3: string;
  micPermissionStep4: string;
  retry: string;
  readyToTest: string;
  readyToTestDesc: string;
  testContent: string;
  testContentItem1: string;
  testContentItem2: string;
  testContentItem3: string;
  testContentItem4: string;
  testContentItem5: string;
  startMicrophoneTest: string;
  
  // 麦克风测试界面
  audioVisualization: string;
  realTimeWaveform: string;
  startSpeaking: string;
  usageTip: string;
  microphoneStatus: string;
  micConnected: string;
  speakIntoMic: string;
  deviceWorking: string;
  volumeDetection: string;
  lowLevel: string;
  goodLevel: string;
  overload: string;
  dbLow: string;
  dbOverload: string;
  goodRange: string;
  
  // 实时监听
  realTimeMonitoring: string;
  monitoring: string;
  closed: string;
  monitoringDesc: string;
  warningHeadphones: string;
  preventFeedback: string;
  audioDelay: string;
  avoidOverlap: string;
  monitoringVolume: string;
  notAffectRecording: string;
  speaker: string;
  monitoringEnabled: string;
  adjustSettings: string;
  
  // 录音测试
  recordingTest: string;
  startRecording: string;
  stopRecording: string;
  stopPlayback: string;
  playRecording: string;
  recording: string;
  playing: string;
  playbackProgress: string;
  playbackComplete: string;
  paused: string;
  recordingInstructions: string;
  advancedTest: string;
  
  // 设备信息
  deviceInfo: string;
  sampleRate: string;
  channels: string;
  mono: string;
  stereo: string;
  bitDepth: string;
  
  // 高级设置
  advancedSettings: string;
  whenToAdjust: string;
  echoCancellation: string;
  echoCancellationDesc: string;
  noiseSuppression: string;
  noiseSuppressionDesc: string;
  autoGainControl: string;
  autoGainControlDesc: string;
  realTimeEffect: string;
  enabled: string;
  disabled: string;
  preventEcho: string;
  mayEcho: string;
  filterNoise: string;
  improveQuality: string;
  autoAdjustVolume: string;
  naturalVariation: string;
  
  // 测试建议
  testSuggestions: string;
  defaultFirst: string;
  compareSettings: string;
  quietEnvironment: string;
  speakerEcho: string;
  
  stopTestButton: string;
  backToHomeButton: string;
  nextSpeakerTestButton: string;
  
  // 双击检测功能
  startDoubleClickDetection: string;
  stopMonitoring: string;
  doubleClickDetectionInProgress: string;
  duration: string;
  detectedSevereHardwareFailure: string;
  mouseMayHaveIssues: string;
  sporadicIssuesContinueMonitoring: string;
  mouseWorkingNormally: string;
  detected: string;
  issues: string;
  performSingleClickTest: string;
  tryNormalSingleClickOperations: string;
  systemWillAutoDetectDoubleClickIssues: string;
  doubleClickIssueDetection: string;
  noDoubleClickIssuesDetected: string;
  continueWithSingleClickTesting: string;
  severeHardwareFailureShortInterval: string;
  possibleHardwareFailureUnexpectedDoubleClick: string;
  potentialIssueFastDoubleClick: string;
  interval: string;
  normal: string;
  doubleClickDetectionInstructions: string;
  doubleClickTip1: string;
  doubleClickTip2: string;
  doubleClickTip3: string;
  doubleClickTip4: string;
  doubleClickTip5: string;
  doubleClickTip6: string;
  
  // TestWorkflowPage 专用翻译键
  networkQualityTest: string;
  notTested: string;
  failed: string;
  failureReason: string;
  onlineMeetingDeviceReport: string;
  generatedTime: string;
  testScenario: string;
  onlineMeetingScenario: string;
  unknownScenario: string;
  testCompletionStatus: string;
  testCompletionCount: string;
  testResultsTitle: string;
  allHardwareTestsPassed: string;
  testsPassed: string;
  partialTestsCompleted: string;
  checkFailedTests: string;
  suggestions: string;
  suggestion1: string;
  suggestion2: string;
  suggestion3: string;
  suggestion4: string;
  retestFailedDevices: string;
  testResults: string;
  testsCompletedCount: string;
  meetingDeviceTestReport: string;
  reason: string;
  allDevicesReady: string;
  someTestsFailed: string;
  completeAllTests: string;
  
  // Network test features
  networkTestDesc: string;
  networkQualityTestDesc: string;
  testProgress: string;
  testingNetworkLatency: string;
  networkQualityAssessment: string;
  excellent: string;
  good: string;
  fair: string;
  poor: string;
  latency: string;
  download: string;
  upload: string;
  jitter: string;
  recommendations: string;
  networkExcellentDesc: string;
  networkGoodDesc: string;
  networkFairDesc: string;
  networkPoorDesc: string;
  serverIP: string;
  timezone: string;
  userLocation: string;
  serverLocation: string;
  distance: string;
  approximateDistance: string;
  gpsLocation: string;
  ipLocation: string;
  timezoneLocation: string;
  unknownLocation: string;
  loadingLocation: string;
  server: string;
  
  // Enhanced network test features
  testingLatency: string;
  testingDownload: string;
  testingUpload: string;
  testingPacketLoss: string;
  testCompleted: string;
  preparingTest: string;
  aimScores: string;
  gaming: string;
  streaming: string;
  realTimeCommunication: string;
  loadedMetrics: string;
  loadedLatency: string;
  loadedJitter: string;
  networkNotTested: string;
  latencyTooHigh: string;
  downloadSpeedTooSlow: string;
  uploadSpeedTooSlow: string;
  jitterTooHigh: string;
  startNetworkTest: string;
  retestNetwork: string;
  networkOptimizationTips: string;
  networkTip1: string;
  networkTip2: string;
  networkTip3: string;
  networkTip4: string;
  testRegion: string;
  nextMicrophoneTest: string;
  
  // Enhanced speaker test
  enhancedSpeakerTest: string;
  comprehensiveAudioTest: string;
  volumeControl: string;
  stereoTest: string;
  leftChannel: string;
  rightChannel: string;
  bothChannels: string;
  testLeftRightChannels: string;
  confirmStereoTestPassed: string;
  frequencySweep: string;
  sweeping: string;
  testTone1kHz: string;
  testSpeakerFrequencyRange: string;
  confirmFrequencyTestPassed: string;
  speakerTestComplete: string;
  allAudioTestsPassed: string;
  audioOptimizationTips: string;
  audioTip1: string;
  audioTip2: string;
  audioTip3: string;
  audioTip4: string;
  
  // Enhanced camera test
  enhancedCameraTest: string;
  comprehensiveVideoTest: string;
  cameraPermissionDeniedDesc: string;
  howToEnableCamera: string;
  cameraPermissionStep1: string;
  cameraPermissionStep2: string;
  cameraPermissionStep3: string;
  cameraPermissionStep4: string;
  readyToTestCamera: string;
  readyToTestCameraDesc: string;
  cameraTestContent: string;
  cameraTestContentItem1: string;
  cameraTestContentItem2: string;
  cameraTestContentItem3: string;
  cameraTestContentItem4: string;
  cameraTestContentItem5: string;
  cameraStatus: string;
  cameraConnected: string;
  lookIntoCameraTest: string;
  videoQualityAnalysis: string;
  brightness: string;
  contrast: string;
  sharpness: string;
  videoQualityExcellentDesc: string;
  videoQualityGoodDesc: string;
  videoQualityFairDesc: string;
  videoQualityPoorDesc: string;
  videoOptimizationTips: string;
  videoTip1: string;
  videoTip2: string;
  videoTip3: string;
  videoTip4: string;
  completeTest: string;
  
  // Enhanced microphone test
  enhancedMicrophoneTest: string;
  comprehensiveMicTest: string;
  audioQualityAnalysis: string;
  signalToNoiseRatio: string;
  backgroundNoiseLevel: string;
  distortionLevel: string;
  echoDetection: string;
  notDetected: string;
  micExcellentDesc: string;
  micGoodDesc: string;
  micFairDesc: string;
  micPoorDesc: string;
  echoDetectedWarning: string;
  micOptimizationTips: string;
  micTip1: string;
  micTip2: string;
  micTip3: string;
  micTip4: string;
  
  // Test failure reasons
  networkQualityPoor: string;
  micPermissionDenied: string;
  micPermissionNotGranted: string;
  noMicrophoneDevices: string;
  noAudioInput: string;
  audioQualityIssues: string;
  signalToNoiseRatioLow: string;
  backgroundNoiseTooHigh: string;
  audioDistortionHigh: string;
  echoDetected: string;
  audioQualityPoor: string;
  requiredTestsNotCompleted: string;
  stereoTestNotCompleted: string;
  frequencyTestNotCompleted: string;
  cameraPermissionDenied: string;
  noCameraDevices: string;
  cameraNotStarted: string;
  cameraError: string;
  videoQualityIssues: string;
  lightingTooDark: string;
  lightingTooBright: string;
  contrastTooLow: string;
  imageBlurry: string;
  videoQualityPoor: string;
  
  // Meeting device test complete
  meetingDeviceTestComplete: string;
  
  // Gaming Setup Check
  gamingSetupCheckTitle: string;
  gamingScenario: string;
  gamingDeviceTestReport: string;
  gamingDeviceTestComplete: string;
  allGamingTestsPassed: string;
  gamingNetworkTestDesc: string;
  gamingKeyboardTestDesc: string;
  gamingMouseTestDesc: string;
  gamingAudioTestDesc: string;
  keyboardResponseTime: string;
  mouseAccuracy: string;
  audioLatency: string;
  peripheralPerformance: string;
  allPeripheralsReady: string;
  somePeripheralsFailed: string;
  completeAllGamingTests: string;
  retestFailedPeripherals: string;
  optimizeGamingSetup: string;
  gamingTip1: string;
  gamingTip2: string;
  gamingTip3: string;
  gamingTip4: string;
  gamingTip5: string;

  // 麦克风测试模块新增翻译键
  micPermissionDeniedTitle: string;
  micPermissionDeniedMessage: string;
  enableMicPermissionInstructions: string;
  enableMicStep1: string;
  enableMicStep2: string;
  enableMicStep3: string;
  retryTest: string;
  waitingConnection: string;
  startTestingButton: string;
  waitingTesting: string;
  deviceReady: string;
  applyingNewSettings: string;
  settingsApplied: string;
  applySettingsError: string;
  realTimeAudioDelay: string;
  seconds: string;
  preventSoundOverlap: string;
  adjustAnytime: string;
  playingInProgress: string;
  recordingCompleted: string;
  playbackStopped: string;
  microphoneInformation: string;
  gettingDeviceInfo: string;
  pleaseTurnOnTest: string;
  whyAdjustSettings: string;
  echoWhenUsingSpeaker: string;
  noisyBackground: string;
  unstableVolume: string;
  settingsApplyImmediately: string;
  toggleOn: string;
  toggleOff: string;
  useHeadphonesToPreventEcho: string;
  improveCallQuality: string;
  hearNaturalVolumeChanges: string;
  testSuggestionsTitle: string;
  defaultSettingsFirst: string;
  compareEachSetting: string;
  noiseSuppressionTip: string;
  echoCancellationTip: string;
  returnButton: string;

  // SEO相关类型
  siteDescription: string;
  siteKeywords: string;
  homePageDescription: string;
  toolsPageDescription: string;
  cameraTestDescription: string;
  microphoneTestDescription: string;
  headphonesTestDescription: string;
  keyboardTestDescription: string;
  mouseTestDescription: string;
  networkTestDescription: string;
  meetingTestDescription: string;
  gamingTestDescription: string;

  // Streaming & Content Creation Scenario
  streamingScenario: string;
  streamingScenarioDesc: string;
  streamingSetupCheckTitle: string;
  streamingDeviceTestReport: string;
  streamingDeviceTestComplete: string;
  allStreamingTestsPassed: string;
  streamingOptimizationTips: string;
  streamingTip1: string;
  streamingTip2: string;
  streamingTip3: string;
  streamingTip4: string;
  streamingTip5: string;
  streamingTestDescription: string;

  // Device Diagnostic Scenario
  diagnosticScenario: string;
  diagnosticScenarioDesc: string;
  diagnosticSetupCheckTitle: string;
  diagnosticDeviceTestReport: string;
  diagnosticDeviceTestComplete: string;
  allDiagnosticTestsPassed: string;
  diagnosticTroubleshootingTips: string;
  diagnosticTip1: string;
  diagnosticTip2: string;
  diagnosticTip3: string;
  diagnosticTip4: string;
  diagnosticTip5: string;
  diagnosticTestDescription: string;

  // Error Messages - Camera
  cameraAccessDenied: string;
  cameraNotFound: string;
  cameraAccessFailed: string;

  // Error Messages - Microphone
  microphoneAccessDenied: string;
  microphoneAccessFailed: string;
  recordingStartFailed: string;

  // SEO Validation Messages
  seoTitleMissing: string;
  seoTitleTooLong: string;
  seoTitleTooShort: string;
  seoDescriptionMissing: string;
  seoDescriptionTooLong: string;
  seoDescriptionTooShort: string;

  // Default SEO Configuration
  defaultSEOTitle: string;
  defaultSEODescription: string;

  // Console Messages
  setLanguageWarning: string;
  invalidLanguageCode: string;

  // Scenario Page Labels
  includedTests: string;
  completeHardwareCheck: string;
  needIndividualTesting: string;
  individualTestingDesc: string;
  browseIndividualTools: string;
  selectScenarioDesc: string;

  // Scenario Durations
  duration5to8: string;
  duration8to12: string;
  duration6to10: string;
  duration10to15: string;

  // Difficulty Levels
  difficultyBeginner: string;
  difficultyAdvanced: string;
  difficultyIntermediate: string;
  difficultyComprehensive: string;

  // Usage Descriptions
  usageMeeting: string;
  usageGaming: string;
  usageStreaming: string;
  usageDiagnostic: string;

  // Keyboard Test Module
  keyboardTestInstructions: string;
  keyboardTestInstruction1: string;
  keyboardTestInstruction2: string;
  keyboardTestInstruction3: string;
  keyboardTestInstruction4: string;
  startKeyboardTest: string;
  keyboardTestingActive: string;
  keyboardTestStopped: string;
  keyPressCount: string;
  averageLatency: string;
  keyCoverage: string;
  keyTestStatus: string;
  recentKeys: string;
  stopKeyboardTest: string;
  continueKeyboardTest: string;
  resetKeyboardTest: string;
  keyboardTestComplete: string;
  keyboardTestCompleteDesc: string;
  insufficientKeyCoverage: string;
  highInputLatency: string;
  notEnoughKeyPresses: string;

  // Keyboard Latency Test (KeyboardTest.tsx)
  keyboardLatencyTest: string;
  testKeyboardResponseTime: string;
  latencyShortestLatency: string;
  latencyAverageLatency: string;
  latencyScanRate: string;
  latencyConnection: string;
  latencyNoData: string;
  latencyPresses: string;
  latencyMaxLatency: string;
  latencyHistory: string;
  latencyTestInstructions: string;
  latencyInstruction1: string;
  latencyInstruction2: string;
  latencyInstruction3: string;
  latencyInstruction4: string;
  latencyInstruction5: string;
  latencyPerformanceAssessment: string;
  latencyOverallRating: string;
  latencyRecommendation: string;
  latencyKeyCharacteristics: string;
  latencyEstimatedPollingRate: string;
  latencyConnectionType: string;
  latencyResponseConsistency: string;
  latencyVeryConsistent: string;
  latencyVariable: string;
  latencyExcellent: string;
  latencyVeryGood: string;
  latencyGood: string;
  latencyAverage: string;
  latencyPoor: string;
  latencyExcellentForGaming: string;
  latencyGoodForGeneral: string;
  latencyConsiderUpgrading: string;
  latencyUsbEstimated: string;
  latencyUnknown: string;

  // 隐私政策和 Cookie 政策
  privacyPolicy: string;
  cookiePolicy: string;
  privacyPolicyTitle: string;
  cookiePolicyTitle: string;
  lastUpdated: string;
  effectiveDate: string;
  contactUs: string;
  backToHome: string;

  // Cookie 同意相关
  cookieConsentTitle: string;
  cookieConsentDescription: string;
  cookieSettingsTitle: string;
  cookieSettingsDescription: string;
  acceptAll: string;
  rejectAll: string;
  customize: string;
  saveSettings: string;
  required: string;
  close: string;

  // Cookie 类别
  cookieNecessaryTitle: string;
  cookieNecessaryDesc: string;
  cookieAnalyticsTitle: string;
  cookieAnalyticsDesc: string;
  cookieMarketingTitle: string;
  cookieMarketingDesc: string;
  cookiePreferencesTitle: string;
  cookiePreferencesDesc: string;

  // 隐私政策内容
  privacyIntroduction: string;
  dataCollectionTitle: string;
  dataCollectionContent: string;
  dataUsageTitle: string;
  dataUsageContent: string;
  thirdPartyServicesTitle: string;
  thirdPartyServicesContent: string;
  userRightsTitle: string;
  userRightsContent: string;
  dataSecurityTitle: string;
  dataSecurityContent: string;
  contactInformationTitle: string;
  contactInformationContent: string;

  // Cookie 政策内容
  cookieIntroduction: string;
  whatAreCookiesTitle: string;
  whatAreCookiesContent: string;
  cookieTypesTitle: string;
  cookieTypesContent: string;
  manageCookiesTitle: string;
  manageCookiesContent: string;
  thirdPartyCookiesTitle: string;
  thirdPartyCookiesContent: string;

  // 联系我们
  contactUs: string;
  userFeedback: string;
  contactEmailTemplate: string;
  systemInfo: string;
  browser: string;
  language: string;
  timestamp: string;
  contactTitle: string;
  contactDescription: string;
  feedbackTypes: string;
  bugReport: string;
  featureRequest: string;
  generalInquiry: string;
  technicalSupport: string;

}