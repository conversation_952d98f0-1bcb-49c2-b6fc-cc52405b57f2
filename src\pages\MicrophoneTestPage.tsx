import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { MicrophoneTestModule } from "@/components/tests/MicrophoneTestModule";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { SEOFooter } from "@/components/seo/SEOFooter";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";

export const MicrophoneTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('microphone', t, window.location.origin);

  return (
    <MainLayout seoConfig={seoConfig}>
      <MicrophoneTestModule />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="microphone" />
        <FAQ pageType="microphone" />
        <Glossary pageType="microphone" />
        <TroubleshootingGuide pageType="microphone" />
        <SEOFooter pageType="microphone" />
      </div>
    </MainLayout>
  );
};