import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { MouseTest } from "@/components/tests/MouseTest";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

export const MouseTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('mouse', t, window.location.origin);

  return (
    <MainLayout seoConfig={seoConfig}>
      <MouseTest />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="mouse" />
        <FAQ pageType="mouse" />
        <Glossary pageType="mouse" />
        <TroubleshootingGuide pageType="mouse" />
        <SEOFooter pageType="mouse" />
      </div>
    </MainLayout>
  );
};