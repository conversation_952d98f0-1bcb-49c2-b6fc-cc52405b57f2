import React from 'react';
import { X, Setting<PERSON>, Check, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';

interface CookieConsentBannerProps {
  onAcceptAll: () => void;
  onRejectAll: () => void;
  onCustomize: () => void;
  onClose: () => void;
  isVisible: boolean;
}

export const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({
  onAcceptAll,
  onRejectAll,
  onCustomize,
  onClose,
  isVisible
}) => {
  const { t, language } = useLanguage();

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-black/95 backdrop-blur-sm border-t border-white/10">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
          {/* 内容区域 */}
          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              <h3 className="text-white font-semibold">
                {t('cookieConsentTitle')}
              </h3>
            </div>
            <p className="text-white/80 text-sm leading-relaxed">
              {t('cookieConsentDescription')}
            </p>
            <div className="flex items-center gap-4 text-xs text-white/60">
              <a
                href={`/${language}/privacy-policy`}
                className="hover:text-white/80 underline transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                {t('privacyPolicy')}
              </a>
              <a
                href={`/${language}/cookie-policy`}
                className="hover:text-white/80 underline transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                {t('cookiePolicy')}
              </a>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 min-w-fit">
            <Button
              variant="outline"
              size="sm"
              onClick={onRejectAll}
              className="bg-transparent border-white/20 text-white hover:bg-white/10 hover:border-white/30"
            >
              {t('rejectAll')}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onCustomize}
              className="bg-transparent border-white/20 text-white hover:bg-white/10 hover:border-white/30"
            >
              <Settings className="h-4 w-4 mr-1" />
              {t('customize')}
            </Button>

            <Button
              size="sm"
              onClick={onAcceptAll}
              className="bg-blue-600 hover:bg-blue-700 text-white border-0"
            >
              <Check className="h-4 w-4 mr-1" />
              {t('acceptAll')}
            </Button>
          </div>

          {/* 关闭按钮 */}
          <button
            onClick={onClose}
            className="absolute top-2 right-2 lg:relative lg:top-0 lg:right-0 p-1 text-white/60 hover:text-white/80 transition-colors"
            aria-label={t('close')}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};
