@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Dashboard specific animations */
@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    transform: scale(1.02);
  }
}

@keyframes wave-flow {
  0% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(2deg); }
  50% { transform: translateY(0px) rotate(0deg); }
  75% { transform: translateY(10px) rotate(-2deg); }
  100% { transform: translateY(0px) rotate(0deg); }
}

@keyframes data-stream {
  0% { 
    opacity: 0.3;
    transform: translateX(-100px) scale(0.8);
  }
  50% { 
    opacity: 1;
    transform: translateX(0px) scale(1);
  }
  100% { 
    opacity: 0.3;
    transform: translateX(100px) scale(0.8);
  }
}

@keyframes rotate-gauge {
  0% { transform: rotate(-90deg); }
  100% { transform: rotate(90deg); }
}

@keyframes glow {
  0%, 100% { filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.3)); }
  50% { filter: drop-shadow(0 0 16px rgba(16, 185, 129, 0.6)); }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-wave-flow {
  animation: wave-flow 3s ease-in-out infinite;
}

.animate-data-stream {
  animation: data-stream 2s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

/* Background styles */
body {
  background: linear-gradient(135deg, 
    hsl(260, 30%, 15%) 0%,
    hsl(290, 35%, 18%) 25%,
    hsl(210, 40%, 20%) 50%,
    hsl(330, 30%, 18%) 75%,
    hsl(260, 30%, 15%) 100%
  );
  background-attachment: fixed;
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* 音量滑块样式 */
.slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider-thumb::-webkit-slider-track {
  height: 8px;
  background: linear-gradient(to right, 
    rgba(139, 92, 246, 0.3) 0%,
    rgba(139, 92, 246, 0.8) var(--value, 50%),
    rgba(255, 255, 255, 0.2) var(--value, 50%)
  );
  border-radius: 4px;
  border: none;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.slider-thumb::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 1) 0%,
    rgba(168, 85, 247, 1) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
}

.slider-thumb::-moz-range-track {
  height: 8px;
  background: linear-gradient(to right, 
    rgba(139, 92, 246, 0.3) 0%,
    rgba(139, 92, 246, 0.8) var(--value, 50%),
    rgba(255, 255, 255, 0.2) var(--value, 50%)
  );
  border-radius: 4px;
  border: none;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.slider-thumb::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 1) 0%,
    rgba(168, 85, 247, 1) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
}

/* 液态玻璃风格滚动条 - 全局应用 */
.glass-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.glass-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.glass-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.glass-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.glass-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.glass-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* 默认对所有可滚动元素应用玻璃滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

*::-webkit-scrollbar-corner {
  background: transparent;
}