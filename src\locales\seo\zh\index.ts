/**
 * 中文 - SEO翻译模块索引
 * 统一导出所有中文SEO翻译内容
 */

import type { SEOTranslation } from '../types';
import { zhEnhanced } from './enhanced';
import { zhFooter } from './footer';
import { zhKeywords } from './keywords';
import { zhFAQ } from './faq';
import { zhGlossary } from './glossary';
import { zhTroubleshooting } from './troubleshooting';

export const zhTranslation: SEOTranslation = {
  enhanced: zhEnhanced,
  seoFooter: zhFooter,
  seoKeywords: zhKeywords,
  faq: zhFAQ,
  glossary: zhGlossary,
  troubleshooting: zhTroubleshooting
};

// 保持向后兼容的导出
export const zhSEO = zhTranslation;

// 单独导出各模块，便于按需导入
export { zhEnhanced, zhFooter, zhKeywords, zhFAQ, zhGlossary, zhTroubleshooting };
