/**
 * 中文 - 术语表翻译
 * 包含专业术语和定义
 */

import type { GlossaryTranslation } from '../types';

export const zhGlossary: GlossaryTranslation = {
  title: "专业术语",
  terms: {
    resolution: {
      title: "分辨率",
      description: "视频的像素尺寸，如1920x1080，数值越高画质越清晰"
    },
    frameRate: {
      title: "帧率",
      description: "每秒显示的图像帧数，通常以fps表示，影响视频流畅度"
    },
    latency: {
      title: "延迟",
      description: "数据传输的时间延迟，以毫秒(ms)为单位，越低越好"
    },
    bandwidth: {
      title: "带宽",
      description: "网络传输能力，通常以Mbps为单位，决定数据传输速度"
    },
    sampleRate: {
      title: "采样率",
      description: "每秒采集音频样本的次数，常见的有44.1kHz、48kHz"
    },
    bitRate: {
      title: "比特率",
      description: "音频或视频的数据传输速率，影响质量和文件大小"
    },
    dpi: {
      title: "DPI",
      description: "鼠标灵敏度单位，表示每英寸移动的像素数"
    },
    pollingRate: {
      title: "轮询率",
      description: "设备向电脑报告状态的频率，以Hz为单位，越高响应越快"
    },
    fps: {
      title: "帧率(FPS)",
      description: "每秒显示的图像帧数，影响视频流畅度和质量"
    },
    megapixel: {
      title: "像素",
      description: "数字图像的基本单位，百万像素决定图像清晰度"
    },
    exposure: {
      title: "曝光",
      description: "摄像头感光的程度，影响图像明暗和清晰度"
    },
    noiseReduction: {
      title: "降噪",
      description: "消除音频中杂音和背景噪声的技术"
    },
    sensitivity: {
      title: "灵敏度",
      description: "麦克风对声音信号的感应能力"
    },
    frequency: {
      title: "频率",
      description: "声音或电信号的振动次数，以Hz为单位"
    },
    impedance: {
      title: "阻抗",
      description: "音频设备对电流的阻力，影响功率匹配"
    },
    soundStage: {
      title: "声场",
      description: "音频的空间感和定位感，反映音质层次"
    },
    drivers: {
      title: "驱动单元",
      description: "耳机中将电信号转换为声音的核心部件"
    },
    thd: {
      title: "总谐波失真",
      description: "音频信号失真程度的测量指标"
    },
    keyTravel: {
      title: "键程",
      description: "按键从静止到触发的移动距离"
    },
    actuationForce: {
      title: "触发力",
      description: "激活按键所需的最小压力"
    },
    tactile: {
      title: "段落感",
      description: "按键触发时的触觉反馈"
    },
    linear: {
      title: "线性",
      description: "按键压力与行程成比例的特性"
    },
    polling: {
      title: "轮询",
      description: "系统定期检查设备状态的机制"
    },
    acceleration: {
      title: "加速度",
      description: "鼠标快速移动时的响应特性"
    },
    liftOffDistance: {
      title: "离地距离",
      description: "鼠标抬起时仍能感应的最大高度"
    },
    tracking: {
      title: "跟踪",
      description: "鼠标传感器感应移动的能力"
    },
    jitter: {
      title: "抖动",
      description: "网络延迟的变化程度，影响稳定性"
    },
    packetLoss: {
      title: "丢包",
      description: "网络传输中数据包丢失的比例"
    },
    throughput: {
      title: "吞吐量",
      description: "网络实际传输数据的速率"
    },
    codec: {
      title: "编解码器",
      description: "压缩和解压音视频数据的算法"
    },
    compression: {
      title: "压缩",
      description: "减少数据大小以节省带宽的技术"
    },
    inputLag: {
      title: "输入延迟",
      description: "从输入操作到系统响应的时间差"
    },
    compatibility: {
      title: "兼容性",
      description: "硬件设备与不同系统和软件正常工作的能力"
    },
    accuracy: {
      title: "准确性",
      description: "测试结果的精确性和可靠性，表示测量值与实际值的接近程度"
    },
    realTime: {
      title: "实时",
      description: "处理和响应立即发生，没有明显延迟"
    },
    calibration: {
      title: "校准",
      description: "调整设备设置以确保准确和最佳性能的过程"
    },
    benchmark: {
      title: "基准测试",
      description: "用于测量和比较设备性能与既定标准的标准化测试"
    }
  }
};
