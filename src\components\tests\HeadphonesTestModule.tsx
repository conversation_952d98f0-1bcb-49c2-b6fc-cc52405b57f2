import React, { useEffect } from "react";
import { Headphones, Volume2, VolumeX, Play, Pause, Info, RotateCcw, AlertCircle } from "lucide-react";

import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useHeadphones } from "@/hooks/useHeadphones";
import { useLanguage } from "@/hooks/useLanguage";

interface HeadphonesTestModuleProps {
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
}

export const HeadphonesTestModule: React.FC<HeadphonesTestModuleProps> = ({ onTestResult }) => {
  const { t } = useLanguage();

  // 跟踪用户是否进行了测试
  const [hasUserTested, setHasUserTested] = React.useState(false);
  const [testResults, setTestResults] = React.useState({
    leftChannelTested: false,
    rightChannelTested: false,
    frequencyTested: false,
    dynamicRangeTested: false,
    stereoTested: false
  });

  // Add CSS animations for enhanced effects
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes dynamicBar {
        0% { transform: scaleY(0.3); }
        50% { transform: scaleY(1.2); }
        100% { transform: scaleY(0.6); }
      }
      
      @keyframes floatUp {
        0% { transform: translateY(0px); opacity: 1; }
        100% { transform: translateY(-20px); opacity: 0; }
      }
      
      @keyframes liquidFlow {
        0% { transform: translateX(-100%) scaleX(0.8); }
        50% { transform: translateX(0%) scaleX(1.2); }
        100% { transform: translateX(100%) scaleX(0.8); }
      }
      
      @keyframes glowPulse {
        0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
        50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
      }
      
             @keyframes morphBorder {
         0% { border-radius: 50%; }
         25% { border-radius: 60% 40% 60% 40%; }
         50% { border-radius: 40% 60% 40% 60%; }
         75% { border-radius: 60% 40% 60% 40%; }
         100% { border-radius: 50%; }
       }
       
       @keyframes orbit {
         0% { transform: rotate(0deg) translateX(30px) rotate(0deg); }
         100% { transform: rotate(360deg) translateX(30px) rotate(-360deg); }
       }
       
       @keyframes strongBlink {
         0%, 100% { 
           opacity: 1; 
           transform: scale(1);
           box-shadow: 0 0 20px currentColor;
         }
         50% { 
           opacity: 0.3; 
           transform: scale(0.95);
           box-shadow: 0 0 40px currentColor;
         }
       }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  const {
    outputDevices,
    selectedOutputDevice,
    setSelectedOutputDevice,
    isPlaying,
    currentTest,
    playLeftChannel,
    playRightChannel,
    playFrequencySweep,
    playDynamicRangeTest,
    play3DAudio,
    stopAllAudio,
    currentFrequency,
    sweepProgress,
    currentPanPosition,
    requestPermissions,
    error
  } = useHeadphones();

  useEffect(() => {
    requestPermissions();
  }, [requestPermissions]);

  // 自动报告测试结果 - 只有在用户实际进行了测试后才报告
  useEffect(() => {
    if (onTestResult && hasUserTested && !error && outputDevices.length > 0) {
      // 检查用户是否至少完成了基本的左右声道测试
      const basicTestsCompleted = testResults.leftChannelTested && testResults.rightChannelTested;

      if (basicTestsCompleted) {
        const selectedDevice = outputDevices.find(device => device.deviceId === selectedOutputDevice);
        const completedTestsCount = Object.values(testResults).filter(Boolean).length;

        onTestResult({
          passed: true,
          details: {
            deviceCount: outputDevices.length,
            selectedDevice: selectedDevice?.label || 'Default',
            hasError: !!error,
            completedTests: completedTestsCount,
            testResults: testResults
          }
        });
      }
    }
  }, [onTestResult, hasUserTested, error, outputDevices, selectedOutputDevice, testResults]);

  const handlePlayWithUserInteraction = async (playFunction: () => Promise<void>, testType?: string) => {
    console.log('handlePlayWithUserInteraction called');
    console.log('Current isPlaying state:', isPlaying);
    console.log('Current currentTest state:', currentTest);

    try {
      // If currently playing, stop the audio
      if (isPlaying) {
        console.log('Stopping audio because already playing');
        stopAllAudio();
        return;
      }

      console.log('Attempting to play audio...');
      // Ensure audio context is initialized with user interaction
      await playFunction();
      console.log('Audio play function completed');

      // 标记用户已经进行了测试
      setHasUserTested(true);

      // 根据测试类型更新相应的测试状态
      if (testType) {
        setTestResults(prev => ({
          ...prev,
          [testType]: true
        }));
      }
    } catch (err) {
      console.error('Error playing audio:', err);
    }
  };

  const renderOutputDeviceSelector = () => (
    <GlassCard className="mb-6">
      <div className="flex items-center justify-center mb-4">
        <div className="bg-white/10 rounded-full p-3 w-fit">
          <Headphones className="h-8 w-8 text-blue-400" />
        </div>
      </div>
      <h3 className="text-lg font-medium text-white mb-4 text-center">
        {t("outputDeviceSelector")}
      </h3>
      
      {outputDevices.length > 0 ? (
        <select
          value={selectedOutputDevice}
          onChange={(e) => setSelectedOutputDevice(e.target.value)}
          className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50 glass-scrollbar"
        >
          {outputDevices.map((device) => (
            <option key={device.deviceId} value={device.deviceId} className="bg-gray-800 text-white">
              {device.label}
            </option>
          ))}
        </select>
      ) : (
        <div className="text-white/70 text-center py-4">
          {t("noOutputDevices")}
        </div>
      )}
    </GlassCard>
  );

  const renderLeftRightChannelTest = () => (
    <GlassCard className="mb-6 overflow-hidden relative">
      {/* Ambient background effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 via-transparent to-purple-400/5 pointer-events-none" />
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-transparent rounded-full blur-3xl opacity-50" />
      <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-purple-400/10 to-transparent rounded-full blur-3xl opacity-50" />
             {/* Main content wrapper */}
       <div className="relative z-10">
         {/* Header with floating icon */}
         <div className="flex items-center justify-center mb-6">
         <div className="relative">
           <div className="bg-gradient-to-br from-blue-400/20 to-purple-400/20 backdrop-blur-xl rounded-full p-4 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
             <Volume2 className="h-8 w-8 text-blue-400" />
           </div>
           {/* Enhanced floating particles */}
           <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping" />
           <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
           <div className="absolute -top-2 -left-2 w-1 h-1 bg-cyan-400 rounded-full animate-pulse" style={{ animationDelay: '2s' }} />
           <div className="absolute -bottom-2 -right-2 w-1 h-1 bg-pink-400 rounded-full animate-ping" style={{ animationDelay: '1.5s' }} />
           
           {/* Orbital particles */}
           {[...Array(3)].map((_, i) => (
             <div
               key={`orbital-${i}`}
               className="absolute w-1 h-1 bg-white/60 rounded-full"
               style={{
                 animation: `orbit 3s linear infinite`,
                 animationDelay: `${i * 1}s`,
                 transformOrigin: '30px 30px'
               }}
             />
           ))}
         </div>
       </div>
      
      <h3 className="text-xl font-semibold text-white mb-3 text-center bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
        {t("leftRightChannelTest")}
      </h3>
      <p className="text-white/70 text-sm mb-8 text-center leading-relaxed">
        {t("leftRightChannelDesc")}
      </p>
      
             {/* Enhanced headphones visualization */}
       <div className="mb-6 flex justify-center">
        <div className="relative">
          {/* Headphones visualization with enhanced design */}
          <div className="flex items-center space-x-12">
                         {/* Left earphone - Enhanced */}
             <div className={`relative w-20 h-20 border-2 transition-all duration-500 transform hover:scale-105 ${
               currentTest === 'left-channel' 
                 ? 'border-blue-400 bg-gradient-to-br from-blue-400/30 to-cyan-400/20 shadow-lg shadow-blue-400/30 animate-pulse scale-110' 
                 : 'border-white/30 bg-gradient-to-br from-white/10 to-white/5 hover:border-white/50 hover:bg-white/10'
             }`}
             style={{
               borderRadius: currentTest === 'left-channel' ? '50%' : '50%',
               animation: currentTest === 'left-channel' ? 'morphBorder 3s ease-in-out infinite' : 'none'
             }}>
                             {/* Inner circle with glass effect */}
               <div className={`absolute inset-2 rounded-full bg-gradient-to-br backdrop-blur-sm border flex items-center justify-center transition-all duration-300 ${
                 currentTest === 'left-channel' 
                   ? 'from-blue-400/50 to-cyan-400/40 border-blue-400/60 shadow-lg shadow-blue-400/40' 
                   : 'from-white/20 to-white/10 border-white/20'
               }`}
               style={{
                 animation: currentTest === 'left-channel' ? 'strongBlink 1s ease-in-out infinite' : 'none'
               }}>
                 <span className={`text-lg font-bold transition-all duration-300 ${
                   currentTest === 'left-channel' 
                     ? 'text-blue-100 drop-shadow-lg' 
                     : 'text-white/80'
                 }`}
                 style={{
                   animation: currentTest === 'left-channel' ? 'strongBlink 1s ease-in-out infinite' : 'none'
                 }}>L</span>
               </div>
              
              {/* Ripple effect when active */}
              {currentTest === 'left-channel' && (
                <>
                  <div className="absolute inset-0 rounded-full border-2 border-blue-400/50 animate-ping" />
                  <div className="absolute inset-0 rounded-full border-2 border-cyan-400/30 animate-ping" style={{ animationDelay: '0.5s' }} />
                </>
              )}
              
                             {/* Enhanced sound waves */}
               {currentTest === 'left-channel' && (
                 <div className="absolute -left-6 top-1/2 transform -translate-y-1/2">
                   <div className="flex space-x-2">
                     {[...Array(4)].map((_, i) => (
                       <div
                         key={i}
                         className="bg-gradient-to-t from-blue-400 to-cyan-400 rounded-full animate-pulse shadow-lg"
                         style={{
                           width: '3px',
                           height: `${12 + i * 4}px`,
                           animationDelay: `${i * 150}ms`,
                           animationDuration: '1s'
                         }}
                       />
                     ))}
                   </div>
                   {/* Floating particles */}
                   {[...Array(6)].map((_, i) => (
                     <div
                       key={`particle-${i}`}
                       className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-70"
                       style={{
                         left: `${-20 + i * 5}px`,
                         top: `${-10 + Math.random() * 20}px`,
                         animation: `floatUp 2s ease-out infinite`,
                         animationDelay: `${i * 300}ms`
                       }}
                     />
                   ))}
                 </div>
               )}
            </div>
            
            {/* Enhanced headband */}
            <div className="flex-1 h-3 bg-gradient-to-r from-white/20 via-white/30 to-white/20 rounded-full relative shadow-inner">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm rounded-full border border-white/30 flex items-center justify-center shadow-lg">
                <Headphones className="h-6 w-6 text-white/70" />
              </div>
                             {/* Flowing light effect */}
               <div className="absolute inset-0 rounded-full overflow-hidden">
                 <div 
                   className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                   style={{
                     animation: (currentTest === 'left-channel' || currentTest === 'right-channel') 
                       ? 'liquidFlow 2s ease-in-out infinite' 
                       : 'none'
                   }}
                 />
               </div>
            </div>
            
                         {/* Right earphone - Enhanced */}
             <div className={`relative w-20 h-20 border-2 transition-all duration-500 transform hover:scale-105 ${
               currentTest === 'right-channel' 
                 ? 'border-pink-400 bg-gradient-to-br from-pink-400/30 to-rose-400/20 shadow-lg shadow-pink-400/30 animate-pulse scale-110' 
                 : 'border-white/30 bg-gradient-to-br from-white/10 to-white/5 hover:border-white/50 hover:bg-white/10'
             }`}
             style={{
               borderRadius: currentTest === 'right-channel' ? '50%' : '50%',
               animation: currentTest === 'right-channel' ? 'morphBorder 3s ease-in-out infinite' : 'none'
             }}>
                             {/* Inner circle with glass effect */}
               <div className={`absolute inset-2 rounded-full bg-gradient-to-br backdrop-blur-sm border flex items-center justify-center transition-all duration-300 ${
                 currentTest === 'right-channel' 
                   ? 'from-pink-400/50 to-rose-400/40 border-pink-400/60 shadow-lg shadow-pink-400/40' 
                   : 'from-white/20 to-white/10 border-white/20'
               }`}
               style={{
                 animation: currentTest === 'right-channel' ? 'strongBlink 1s ease-in-out infinite' : 'none'
               }}>
                 <span className={`text-lg font-bold transition-all duration-300 ${
                   currentTest === 'right-channel' 
                     ? 'text-pink-100 drop-shadow-lg' 
                     : 'text-white/80'
                 }`}
                 style={{
                   animation: currentTest === 'right-channel' ? 'strongBlink 1s ease-in-out infinite' : 'none'
                 }}>R</span>
               </div>
              
              {/* Ripple effect when active */}
              {currentTest === 'right-channel' && (
                <>
                  <div className="absolute inset-0 rounded-full border-2 border-pink-400/50 animate-ping" />
                  <div className="absolute inset-0 rounded-full border-2 border-rose-400/30 animate-ping" style={{ animationDelay: '0.5s' }} />
                </>
              )}
              
                             {/* Enhanced sound waves */}
               {currentTest === 'right-channel' && (
                 <div className="absolute -right-6 top-1/2 transform -translate-y-1/2">
                   <div className="flex space-x-2">
                     {[...Array(4)].map((_, i) => (
                       <div
                         key={i}
                         className="bg-gradient-to-t from-pink-400 to-rose-400 rounded-full animate-pulse shadow-lg"
                         style={{
                           width: '3px',
                           height: `${12 + i * 4}px`,
                           animationDelay: `${i * 150}ms`,
                           animationDuration: '1s'
                         }}
                       />
                     ))}
                   </div>
                   {/* Floating particles */}
                   {[...Array(6)].map((_, i) => (
                     <div
                       key={`particle-${i}`}
                       className="absolute w-1 h-1 bg-pink-400 rounded-full opacity-70"
                       style={{
                         right: `${-20 + i * 5}px`,
                         top: `${-10 + Math.random() * 20}px`,
                         animation: `floatUp 2s ease-out infinite`,
                         animationDelay: `${i * 300}ms`
                       }}
                     />
                   ))}
                 </div>
               )}
            </div>
          </div>
          
          
        </div>
      </div>
      
      {/* Enhanced button design */}
      <div className="grid grid-cols-2 gap-6 mt-4">
                 {/* Left channel button */}
         <button
          onClick={() => handlePlayWithUserInteraction(playLeftChannel, 'leftChannelTested')}
          className={`group relative overflow-hidden rounded-2xl p-4 transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 ${
            currentTest === 'left-channel' 
              ? 'bg-gradient-to-br from-blue-400/25 to-cyan-400/15 border-2 border-blue-400/50 shadow-xl shadow-blue-400/25 animate-pulse' 
              : 'bg-gradient-to-br from-white/10 to-white/5 border-2 border-white/20 hover:border-blue-400/40 hover:bg-gradient-to-br hover:from-blue-400/15 hover:to-cyan-400/10'
          }`}
        >
          {/* Background shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
          
          {/* Liquid border effect */}
          <div className="absolute inset-0 rounded-2xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-cyan-400/30 to-blue-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" 
                 style={{ animation: 'liquidFlow 3s ease-in-out infinite' }} />
          </div>
          
          {/* Button content */}
          <div className="relative flex flex-col items-center space-y-2">
            <div className={`p-3 rounded-full transition-all duration-300 ${
              currentTest === 'left-channel' 
                ? 'bg-gradient-to-br from-blue-400/30 to-cyan-400/20 shadow-lg' 
                : 'bg-white/10 group-hover:bg-blue-400/20'
            }`}>
              {currentTest === 'left-channel' ? (
                <Pause className="h-6 w-6 text-blue-300" />
              ) : (
                <Play className="h-6 w-6 text-white/80 group-hover:text-blue-300" />
              )}
            </div>
            <span className={`text-sm font-medium transition-colors duration-300 ${
              currentTest === 'left-channel' 
                ? 'text-blue-200' 
                : 'text-white/80 group-hover:text-blue-200'
            }`}>
              {currentTest === 'left-channel' ? t("stopPlaying") : t("playLeft")}
            </span>
          </div>
          
          {/* Glow effect */}
          {currentTest === 'left-channel' && (
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400/20 to-cyan-400/10 blur-xl -z-10" />
          )}
        </button>
        
        {/* Right channel button */}
        <button
          onClick={() => handlePlayWithUserInteraction(playRightChannel, 'rightChannelTested')}
          className={`group relative overflow-hidden rounded-2xl p-4 transition-all duration-500 transform hover:scale-105 hover:-translate-y-1 ${
            currentTest === 'right-channel' 
              ? 'bg-gradient-to-br from-pink-400/25 to-rose-400/15 border-2 border-pink-400/50 shadow-xl shadow-pink-400/25 animate-pulse' 
              : 'bg-gradient-to-br from-white/10 to-white/5 border-2 border-white/20 hover:border-pink-400/40 hover:bg-gradient-to-br hover:from-pink-400/15 hover:to-rose-400/10'
          }`}
        >
          {/* Background shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
          
          {/* Liquid border effect */}
          <div className="absolute inset-0 rounded-2xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-pink-400/20 via-rose-400/30 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" 
                 style={{ animation: 'liquidFlow 3s ease-in-out infinite' }} />
          </div>
          
          {/* Button content */}
          <div className="relative flex flex-col items-center space-y-2">
            <div className={`p-3 rounded-full transition-all duration-300 ${
              currentTest === 'right-channel' 
                ? 'bg-gradient-to-br from-pink-400/30 to-rose-400/20 shadow-lg' 
                : 'bg-white/10 group-hover:bg-pink-400/20'
            }`}>
              {currentTest === 'right-channel' ? (
                <Pause className="h-6 w-6 text-pink-300" />
              ) : (
                <Play className="h-6 w-6 text-white/80 group-hover:text-pink-300" />
              )}
            </div>
            <span className={`text-sm font-medium transition-colors duration-300 ${
              currentTest === 'right-channel' 
                ? 'text-pink-200' 
                : 'text-white/80 group-hover:text-pink-200'
            }`}>
              {currentTest === 'right-channel' ? t("stopPlaying") : t("playRight")}
            </span>
          </div>
          
          {/* Glow effect */}
          {currentTest === 'right-channel' && (
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-pink-400/20 to-rose-400/10 blur-xl -z-10" />
          )}
        </button>
      </div>
      
      
      </div>
    </GlassCard>
  );

  const renderFrequencyResponseTest = () => {
    // 计算当前频率在对数刻度上的位置
    const getFrequencyPosition = (frequency: number) => {
      if (frequency <= 0) return 0;
      const minFreq = 20;
      const maxFreq = 20000;
      const logMin = Math.log10(minFreq);
      const logMax = Math.log10(maxFreq);
      const logCurrent = Math.log10(frequency);
      return Math.min(Math.max((logCurrent - logMin) / (logMax - logMin), 0), 1);
    };

    // 获取当前频率的颜色
    const getCurrentFrequencyColor = (frequency: number) => {
      if (frequency < 250) return '#10B981'; // 绿色 - 超低音
      if (frequency < 500) return '#06B6D4'; // 青色 - 低音
      if (frequency < 2000) return '#3B82F6'; // 蓝色 - 中低音
      if (frequency < 4000) return '#6366F1'; // 靛蓝 - 中音
      if (frequency < 8000) return '#8B5CF6'; // 紫色 - 中高音
      if (frequency < 16000) return '#EC4899'; // 粉色 - 高音
      return '#F59E0B'; // 橙色 - 超高音
    };

    // 获取频率范围标签
    const getFrequencyLabel = (frequency: number) => {
      if (frequency < 250) return t("ultraLowBass");
      if (frequency < 500) return t("lowBass");
      if (frequency < 2000) return t("midLowBass");
      if (frequency < 4000) return t("midrange");
      if (frequency < 8000) return t("midTreble");
      if (frequency < 16000) return t("treble");
      return t("ultraTreble");
    };

    return (
      <GlassCard className="mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-white/10 rounded-full p-3 w-fit">
            <div className="flex items-center space-x-2">
              <VolumeX className="h-6 w-6 text-blue-400" />
              <Volume2 className="h-6 w-6 text-blue-400" />
            </div>
          </div>
        </div>
        <h3 className="text-lg font-medium text-white mb-3 text-center">
          {t("frequencyResponseTest")}
        </h3>
        <p className="text-white/70 text-sm mb-6 text-center">
          {t("frequencyResponseDesc")}
        </p>
        
        {currentTest === 'frequency-sweep' && (
          <div className="mb-6">
            {/* 当前频率显示 - 增强版 */}
            <div className="text-center mb-6">
              <div className="relative">
                <div 
                  className="text-6xl font-bold mb-2 transition-all duration-200"
                  style={{ color: getCurrentFrequencyColor(currentFrequency) }}
                >
                  {currentFrequency.toLocaleString()} Hz
                </div>
                <div className="text-lg text-white/80 font-medium">
                  {getFrequencyLabel(currentFrequency)}
                </div>
                {/* 频率变化动画指示器 */}
                <div className="absolute -top-2 -right-2">
                  <div className="w-4 h-4 rounded-full animate-ping" style={{ backgroundColor: getCurrentFrequencyColor(currentFrequency) + '40' }} />
                  <div className="absolute top-0 w-4 h-4 rounded-full" style={{ backgroundColor: getCurrentFrequencyColor(currentFrequency) }} />
                </div>
              </div>
            </div>
            
            {/* 增强版频率图谱可视化 */}
            <div className="bg-gradient-to-b from-white/10 to-white/5 rounded-xl p-6 mb-6 border border-white/20">
              <div className="relative h-48">
                {/* 频率标尺背景 */}
                <div className="absolute inset-0">
                  <div className="w-full h-full relative">
                    {/* 频率网格线 - 对数刻度 */}
                    <div className="absolute inset-0 opacity-30">
                      {/* 主要频率线 */}
                      {[20, 100, 1000, 10000, 20000].map((freq, i) => {
                        const pos = getFrequencyPosition(freq);
                        return (
                          <div key={freq} className="absolute h-full border-l-2 border-white/40" style={{ left: `${pos * 100}%` }}>
                            <div className="absolute -bottom-6 -translate-x-1/2 text-xs text-white/60 font-medium">
                              {freq >= 1000 ? `${freq/1000}k` : freq}Hz
                            </div>
                          </div>
                        );
                      })}
                      {/* 次要频率线 */}
                      {[50, 200, 500, 2000, 5000, 15000].map((freq, i) => {
                        const pos = getFrequencyPosition(freq);
                        return (
                          <div key={freq} className="absolute h-full border-l border-white/20" style={{ left: `${pos * 100}%` }} />
                        );
                      })}
                    </div>
                    
                    {/* 频率响应曲线 */}
                    <svg className="absolute inset-0 w-full h-full" viewBox="0 0 400 192">
                      {/* 背景频率响应曲线 */}
                      <defs>
                        <linearGradient id="responseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="0%" stopColor="#10B981" stopOpacity="0.3" />
                          <stop offset="20%" stopColor="#06B6D4" stopOpacity="0.3" />
                          <stop offset="40%" stopColor="#3B82F6" stopOpacity="0.3" />
                          <stop offset="60%" stopColor="#6366F1" stopOpacity="0.3" />
                          <stop offset="80%" stopColor="#EC4899" stopOpacity="0.3" />
                          <stop offset="100%" stopColor="#F59E0B" stopOpacity="0.3" />
                        </linearGradient>
                        
                        <linearGradient id="currentFreqGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" stopColor={getCurrentFrequencyColor(currentFrequency)} stopOpacity="0.8" />
                          <stop offset="100%" stopColor={getCurrentFrequencyColor(currentFrequency)} stopOpacity="0.2" />
                        </linearGradient>
                      </defs>
                      
                      {/* 理想频率响应曲线 */}
                      <path
                        d="M 0 144 Q 40 120 80 126 Q 120 122 160 120 Q 200 118 240 122 Q 280 126 320 132 Q 360 138 400 144"
                        stroke="url(#responseGradient)"
                        strokeWidth="3"
                        fill="none"
                        className="drop-shadow-lg"
                      />
                      
                      {/* 当前频率扫描区域 */}
                      <rect
                        x={Math.max(0, getFrequencyPosition(currentFrequency) * 400 - 10)}
                        y="0"
                        width="20"
                        height="192"
                        fill="url(#currentFreqGradient)"
                        className="animate-pulse"
                      />
                      
                      {/* 当前频率指示器 */}
                      <circle
                        cx={getFrequencyPosition(currentFrequency) * 400}
                        cy="96"
                        r="8"
                        fill={getCurrentFrequencyColor(currentFrequency)}
                        className="animate-pulse drop-shadow-lg"
                      />
                      
                      {/* 当前频率垂直线 */}
                      <line
                        x1={getFrequencyPosition(currentFrequency) * 400}
                        y1="0"
                        x2={getFrequencyPosition(currentFrequency) * 400}
                        y2="192"
                        stroke={getCurrentFrequencyColor(currentFrequency)}
                        strokeWidth="3"
                        opacity="0.8"
                        className="drop-shadow-lg"
                      />
                      
                      {/* 频率扫描波形效果 */}
                      <circle
                        cx={getFrequencyPosition(currentFrequency) * 400}
                        cy="96"
                        r="20"
                        fill="none"
                        stroke={getCurrentFrequencyColor(currentFrequency)}
                        strokeWidth="2"
                        opacity="0.6"
                        className="animate-ping"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 增强版进度条 */}
            <div className="w-full bg-white/20 rounded-full h-4 mb-6 relative overflow-hidden">
              <div 
                className="h-4 rounded-full transition-all duration-200 relative"
                style={{ 
                  width: `${sweepProgress * 100}%`,
                  background: `linear-gradient(90deg, 
                    #10B981 0%, 
                    #06B6D4 16.67%, 
                    #3B82F6 33.33%, 
                    #6366F1 50%, 
                    #8B5CF6 66.67%, 
                    #EC4899 83.33%, 
                    #F59E0B 100%)`
                }}
              >
                <div 
                  className="absolute right-0 top-0 w-4 h-4 rounded-full shadow-lg animate-pulse"
                  style={{ backgroundColor: getCurrentFrequencyColor(currentFrequency) }}
                />
              </div>
              {/* 进度条背景波纹效果 */}
              <div 
                className="absolute top-0 h-4 w-8 bg-white/20 rounded-full animate-pulse"
                style={{ left: `${Math.max(0, sweepProgress * 100 - 4)}%` }}
              />
            </div>
            
            {/* 实时频率范围指示器 */}
            <div className="grid grid-cols-2 xl:grid-cols-4 gap-3 text-xs mb-4">
              {[
                { range: '20Hz - 250Hz', label: t("ultraLowBass"), desc: t("bassDrumBass"), color: '#10B981', active: currentFrequency >= 20 && currentFrequency < 250 },
                { range: '250Hz - 2kHz', label: t("midLowBass"), desc: t("voiceBase"), color: '#3B82F6', active: currentFrequency >= 250 && currentFrequency < 2000 },
                { range: '2kHz - 8kHz', label: t("midTreble"), desc: t("voiceClarity"), color: '#8B5CF6', active: currentFrequency >= 2000 && currentFrequency < 8000 },
                { range: '8kHz - 20kHz', label: t("treble"), desc: t("detailAiriness"), color: '#EC4899', active: currentFrequency >= 8000 && currentFrequency <= 20000 }
              ].map((item, index) => (
                <div 
                  key={index}
                  className={`rounded-lg p-3 transition-all duration-200 border ${
                    item.active 
                      ? 'bg-white/20 border-white/40 shadow-lg' 
                      : 'bg-white/5 border-white/10'
                  }`}
                  style={{ 
                    backgroundColor: item.active ? `${item.color}20` : undefined,
                    borderColor: item.active ? `${item.color}60` : undefined
                  }}
                >
                  <div 
                    className="font-medium mb-1"
                    style={{ color: item.active ? item.color : '#9CA3AF' }}
                  >
                    {item.range}
                  </div>
                  <div className={`text-xs ${item.active ? 'text-white' : 'text-white/60'}`}>
                    {item.label} - {item.desc}
                  </div>
                  {item.active && (
                    <div className="mt-1">
                      <div className="w-full h-1 bg-white/20 rounded-full overflow-hidden">
                        <div 
                          className="h-1 rounded-full animate-pulse"
                          style={{ backgroundColor: item.color, width: '100%' }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        <PrimaryButton
          onClick={() => handlePlayWithUserInteraction(playFrequencySweep, 'frequencyTested')}
          size="lg"
          className={`w-full ${currentTest === 'frequency-sweep' ? 'bg-gradient-to-r from-red-400/20 to-pink-400/20 border-red-400/40 backdrop-blur-xl shadow-lg shadow-red-400/10 animate-pulse' : ''}`}
        >
          {currentTest === 'frequency-sweep' ? (
            <>
              <Pause className="h-5 w-5 mr-2" />
              {t("stopSweep")}
            </>
          ) : (
            <>
              <Play className="h-5 w-5 mr-2" />
              {t("startSweep")}
            </>
          )}
        </PrimaryButton>
        
        <div className="flex items-center space-x-2 mt-4 text-sm text-blue-200">
          <Info className="h-4 w-4" />
          <span>{t("frequencyTestTip")}</span>
        </div>
        
        {/* 测试说明 */}
        <div className="mt-4 bg-blue-500/10 border border-blue-400/30 rounded-xl p-3">
          <h5 className="text-blue-200 font-medium mb-2 text-sm">{t("testInstructions")}</h5>
          <ul className="text-blue-200/80 text-xs space-y-1">
            <li>{t("testInstructionItem1")}</li>
            <li>{t("testInstructionItem2")}</li>
            <li>{t("testInstructionItem3")}</li>
            <li>{t("testInstructionItem4")}</li>
            <li>{t("testInstructionItem5")}</li>
          </ul>
        </div>
      </GlassCard>
    );
  };

  const renderDynamicRangeTest = () => (
    <GlassCard className="mb-6">
      <div className="flex items-center justify-center mb-4">
        <div className="bg-white/10 rounded-full p-3 w-fit">
          <div className="relative">
            <Volume2 className="h-6 w-6 text-blue-400" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
          </div>
        </div>
      </div>
      <h3 className="text-lg font-medium text-white mb-3 text-center">
        {t("dynamicRangeTest")}
      </h3>
      <p className="text-white/70 text-sm mb-6 text-center">
        {t("dynamicRangeDesc")}
      </p>
      
      {/* Dynamic range visualization */}
      {currentTest === 'dynamic-range' && (
        <div className="mb-6">
          <div className="flex justify-center mb-4">
            <div className="relative w-64 h-20 bg-white/5 rounded-lg border border-white/20 overflow-hidden">
                             {/* Volume level bars */}
               <div className="absolute inset-0 flex items-end justify-center space-x-1 p-2">
                 {[...Array(20)].map((_, i) => {
                   const baseHeight = 20 + (i * 3);
                   const maxHeight = 80;
                   return (
                     <div
                       key={i}
                       className={`w-2 rounded-t ${
                         i < 5 ? 'bg-green-400' : 
                         i < 10 ? 'bg-yellow-400' : 
                         i < 15 ? 'bg-orange-400' : 'bg-red-400'
                       }`}
                       style={{
                         height: `${baseHeight}%`,
                         animation: `dynamicBar ${1000 + i * 100}ms ease-in-out infinite alternate`,
                         animationDelay: `${i * 50}ms`
                       }}
                     />
                   );
                 })}
               </div>
              
              {/* Dynamic range indicator */}
              <div className="absolute top-2 left-2 text-xs text-white/70 bg-black/40 px-2 py-1 rounded">
                {t("dynamicRangeTestInProgress")}
              </div>

              {/* Volume level labels */}
              <div className="absolute bottom-1 left-2 text-xs text-white/50">{t("quietSound")}</div>
              <div className="absolute bottom-1 right-2 text-xs text-white/50">{t("loudSound")}</div>
            </div>
          </div>
          
          {/* Audio characteristics display */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-green-400 text-sm font-medium">{t("bassDetail")}</div>
              <div className="text-white/70 text-xs mt-1">{t("backgroundEffects")}</div>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-yellow-400 text-sm font-medium">{t("midrangeLayer")}</div>
              <div className="text-white/70 text-xs mt-1">{t("voiceDialogue")}</div>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-red-400 text-sm font-medium">{t("trebleImpact")}</div>
              <div className="text-white/70 text-xs mt-1">{t("explosionEffects")}</div>
            </div>
          </div>
        </div>
      )}
      
      <PrimaryButton
        onClick={() => handlePlayWithUserInteraction(playDynamicRangeTest, 'dynamicRangeTested')}
        size="lg"
        className={`w-full ${currentTest === 'dynamic-range' ? 'bg-gradient-to-r from-red-400/20 to-pink-400/20 border-red-400/40 backdrop-blur-xl shadow-lg shadow-red-400/10 animate-pulse' : ''}`}
      >
        {currentTest === 'dynamic-range' ? (
          <>
            <Pause className="h-5 w-5 mr-2" />
            {t("stopTestButton")}
          </>
        ) : (
          <>
            <Play className="h-5 w-5 mr-2" />
            {t("startTestButton")}
          </>
        )}
      </PrimaryButton>
      
      <div className="flex items-center space-x-2 mt-4 text-sm text-blue-200">
        <Info className="h-4 w-4" />
        <span>{t("dynamicRangeTestTip")}</span>
      </div>
    </GlassCard>
  );

  const renderStereoImagingTest = () => (
    <GlassCard className="mb-6">
      <div className="flex items-center justify-center mb-4">
        <div className="bg-white/10 rounded-full p-3 w-fit">
          <div className="relative">
            <Headphones className="h-6 w-6 text-blue-400" />
            <div className="absolute inset-0 border-2 border-blue-400 rounded-full animate-ping" />
          </div>
        </div>
      </div>
      <h3 className="text-lg font-medium text-white mb-3 text-center">
        {t("stereoImagingTest")}
      </h3>
      <p className="text-white/70 text-sm mb-6 text-center">
        {t("stereoImagingDesc")}
      </p>
      
      {currentTest === '3d-positioning' && (
        <div className="mb-6 flex justify-center">
          <div className="relative w-40 h-40 border border-white/30 rounded-full">
            {/* Center point */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-blue-400 rounded-full" />
            
            {/* Moving sound position indicator */}
            <div 
              className="absolute top-1/2 w-3 h-3 bg-red-400 rounded-full animate-pulse transform -translate-y-1/2 transition-all duration-100 ease-linear"
              style={{
                left: `${50 + currentPanPosition * 35}%`,
                transform: 'translateY(-50%) translateX(-50%)',
                boxShadow: '0 0 10px rgba(248, 113, 113, 0.8)'
              }}
            />
            
            {/* Direction labels */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs text-white/60">{t("frontDirection")}</div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-white/60">{t("backDirection")}</div>
            <div className="absolute top-1/2 left-1 transform -translate-y-1/2 text-xs text-white/60">{t("leftDirection")}</div>
            <div className="absolute top-1/2 right-1 transform -translate-y-1/2 text-xs text-white/60">{t("rightDirection")}</div>
            
            {/* Pan position indicator */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-white/60 text-center">
              <div className="bg-white/10 px-2 py-1 rounded">
                {currentPanPosition < -0.5 ? t("leftChannel") :
                 currentPanPosition > 0.5 ? t("rightChannel") : t("center")}
              </div>
            </div>
          </div>
        </div>
      )}
      
      <PrimaryButton
        onClick={() => handlePlayWithUserInteraction(play3DAudio, 'stereoTested')}
        size="lg"
        className={`w-full ${currentTest === '3d-positioning' ? 'bg-gradient-to-r from-red-400/20 to-pink-400/20 border-red-400/40 backdrop-blur-xl shadow-lg shadow-red-400/10 animate-pulse' : ''}`}
      >
        {currentTest === '3d-positioning' ? (
          <>
            <Pause className="h-5 w-5 mr-2" />
            {t("stopPlaying")}
          </>
        ) : (
          <>
            <Play className="h-5 w-5 mr-2" />
            {t("play3dAudio")}
          </>
        )}
      </PrimaryButton>
      
      <div className="flex items-center space-x-2 mt-4 text-sm text-blue-200">
        <Info className="h-4 w-4" />
        <span>{t("stereoImagingTestTip")}</span>
      </div>
    </GlassCard>
  );

  const reset = () => {
    stopAllAudio();
  };

  return (
    <div className="min-h-screen w-full">
      
      <main className="w-full px-6 py-8 flex justify-center">
        <GlassCard className="mb-6 p-8 w-full max-w-7xl">
          <div className="flex items-center justify-between mb-8 min-w-0">
            <div className="flex-1 min-w-0">
              <h1 className="text-3xl font-bold text-white mb-2">
                {t("headphonesTestTitle")}
              </h1>
              <p className="text-white/70">
                {t("headphonesTestDesc")}
              </p>
            </div>
            <div className="flex space-x-3 flex-shrink-0">
              <PrimaryButton onClick={reset} variant="outline" size="sm">
                <RotateCcw className="h-4 w-4 mr-2" />
                {t("resetTest")}
              </PrimaryButton>
            </div>
          </div>

          {error && (
            <div className="mb-6 bg-red-500/20 border border-red-400/50 rounded-xl p-4 text-red-200">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <span className="font-medium">{t("audioError")}</span>
              </div>
              <p className="mt-2 text-sm">{error}</p>
              <PrimaryButton 
                onClick={requestPermissions}
                variant="outline"
                size="sm"
                className="mt-3"
              >
                {t("retry")}
              </PrimaryButton>
            </div>
          )}

          {/* 测试进度指示器 */}
          <div className="mb-6 bg-white/5 border border-white/20 rounded-xl p-4">
            <h3 className="text-white font-medium mb-3 flex items-center">
              <Headphones className="h-5 w-5 mr-2" />
              {t("testProgress")}
            </h3>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className={`flex items-center space-x-2 ${testResults.leftChannelTested ? 'text-green-400' : 'text-white/60'}`}>
                <span>{testResults.leftChannelTested ? '✅' : '⏸️'}</span>
                <span>{t("leftChannelTest")}</span>
              </div>
              <div className={`flex items-center space-x-2 ${testResults.rightChannelTested ? 'text-green-400' : 'text-white/60'}`}>
                <span>{testResults.rightChannelTested ? '✅' : '⏸️'}</span>
                <span>{t("rightChannelTest")}</span>
              </div>
              <div className={`flex items-center space-x-2 ${testResults.frequencyTested ? 'text-green-400' : 'text-white/60'}`}>
                <span>{testResults.frequencyTested ? '✅' : '⏸️'}</span>
                <span>{t("frequencyTest")}</span>
              </div>
              <div className={`flex items-center space-x-2 ${testResults.dynamicRangeTested ? 'text-green-400' : 'text-white/60'}`}>
                <span>{testResults.dynamicRangeTested ? '✅' : '⏸️'}</span>
                <span>{t("dynamicRangeTest")}</span>
              </div>
              <div className={`flex items-center space-x-2 ${testResults.stereoTested ? 'text-green-400' : 'text-white/60'}`}>
                <span>{testResults.stereoTested ? '✅' : '⏸️'}</span>
                <span>{t("stereoTest")}</span>
              </div>
            </div>

            {/* 基本测试要求提示 */}
            {!testResults.leftChannelTested || !testResults.rightChannelTested ? (
              <div className="mt-3 p-3 bg-blue-500/10 border border-blue-400/30 rounded-lg">
                <p className="text-blue-200 text-xs">
                  <Info className="h-4 w-4 inline mr-1" />
                  {t("basicTestRequired")}
                </p>
              </div>
            ) : (
              <div className="mt-3 p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
                <p className="text-green-200 text-xs">
                  ✅ {t("basicTestCompleted")}
                </p>
              </div>
            )}
          </div>

          {/* Output Device Selector */}
          {renderOutputDeviceSelector()}

          {/* Test Modules */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-8">
            {renderLeftRightChannelTest()}
            {renderFrequencyResponseTest()}
            {renderDynamicRangeTest()}
            {renderStereoImagingTest()}
          </div>

          {/* Stop All Audio Button */}
          {isPlaying && (
            <div className="mt-6 flex justify-center">
              <PrimaryButton 
                onClick={stopAllAudio}
                variant="secondary"
                size="lg"
                className="shadow-lg"
              >
                <Pause className="h-5 w-5 mr-2" />
                {t("stopAllAudio")}
              </PrimaryButton>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">
              {t("testingTips")}
            </h4>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>• {t("speakerTip1")}</li>
              <li>• {t("speakerTip2")}</li>
              <li>• {t("speakerTip3")}</li>
              <li>• {t("speakerTip4")}</li>
            </ul>
          </div>
        </GlassCard>
      </main>
    </div>
  );
}; 