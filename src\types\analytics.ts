// Google Analytics 4 类型定义

export interface GA4Config {
  measurementId: string;
  enabled: boolean;
  debug: boolean;
}

export interface GA4Event {
  action: string;
  category?: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

export interface DeviceTestEvent extends GA4Event {
  device_type: 'camera' | 'microphone' | 'speaker' | 'keyboard' | 'mouse' | 'network';
  test_result: 'success' | 'failure' | 'partial';
  test_duration?: number;
  error_message?: string;
}

export interface PageViewEvent {
  page_title: string;
  page_location: string;
  page_path: string;
  language?: string;
}

export interface UserInteractionEvent extends GA4Event {
  interaction_type: 'click' | 'scroll' | 'form_submit' | 'download' | 'external_link';
  element_id?: string;
  element_class?: string;
}

// 扩展 Window 接口以包含 gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'consent',
      targetId: string | Date,
      config?: any
    ) => void;
    dataLayer: any[];
  }
}

export interface ConsentSettings {
  analytics_storage: 'granted' | 'denied';
  ad_storage: 'granted' | 'denied';
  functionality_storage: 'granted' | 'denied';
  personalization_storage: 'granted' | 'denied';
  security_storage: 'granted' | 'denied';
}
