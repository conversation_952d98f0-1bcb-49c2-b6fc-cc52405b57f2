import React, { useState, useRef, useCallback } from "react";
import { Play, Pause, Volume2, VolumeX, Headphones, Speaker } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useLanguage } from "@/hooks/useLanguage";

interface EnhancedSpeakerTestProps {
  onNext: (result?: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack: () => void;
}

type TestType = 'left' | 'right' | 'stereo' | 'frequency';

export const EnhancedSpeakerTest: React.FC<EnhancedSpeakerTestProps> = ({ onNext, onBack }) => {
  const { t } = useLanguage();
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [currentTest, setCurrentTest] = useState<TestType>('stereo');
  const [testResults, setTestResults] = useState<Record<TestType, boolean>>({
    left: false,
    right: false,
    stereo: false,
    frequency: false
  });

  const audioContextRef = useRef<AudioContext | null>(null);
  const oscillatorRef = useRef<OscillatorNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const pannerRef = useRef<StereoPannerNode | null>(null);

  const stopCurrentTest = useCallback(() => {
    if (oscillatorRef.current) {
      oscillatorRef.current.stop();
      oscillatorRef.current = null;
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    setIsPlaying(false);
  }, []);

  const playTestTone = useCallback(async (type: TestType, frequency: number = 440, duration: number = 2000) => {
    if (isPlaying) {
      stopCurrentTest();
      return;
    }

    try {
      audioContextRef.current = new AudioContext();
      
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      oscillatorRef.current = audioContextRef.current.createOscillator();
      gainNodeRef.current = audioContextRef.current.createGain();
      pannerRef.current = audioContextRef.current.createStereoPanner();

      // Configure oscillator
      oscillatorRef.current.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
      oscillatorRef.current.type = "sine";

      // Configure gain
      gainNodeRef.current.gain.setValueAtTime(0, audioContextRef.current.currentTime);
      gainNodeRef.current.gain.linearRampToValueAtTime(volume * 0.3, audioContextRef.current.currentTime + 0.1);
      gainNodeRef.current.gain.linearRampToValueAtTime(0, audioContextRef.current.currentTime + duration / 1000 - 0.1);

      // Configure panning based on test type
      switch (type) {
        case 'left':
          pannerRef.current.pan.setValueAtTime(-1, audioContextRef.current.currentTime);
          break;
        case 'right':
          pannerRef.current.pan.setValueAtTime(1, audioContextRef.current.currentTime);
          break;
        case 'stereo':
        case 'frequency':
          pannerRef.current.pan.setValueAtTime(0, audioContextRef.current.currentTime);
          break;
      }

      // Connect nodes
      oscillatorRef.current.connect(gainNodeRef.current);
      gainNodeRef.current.connect(pannerRef.current);
      pannerRef.current.connect(audioContextRef.current.destination);

      oscillatorRef.current.start(audioContextRef.current.currentTime);
      oscillatorRef.current.stop(audioContextRef.current.currentTime + duration / 1000);

      setIsPlaying(true);
      setCurrentTest(type);

      setTimeout(() => {
        setIsPlaying(false);
        if (audioContextRef.current) {
          audioContextRef.current.close();
          audioContextRef.current = null;
        }
      }, duration);

    } catch (error) {
      console.error("Failed to play test sound:", error);
      setIsPlaying(false);
    }
  }, [isPlaying, volume, stopCurrentTest]);

  const playFrequencySweep = useCallback(async () => {
    if (isPlaying) {
      stopCurrentTest();
      return;
    }

    try {
      audioContextRef.current = new AudioContext();
      
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      oscillatorRef.current = audioContextRef.current.createOscillator();
      gainNodeRef.current = audioContextRef.current.createGain();

      // Frequency sweep from 200Hz to 2000Hz over 3 seconds
      const startTime = audioContextRef.current.currentTime;
      const duration = 3;

      oscillatorRef.current.frequency.setValueAtTime(200, startTime);
      oscillatorRef.current.frequency.exponentialRampToValueAtTime(2000, startTime + duration);
      oscillatorRef.current.type = "sine";

      gainNodeRef.current.gain.setValueAtTime(0, startTime);
      gainNodeRef.current.gain.linearRampToValueAtTime(volume * 0.2, startTime + 0.1);
      gainNodeRef.current.gain.linearRampToValueAtTime(0, startTime + duration - 0.1);

      oscillatorRef.current.connect(gainNodeRef.current);
      gainNodeRef.current.connect(audioContextRef.current.destination);

      oscillatorRef.current.start(startTime);
      oscillatorRef.current.stop(startTime + duration);

      setIsPlaying(true);
      setCurrentTest('frequency');

      setTimeout(() => {
        setIsPlaying(false);
        if (audioContextRef.current) {
          audioContextRef.current.close();
          audioContextRef.current = null;
        }
      }, duration * 1000);

    } catch (error) {
      console.error("Failed to play frequency sweep:", error);
      setIsPlaying(false);
    }
  }, [isPlaying, volume, stopCurrentTest]);

  const markTestPassed = (testType: TestType) => {
    setTestResults(prev => ({ ...prev, [testType]: true }));
  };

  // 只需要确认立体声测试和频率测试通过即可
  const allTestsPassed = testResults.stereo && testResults.frequency;

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {isPlaying ? (
            <Volume2 className="h-12 w-12 text-blue-400 animate-pulse" />
          ) : (
            <VolumeX className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("enhancedSpeakerTest")}</h2>
        <p className="text-white/70">
          {t("comprehensiveAudioTest")}
        </p>
      </div>

      <div className="space-y-6">
        {/* Volume Control */}
        <div>
          <label className="block text-white/80 text-sm font-medium mb-2">
            {t("volumeControl")}:
          </label>
          <div className="flex items-center space-x-4">
            <VolumeX className="h-5 w-5 text-white/60" />
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
            />
            <Volume2 className="h-5 w-5 text-white/60" />
          </div>
          <p className="text-white/60 text-sm mt-2">
            {t("volume")} {Math.round(volume * 100)}%
          </p>
        </div>

        {/* Stereo Test */}
        <div className="bg-white/5 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-white flex items-center">
              <Speaker className="h-5 w-5 mr-2" />
              {t("stereoTest")}
            </h3>
            {testResults.stereo && (
              <span className="text-green-400 text-sm">✓ {t("testPassed")}</span>
            )}
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <PrimaryButton 
              onClick={() => playTestTone('left', 440, 2000)}
              disabled={isPlaying}
              variant="outline"
              size="sm"
            >
              {isPlaying && currentTest === 'left' ? (
                <>
                  <Pause className="h-4 w-4" />
                  {t("playing")}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  {t("leftChannel")}
                </>
              )}
            </PrimaryButton>

            <PrimaryButton 
              onClick={() => playTestTone('right', 440, 2000)}
              disabled={isPlaying}
              variant="outline"
              size="sm"
            >
              {isPlaying && currentTest === 'right' ? (
                <>
                  <Pause className="h-4 w-4" />
                  {t("playing")}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  {t("rightChannel")}
                </>
              )}
            </PrimaryButton>

            <PrimaryButton 
              onClick={() => playTestTone('stereo', 440, 2000)}
              disabled={isPlaying}
              variant="outline"
              size="sm"
            >
              {isPlaying && currentTest === 'stereo' ? (
                <>
                  <Pause className="h-4 w-4" />
                  {t("playing")}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  {t("bothChannels")}
                </>
              )}
            </PrimaryButton>
          </div>

          <p className="text-white/60 text-sm mt-3">
            {t("testLeftRightChannels")}
          </p>

          {!testResults.stereo && (
            <div className="mt-4">
              <PrimaryButton 
                onClick={() => markTestPassed('stereo')}
                variant="secondary"
                size="sm"
              >
                {t("confirmStereoTestPassed")}
              </PrimaryButton>
            </div>
          )}
        </div>

        {/* Frequency Response Test */}
        <div className="bg-white/5 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-white flex items-center">
              <Headphones className="h-5 w-5 mr-2" />
              {t("frequencyResponseTest")}
            </h3>
            {testResults.frequency && (
              <span className="text-green-400 text-sm">✓ {t("testPassed")}</span>
            )}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <PrimaryButton 
              onClick={playFrequencySweep}
              disabled={isPlaying}
              variant="outline"
            >
              {isPlaying && currentTest === 'frequency' ? (
                <>
                  <Pause className="h-4 w-4" />
                  {t("sweeping")}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  {t("frequencySweep")}
                </>
              )}
            </PrimaryButton>

            <PrimaryButton 
              onClick={() => playTestTone('frequency', 1000, 3000)}
              disabled={isPlaying}
              variant="outline"
            >
              {isPlaying && currentTest === 'frequency' ? (
                <>
                  <Pause className="h-4 w-4" />
                  {t("playing")}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  {t("testTone1kHz")}
                </>
              )}
            </PrimaryButton>
          </div>

          <p className="text-white/60 text-sm mt-3">
            {t("testSpeakerFrequencyRange")}
          </p>

          {!testResults.frequency && (
            <div className="mt-4">
              <PrimaryButton 
                onClick={() => markTestPassed('frequency')}
                variant="secondary"
                size="sm"
              >
                {t("confirmFrequencyTestPassed")}
              </PrimaryButton>
            </div>
          )}
        </div>

        {/* Test Results Summary */}
        {allTestsPassed && (
          <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4">
            <h4 className="text-green-200 font-medium mb-2">{t("speakerTestComplete")}</h4>
            <p className="text-green-200/80 text-sm">
              {t("allAudioTestsPassed")}
            </p>
          </div>
        )}

        {/* Audio Tips */}
        <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
          <h4 className="text-blue-200 font-medium mb-2">{t("audioOptimizationTips")}</h4>
          <ul className="text-blue-200/80 text-sm space-y-1">
            <li>{t("audioTip1")}</li>
            <li>{t("audioTip2")}</li>
            <li>{t("audioTip3")}</li>
            <li>{t("audioTip4")}</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
        <PrimaryButton onClick={onBack} variant="outline">
          {t("back")}
        </PrimaryButton>
        <PrimaryButton onClick={() => {
          // 报告扬声器测试结果
          let failureReason = "";
          if (!allTestsPassed) {
            const missingTests = [];
            if (!testResults.stereo) missingTests.push(t("stereoTestNotCompleted"));
            if (!testResults.frequency) missingTests.push(t("frequencyTestNotCompleted"));
            failureReason = `${t("requiredTestsNotCompleted")}: ${missingTests.join(", ")}`;
          }
          
          onNext({
            passed: allTestsPassed,
            failureReason: allTestsPassed ? undefined : failureReason,
            details: {
              stereoTestPassed: testResults.stereo,
              frequencyTestPassed: testResults.frequency,
              volume: volume,
              allTestsCompleted: allTestsPassed
            }
          });
        }} disabled={!allTestsPassed}>
          {t("nextCameraTest")}
        </PrimaryButton>
      </div>

      <style>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3B82F6;
          cursor: pointer;
          border: 2px solid #ffffff;
        }

        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3B82F6;
          cursor: pointer;
          border: 2px solid #ffffff;
        }
      `}</style>
    </GlassCard>
  );
};