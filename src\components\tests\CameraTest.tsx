import React from "react";
import { <PERSON>, CameraOff, Monitor } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useCamera } from "@/hooks/useCamera";
import { useLanguage } from "@/hooks/useLanguage";

interface CameraTestProps {
  onNext: () => void;
  onBack: () => void;
}

export const CameraTest: React.FC<CameraTestProps> = ({ onNext, onBack }) => {
  const { t } = useLanguage();
  const {
    isActive,
    devices,
    selectedDevice,
    error,
    videoRef,
    startCamera,
    stopCamera,
    setSelectedDevice,
    hasPermission,
  } = useCamera();

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {isActive ? (
            <Camera className="h-12 w-12 text-green-400" />
          ) : (
            <CameraOff className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("cameraTestTitle")}</h2>
        <p className="text-white/70">
          {t("cameraTestDesc")}
        </p>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {devices.length > 0 && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("selectCamera")}:
            </label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50"
            >
              {devices.map((device) => (
                <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                  {device.label}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="relative">
          <label className="block text-white/80 text-sm font-medium mb-2">
            {t("cameraPreview")}:
          </label>
          <div className="relative bg-black/30 rounded-xl overflow-hidden border border-white/20 aspect-video">
            {isActive ? (
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Monitor className="h-16 w-16 text-white/30 mx-auto mb-4" />
                  <p className="text-white/50">{t("cameraPlaceholder")}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-center">
          {!isActive ? (
            <PrimaryButton onClick={startCamera} size="lg">
              <Camera className="h-5 w-5" />
              {t("startCameraTest")}
            </PrimaryButton>
          ) : (
            <PrimaryButton onClick={stopCamera} variant="secondary" size="lg">
              <CameraOff className="h-5 w-5" />
              {t("stopCamera")}
            </PrimaryButton>
          )}
        </div>

        {isActive && (
          <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4">
            <h4 className="text-green-200 font-medium mb-2">{t("cameraWorking")}</h4>
            <p className="text-green-200/80 text-sm">
              {t("cameraWorkingDesc")}
            </p>
          </div>
        )}

        {!isActive && (
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">{t("cameraTips")}</h4>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>{t("cameraTip1")}</li>
              <li>{t("cameraTip2")}</li>
              <li>{t("cameraTip3")}</li>
              <li>{t("cameraTip4")}</li>
            </ul>
          </div>
        )}
      </div>

      <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
        <PrimaryButton onClick={onBack} variant="outline">
          {t("backSpeakerTest")}
        </PrimaryButton>
        <PrimaryButton onClick={onNext}>
          {t("finishTesting")}
        </PrimaryButton>
      </div>
    </GlassCard>
  );
};