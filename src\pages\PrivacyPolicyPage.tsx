import React from 'react';
import { ArrowLeft, Shield, Calendar, Mail } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { GlassCard } from '@/components/ui/GlassCard';
import { PrimaryButton } from '@/components/ui/PrimaryButton';

const PrivacyPolicyPage: React.FC = () => {
  const { t, language } = useLanguage();

  const formatContent = (content: string) => {
    return content.split('\n').map((line, index) => {
      if (line.startsWith('• **') && line.includes('**:')) {
        // 格式化列表项
        const parts = line.split('**:');
        const title = parts[0].replace('• **', '');
        const description = parts[1];
        return (
          <li key={index} className="mb-2">
            <strong className="text-white">{title}:</strong>
            <span className="text-white/80">{description}</span>
          </li>
        );
      } else if (line.startsWith('• **') && line.endsWith('**')) {
        // 格式化粗体列表项
        const title = line.replace('• **', '').replace('**', '');
        return (
          <li key={index} className="mb-1">
            <strong className="text-white">{title}</strong>
          </li>
        );
      } else if (line.startsWith('• ')) {
        // 普通列表项
        return (
          <li key={index} className="mb-1 text-white/80">
            {line.replace('• ', '')}
          </li>
        );
      } else if (line.trim() === '') {
        // 空行
        return <br key={index} />;
      } else {
        // 普通段落
        return (
          <p key={index} className="mb-4 text-white/80 leading-relaxed">
            {line}
          </p>
        );
      }
    });
  };

  const sections = [
    {
      title: t('dataCollectionTitle'),
      content: t('dataCollectionContent')
    },
    {
      title: t('dataUsageTitle'),
      content: t('dataUsageContent')
    },
    {
      title: t('thirdPartyServicesTitle'),
      content: t('thirdPartyServicesContent')
    },
    {
      title: t('userRightsTitle'),
      content: t('userRightsContent')
    },
    {
      title: t('dataSecurityTitle'),
      content: t('dataSecurityContent')
    },
    {
      title: t('contactInformationTitle'),
      content: t('contactInformationContent')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="mb-8">
          <Link 
            to={`/${language}`}
            className="inline-flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('backToHome')}
          </Link>
        </div>

        {/* 主要内容 */}
        <GlassCard className="max-w-4xl mx-auto">
          <div className="p-8">
            {/* 标题 */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Shield className="h-8 w-8 text-blue-400" />
                <h1 className="text-3xl font-bold text-white">
                  {t('privacyPolicyTitle')}
                </h1>
              </div>
              <div className="flex items-center justify-center gap-4 text-sm text-white/60">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{t('lastUpdated')}: 2024-01-19</span>
                </div>
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  <span>{t('effectiveDate')}: 2024-01-19</span>
                </div>
              </div>
            </div>

            {/* 介绍 */}
            <div className="mb-8">
              <p className="text-white/80 leading-relaxed text-lg">
                {t('privacyIntroduction')}
              </p>
            </div>

            {/* 内容章节 */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <section key={index} className="border-l-4 border-blue-400 pl-6">
                  <h2 className="text-xl font-semibold text-white mb-4">
                    {section.title}
                  </h2>
                  <div className="prose prose-invert max-w-none">
                    {formatContent(section.content)}
                  </div>
                </section>
              ))}
            </div>

            {/* 底部操作 */}
            <div className="mt-12 pt-8 border-t border-white/10">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to={`/${language}/cookie-policy`}>
                  <PrimaryButton variant="outline" className="w-full sm:w-auto">
                    {t('cookiePolicy')}
                  </PrimaryButton>
                </Link>
                <Link to={`/${language}`}>
                  <PrimaryButton className="w-full sm:w-auto">
                    {t('backToHome')}
                  </PrimaryButton>
                </Link>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
