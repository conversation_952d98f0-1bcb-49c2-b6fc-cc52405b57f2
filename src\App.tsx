import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import AnalyticsProvider from "@/components/providers/AnalyticsProvider";
import { ScenarioSelectionPage } from "./pages/ScenarioSelectionPage";
import PrivacyPolicyPage from "./pages/PrivacyPolicyPage";
import CookiePolicyPage from "./pages/CookiePolicyPage";
import { TestWorkflowPage } from "./pages/TestWorkflowPage";
import { WorkflowDemoPage } from "./pages/WorkflowDemoPage";
import { TestScenariosPage } from "./pages/TestScenariosPage";
import { ToolsPage } from "./pages/ToolsPage";
import { KeyboardTestPage } from "./pages/KeyboardTestPage";
import { MouseTestPage } from "./pages/MouseTestPage";
import { MicrophoneTestPage } from "./pages/MicrophoneTestPage";
import { HeadphonesTestPage } from "./pages/HeadphonesTestPage";
import { CameraTestPage } from "./pages/CameraTestPage";
import { NetworkTestPage } from "./pages/NetworkTestPage";
import NotFound from "./pages/NotFound";
import LanguageLayout from "./components/layouts/LanguageLayout";
import RootRedirect from "./components/layouts/RootRedirect";
import LegacyRedirect from "./components/layouts/LegacyRedirect";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter basename="/">
        <AnalyticsProvider>
        <Routes>
          <Route path="/" element={<RootRedirect />} />
          <Route path="/:lang" element={<LanguageLayout />}>
            <Route index element={<ScenarioSelectionPage />} />
            <Route path="test" element={<TestScenariosPage />} />
            <Route path="test/:scenarioName" element={<TestWorkflowPage />} />
            <Route path="demo/workflow" element={<WorkflowDemoPage />} />
            <Route path="tools" element={<ToolsPage />} />
            <Route path="tools/network" element={<NetworkTestPage />} />
            <Route path="tools/keyboard" element={<KeyboardTestPage />} />
            <Route path="tools/mouse" element={<MouseTestPage />} />
            <Route path="tools/microphone" element={<MicrophoneTestPage />} />
            <Route path="tools/camera" element={<CameraTestPage />} />
            <Route path="tools/headphones" element={<HeadphonesTestPage />} />
            <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="cookie-policy" element={<CookiePolicyPage />} />
          </Route>
          {/* Legacy routes - redirect to language-prefixed versions */}
          <Route path="/test" element={<LegacyRedirect />} />
          <Route path="/test/:scenarioName" element={<LegacyRedirect />} />
          <Route path="/tools" element={<LegacyRedirect />} />
          <Route path="/tools/network" element={<LegacyRedirect />} />
          <Route path="/tools/keyboard" element={<LegacyRedirect />} />
          <Route path="/tools/mouse" element={<LegacyRedirect />} />
          <Route path="/tools/microphone" element={<LegacyRedirect />} />
          <Route path="/tools/camera" element={<LegacyRedirect />} />
          <Route path="/tools/headphones" element={<LegacyRedirect />} />
          
          {/* CATCH-ALL should be last */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </AnalyticsProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
