import { Outlet, useParams } from 'react-router-dom';
import { LanguageProvider } from '@/hooks/useLanguage';
import { SUPPORTED_LANGUAGES, Language, isValidLanguage } from '@/config/languages';
import { usePageTracking } from '@/hooks/useAnalytics';
import { PrivacyManager } from '@/components/privacy/PrivacyManager';
import NotFound from '@/pages/NotFound';

const LanguageLayout = () => {
  const { lang } = useParams<{ lang: string }>();

  // 启用页面跟踪
  usePageTracking();

  if (!lang || !isValidLanguage(lang)) {
    return <NotFound />;
  }

  return (
    <LanguageProvider language={lang}>
      <Outlet />
      {/* 隐私管理器 - 在语言提供者内部 */}
      <PrivacyManager />
    </LanguageProvider>
  );
};
export default LanguageLayout; 