import React, { useState, useRef } from "react";
import { Play, Pause, Volume2, VolumeX } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useLanguage } from "@/hooks/useLanguage";

interface SpeakerTestProps {
  onNext: () => void;
  onBack: () => void;
}

export const SpeakerTest: React.FC<SpeakerTestProps> = ({ onNext, onBack }) => {
  const { t } = useLanguage();
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Generate a test tone using Web Audio API
  const generateTestTone = () => {
    const audioContext = new AudioContext();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
    oscillator.type = "sine";

    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume * 0.1, audioContext.currentTime + 0.1);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 2);

    setIsPlaying(true);
    setTimeout(() => setIsPlaying(false), 2000);
  };

  const playTestSound = () => {
    if (isPlaying) return;
    
    try {
      generateTestTone();
    } catch (error) {
      console.error("Failed to play test sound:", error);
    }
  };

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {isPlaying ? (
            <Volume2 className="h-12 w-12 text-blue-400 animate-pulse" />
          ) : (
            <VolumeX className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("speakerTestTitle2")}</h2>
        <p className="text-white/70">
          {t("speakerTestDesc2")}
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-white/80 text-sm font-medium mb-2">
            {t("volumeLevel")}:
          </label>
          <div className="flex items-center space-x-4">
            <VolumeX className="h-5 w-5 text-white/60" />
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
            />
            <Volume2 className="h-5 w-5 text-white/60" />
          </div>
          <p className="text-white/60 text-sm mt-2">
            {t("volume")} {Math.round(volume * 100)}%
          </p>
        </div>

        <div className="bg-white/5 rounded-xl p-6 text-center">
          <h3 className="text-lg font-medium text-white mb-4">{t("testAudioPlayback")}</h3>
          <p className="text-white/70 mb-6">
            {t("testAudioDesc")}
          </p>
          
          <PrimaryButton 
            onClick={playTestSound} 
            disabled={isPlaying}
            size="lg"
            className="mx-auto"
          >
            {isPlaying ? (
              <>
                <Pause className="h-5 w-5" />
                {t("playingTestSound")}
              </>
            ) : (
              <>
                <Play className="h-5 w-5" />
                {t("playTestSound")}
              </>
            )}
          </PrimaryButton>
          
          {isPlaying && (
            <div className="mt-4">
              <div className="animate-pulse text-blue-400 text-sm">
                {t("playingTone")}
              </div>
            </div>
          )}
        </div>

        <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
          <h4 className="text-blue-200 font-medium mb-2">{t("troubleshootingTips")}</h4>
          <ul className="text-blue-200/80 text-sm space-y-1">
            <li>{t("speakerTip1")}</li>
            <li>{t("speakerTip2")}</li>
            <li>{t("speakerTip3")}</li>
            <li>{t("speakerTip4")}</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
        <PrimaryButton onClick={onBack} variant="outline">
          {t("backMicrophone")}
        </PrimaryButton>
        <PrimaryButton onClick={onNext}>
          {t("nextCameraTest")}
        </PrimaryButton>
      </div>

      <style>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3B82F6;
          cursor: pointer;
          border: 2px solid #ffffff;
        }

        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3B82F6;
          cursor: pointer;
          border: 2px solid #ffffff;
        }
      `}</style>
    </GlassCard>
  );
};