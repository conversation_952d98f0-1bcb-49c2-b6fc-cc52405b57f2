import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { generateBreadcrumbs, generateBreadcrumbSchema } from '@/lib/seoUtils';
import { cn } from '@/lib/utils';

interface SEOBreadcrumbProps {
  className?: string;
  showHome?: boolean;
  maxItems?: number;
}

export const SEOBreadcrumb: React.FC<SEOBreadcrumbProps> = ({
  className,
  showHome = true,
  maxItems = 5
}) => {
  const location = useLocation();
  const { t, language } = useLanguage();
  
  // 生成面包屑数据
  const breadcrumbs = generateBreadcrumbs(location.pathname, t, language);
  
  // 限制显示的面包屑数量
  const displayBreadcrumbs = breadcrumbs.slice(0, maxItems);
  
  // 生成结构化数据
  React.useEffect(() => {
    const schema = generateBreadcrumbSchema(breadcrumbs, window.location.origin);
    
    // 清除现有的面包屑结构化数据
    const existingSchema = document.querySelector('script[data-breadcrumb-schema]');
    if (existingSchema) {
      existingSchema.remove();
    }
    
    // 添加新的结构化数据
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-breadcrumb-schema', 'true');
    script.textContent = JSON.stringify(schema);
    document.head.appendChild(script);
    
    return () => {
      const schemaToRemove = document.querySelector('script[data-breadcrumb-schema]');
      if (schemaToRemove) {
        schemaToRemove.remove();
      }
    };
  }, [breadcrumbs]);
  
  // 如果只有首页，不显示面包屑
  if (displayBreadcrumbs.length <= 1) {
    return null;
  }
  
  return (
    <nav 
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center space-x-1 text-sm text-white/70 mb-6 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
        className
      )}
    >
      <ol className="flex items-center space-x-1" itemScope itemType="https://schema.org/BreadcrumbList">
        {displayBreadcrumbs.map((item, index) => {
          const isLast = index === displayBreadcrumbs.length - 1;
          const isFirst = index === 0;
          
          return (
            <li 
              key={item.position}
              className="flex items-center"
              itemProp="itemListElement"
              itemScope
              itemType="https://schema.org/ListItem"
            >
              {index > 0 && (
                <ChevronRight className="w-4 h-4 mx-1 text-white/50" />
              )}
              
              {isLast ? (
                <span 
                  className="text-white font-medium truncate max-w-[150px]"
                  itemProp="name"
                  aria-current="page"
                >
                  {isFirst && showHome && <Home className="w-4 h-4 mr-1 inline" />}
                  {item.name}
                </span>
              ) : (
                <Link
                  to={item.url}
                  className={cn(
                    "hover:text-white transition-colors duration-200 truncate max-w-[150px]",
                    isFirst && "flex items-center"
                  )}
                  itemProp="item"
                >
                  <span itemProp="name">
                    {isFirst && showHome && <Home className="w-4 h-4 mr-1 inline" />}
                    {item.name}
                  </span>
                </Link>
              )}
              
              <meta itemProp="position" content={item.position.toString()} />
            </li>
          );
        })}
      </ol>
    </nav>
  );
}; 