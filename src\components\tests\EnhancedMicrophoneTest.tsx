import React, { useEffect, useRef } from "react";
import { Mic, MicOff, Volume2, Activity, AlertTriangle, CheckCircle } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useMicrophone } from "@/hooks/useMicrophone";
import { useLanguage } from "@/hooks/useLanguage";

interface EnhancedMicrophoneTestProps {
  onNext?: (result?: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack?: () => void;
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
}

export const EnhancedMicrophoneTest: React.FC<EnhancedMicrophoneTestProps> = ({ onNext, onBack, onTestResult }) => {
  const { t } = useLanguage();
  const {
    permissionState,
    audioLevel,
    devices,
    selectedDevice,
    error,
    startTest,
    stopTest,
    setSelectedDevice,
    hasPermission,
    audioQuality,
    volume,
    frequencyData
  } = useMicrophone();

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const spectrumCanvasRef = useRef<HTMLCanvasElement>(null);

  // Draw audio waveform
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || permissionState !== 'granted') return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw background
      ctx.fillStyle = "rgba(255, 255, 255, 0.1)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw level indicator
      const normalizedLevel = Math.max(0, (volume + 60) / 60); // Convert dBFS to 0-1
      const barWidth = canvas.width * normalizedLevel;
      
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "#10B981");
      gradient.addColorStop(0.7, "#F59E0B");
      gradient.addColorStop(1, "#EF4444");

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, barWidth, canvas.height);

      // Draw dB scale
      ctx.fillStyle = "rgba(255, 255, 255, 0.6)";
      ctx.font = "12px sans-serif";
      ctx.fillText(`${volume.toFixed(1)} dBFS`, 10, 20);

      requestAnimationFrame(draw);
    };

    draw();
  }, [permissionState, volume]);

  // 自动报告测试结果
  useEffect(() => {
    if (onTestResult && hasPermission && audioQuality) {
      const testPassed = audioQuality.clarity === 'excellent' || audioQuality.clarity === 'good';
      let failureReason;

      if (!testPassed) {
        const issues = [];
        if (audioQuality.signalToNoiseRatio < 10) issues.push(t("signalToNoiseRatioLow"));
        if (audioQuality.backgroundNoiseLevel > -30) issues.push(t("backgroundNoiseTooHigh"));
        if (audioQuality.distortionLevel > 0.1) issues.push(t("audioDistortionHigh"));
        if (audioQuality.echoDetected) issues.push(t("echoDetected"));
        failureReason = issues.length > 0 ? `${t("audioQualityIssues")}: ${issues.join(", ")}` : t("audioQualityPoor");
      }

      onTestResult({
        passed: testPassed,
        failureReason: failureReason,
        details: {
          audioQuality: audioQuality,
          selectedDevice: selectedDevice?.label,
          volume: volume
        }
      });
    }
  }, [onTestResult, hasPermission, audioQuality, selectedDevice, volume, t]);

  // Draw frequency spectrum
  useEffect(() => {
    const canvas = spectrumCanvasRef.current;
    if (!canvas || !frequencyData || permissionState !== 'granted') return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const barWidth = canvas.width / frequencyData.length;
      
      for (let i = 0; i < frequencyData.length; i++) {
        const barHeight = (frequencyData[i] / 255) * canvas.height;
        
        const hue = (i / frequencyData.length) * 240; // Blue to red spectrum
        ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
        ctx.fillRect(i * barWidth, canvas.height - barHeight, barWidth - 1, barHeight);
      }

      requestAnimationFrame(draw);
    };

    draw();
  }, [frequencyData, permissionState]);

  const getQualityColor = (clarity: string) => {
    switch (clarity) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'fair': return 'text-yellow-400';
      case 'poor': return 'text-red-400';
      default: return 'text-white/60';
    }
  };

  const getQualityIcon = (clarity: string) => {
    switch (clarity) {
      case 'excellent': return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'good': return <CheckCircle className="h-5 w-5 text-blue-400" />;
      case 'fair': return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'poor': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default: return <Activity className="h-5 w-5 text-white/60" />;
    }
  };

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {permissionState === 'granted' ? (
            <Mic className="h-12 w-12 text-green-400" />
          ) : (
            <MicOff className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("enhancedMicrophoneTest")}</h2>
        <p className="text-white/70">
          {t("comprehensiveMicTest")}
        </p>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {devices.length > 0 && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("selectMicrophone")}:
            </label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50"
            >
              {devices.map((device) => (
                <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                  {device.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Audio Level Visualization */}
        {permissionState === 'granted' && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("audioLevel")}:
            </label>
            <canvas
              ref={canvasRef}
              width={400}
              height={60}
              className="w-full rounded-xl border border-white/20"
            />
          </div>
        )}

        {/* Frequency Spectrum */}
        {permissionState === 'granted' && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("frequencyAnalysis")}:
            </label>
            <canvas
              ref={spectrumCanvasRef}
              width={400}
              height={100}
              className="w-full rounded-xl border border-white/20"
            />
          </div>
        )}

        {/* Audio Quality Analysis */}
        {audioQuality && (
          <div className="bg-white/5 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-white">{t("audioQualityAnalysis")}</h3>
              <div className="flex items-center space-x-2">
                {getQualityIcon(audioQuality.clarity)}
                <span className={`font-medium ${getQualityColor(audioQuality.clarity)}`}>
                  {audioQuality.clarity === 'excellent' && t("excellent")}
                  {audioQuality.clarity === 'good' && t("good")}
                  {audioQuality.clarity === 'fair' && t("fair")}
                  {audioQuality.clarity === 'poor' && t("poor")}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">{t("signalToNoiseRatio")}:</span>
                  <span className="text-white font-medium">{audioQuality.signalToNoiseRatio} dB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">{t("backgroundNoiseLevel")}:</span>
                  <span className="text-white font-medium">{audioQuality.backgroundNoiseLevel} dB</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">{t("distortionLevel")}:</span>
                  <span className="text-white font-medium">{(audioQuality.distortionLevel * 100).toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">{t("echoDetection")}:</span>
                  <span className={`font-medium ${audioQuality.echoDetected ? 'text-red-400' : 'text-green-400'}`}>
                    {audioQuality.echoDetected ? t("detected") : t("notDetected")}
                  </span>
                </div>
              </div>
            </div>

            {/* Quality Recommendations */}
            <div className="mt-4 pt-4 border-t border-white/10">
              <h4 className="text-white/90 font-medium mb-2">{t("recommendations")}</h4>
              {audioQuality.clarity === 'excellent' && (
                <p className="text-green-200/80 text-sm">
                  {t("micExcellentDesc")}
                </p>
              )}
              {audioQuality.clarity === 'good' && (
                <p className="text-blue-200/80 text-sm">
                  {t("micGoodDesc")}
                </p>
              )}
              {audioQuality.clarity === 'fair' && (
                <p className="text-yellow-200/80 text-sm">
                  {t("micFairDesc")}
                </p>
              )}
              {audioQuality.clarity === 'poor' && (
                <p className="text-red-200/80 text-sm">
                  {t("micPoorDesc")}
                </p>
              )}
              {audioQuality.echoDetected && (
                <p className="text-red-200/80 text-sm mt-2">
                  {t("echoDetectedWarning")}
                </p>
              )}
            </div>
          </div>
        )}

        <div className="flex justify-center">
          {permissionState === 'idle' ? (
            <PrimaryButton onClick={startTest} size="lg">
              <Volume2 className="h-5 w-5" />
              {t("startMicrophoneTest")}
            </PrimaryButton>
          ) : permissionState === 'pending' ? (
            <PrimaryButton disabled size="lg">
              <Activity className="h-5 w-5 animate-spin" />
              {t("requestingPermission")}
            </PrimaryButton>
          ) : permissionState === 'denied' ? (
            <div className="text-center">
              <p className="text-red-200 mb-4">{t("microphonePermissionDenied")}</p>
              <PrimaryButton onClick={startTest} size="lg">
                {t("requestPermissionAgain")}
              </PrimaryButton>
            </div>
          ) : (
            <PrimaryButton onClick={stopTest} variant="secondary" size="lg">
              <MicOff className="h-5 w-5" />
              {t("stopTest")}
            </PrimaryButton>
          )}
        </div>

        {permissionState !== 'granted' && (
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">{t("micOptimizationTips")}</h4>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>{t("micTip1")}</li>
              <li>{t("micTip2")}</li>
              <li>{t("micTip3")}</li>
              <li>{t("micTip4")}</li>
            </ul>
          </div>
        )}
      </div>

      {/* 保留兼容性的导航按钮 - 仅在使用旧API时显示 */}
      {!onTestResult && (
        <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
          <PrimaryButton onClick={onBack} variant="outline">
            {t("back")}
          </PrimaryButton>
          <PrimaryButton onClick={() => {
            // 报告麦克风测试结果
            let testPassed = false;
            let failureReason = "";
            
            if (permissionState === 'denied') {
              failureReason = t("micPermissionDenied");
            } else if (permissionState !== 'granted') {
              failureReason = t("micPermissionNotGranted");
            } else if (devices.length === 0) {
              failureReason = t("noMicrophoneDevices");
            } else if (audioLevel === 0) {
              failureReason = t("noAudioInput");
            } else if (audioQuality && audioQuality.clarity === 'poor') {
              const issues = [];
              if (audioQuality.signalToNoiseRatio < 10) issues.push(t("signalToNoiseRatioLow"));
              if (audioQuality.backgroundNoiseLevel > -30) issues.push(t("backgroundNoiseTooHigh"));
              if (audioQuality.distortionLevel > 0.1) issues.push(t("audioDistortionHigh"));
              if (audioQuality.echoDetected) issues.push(t("echoDetected"));
              failureReason = issues.length > 0 ? `${t("audioQualityIssues")}: ${issues.join(", ")}` : t("audioQualityPoor");
            } else {
              testPassed = true;
            }
            
            onNext?.({
              passed: testPassed,
              failureReason: testPassed ? undefined : failureReason,
              details: {
                permissionGranted: permissionState === 'granted',
                audioQuality: audioQuality,
                deviceCount: devices.length,
                selectedDevice: selectedDevice,
                hasAudioLevel: audioLevel > 0
              }
            });
          }}>
            {t("nextSpeakerTest")}
          </PrimaryButton>
        </div>
      )}
    </GlassCard>
  );
};