import { renderHook, act } from '@testing-library/react';
import { useWorkflowManager } from './useWorkflowManager';
import { TestStepConfig, NavigationAction, TestStatus, EnhancedTestResult } from '@/types/testWorkflow';

describe('useWorkflowManager', () => {
  const mockSteps: TestStepConfig[] = [
    {
      key: 'network',
      title: 'Network Test',
      number: 1,
      allowProceedOnFailure: true,
      canSkip: false
    },
    {
      key: 'camera',
      title: 'Camera Test',
      number: 2,
      allowProceedOnFailure: false,
      canSkip: true
    },
    {
      key: 'summary',
      title: 'Summary',
      number: 3,
      allowProceedOnFailure: true,
      canSkip: false
    }
  ];

  it('initializes with correct state', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    expect(result.current.state.currentStepIndex).toBe(0);
    expect(result.current.state.steps).toHaveLength(3);
    expect(result.current.state.isComplete).toBe(false);
    expect(result.current.getCurrentStep()?.config.key).toBe('network');
  });

  it('allows proceeding when test passes', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    const testResult: EnhancedTestResult = {
      status: TestStatus.COMPLETED,
      passed: true,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(0, testResult);
    });

    const navigationControl = result.current.getNavigationControl();
    expect(navigationControl.canGoNext).toBe(true);
  });

  it('allows proceeding when test fails but allowProceedOnFailure is true', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    const testResult: EnhancedTestResult = {
      status: TestStatus.FAILED,
      passed: false,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(0, testResult);
    });

    const navigationControl = result.current.getNavigationControl();
    expect(navigationControl.canGoNext).toBe(true); // network step allows proceed on failure
  });

  it('prevents proceeding when test fails and allowProceedOnFailure is false', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    // Move to camera step (index 1)
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });

    const testResult: EnhancedTestResult = {
      status: TestStatus.FAILED,
      passed: false,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(1, testResult);
    });

    const navigationControl = result.current.getNavigationControl();
    expect(navigationControl.canGoNext).toBe(false); // camera step doesn't allow proceed on failure
  });

  it('shows skip button when canSkip is true', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    // Move to camera step (index 1)
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });

    const navigationControl = result.current.getNavigationControl();
    expect(navigationControl.canSkip).toBe(true); // camera step can be skipped
  });

  it('executes next action correctly', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    const testResult: EnhancedTestResult = {
      status: TestStatus.COMPLETED,
      passed: true,
      timestamp: new Date()
    };

    act(() => {
      result.current.executeAction(NavigationAction.NEXT, testResult);
    });

    expect(result.current.state.currentStepIndex).toBe(1);
    expect(result.current.getCurrentStep()?.config.key).toBe('camera');
    expect(result.current.state.steps[0].result).toEqual(testResult);
  });

  it('executes back action correctly', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    // Move to step 1
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });

    // Move back to step 0
    act(() => {
      result.current.executeAction(NavigationAction.BACK);
    });

    expect(result.current.state.currentStepIndex).toBe(0);
    expect(result.current.getCurrentStep()?.config.key).toBe('network');
  });

  it('executes skip action correctly', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    act(() => {
      result.current.executeAction(NavigationAction.SKIP);
    });

    const currentStep = result.current.getCurrentStep();
    expect(currentStep?.result?.status).toBe(TestStatus.SKIPPED);
    expect(currentStep?.result?.passed).toBe(false);
  });

  it('executes retry action correctly', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    // Set a failed result first
    const failedResult: EnhancedTestResult = {
      status: TestStatus.FAILED,
      passed: false,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(0, failedResult);
    });

    // Retry the step
    act(() => {
      result.current.executeAction(NavigationAction.RETRY);
    });

    const currentStep = result.current.getCurrentStep();
    expect(currentStep?.result).toBeUndefined(); // Result should be cleared
    expect(currentStep?.retryCount).toBe(1);
  });

  it('shows retry button when test fails and can retry', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    const failedResult: EnhancedTestResult = {
      status: TestStatus.FAILED,
      passed: false,
      canRetry: true,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(0, failedResult);
    });

    const navigationControl = result.current.getNavigationControl();
    expect(navigationControl.showRetryButton).toBe(true);
    expect(navigationControl.canRetry).toBe(true);
  });

  it('marks workflow as complete when reaching the end', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    // Move through all steps
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });
    act(() => {
      result.current.executeAction(NavigationAction.NEXT);
    });

    expect(result.current.state.isComplete).toBe(true);
  });

  it('resets step correctly', () => {
    const { result } = renderHook(() => useWorkflowManager({ steps: mockSteps }));

    const testResult: EnhancedTestResult = {
      status: TestStatus.COMPLETED,
      passed: true,
      timestamp: new Date()
    };

    act(() => {
      result.current.updateStepResult(0, testResult);
    });

    act(() => {
      result.current.resetStep(0);
    });

    const currentStep = result.current.getCurrentStep();
    expect(currentStep?.result).toBeUndefined();
    expect(currentStep?.retryCount).toBe(0);
  });
});
