# Project Structure

## Directory Organization

```
/
├── src/                    # Source code
│   ├── components/         # Reusable UI components
│   │   ├── ui/             # Base UI components (shadcn/ui)
│   │   └── layouts/        # Layout components
│   ├── config/             # Configuration files
│   │   └── seo.ts          # SEO configuration
│   ├── hooks/              # Custom React hooks
│   │   └── useSEO.ts       # SEO hook
│   ├── lib/                # Utility functions and libraries
│   │   └── seoUtils.ts     # SEO utilities
│   ├── locales/            # Translation files for i18n
│   ├── pages/              # Page components
│   │   ├── ScenarioSelectionPage.tsx
│   │   ├── TestScenariosPage.tsx
│   │   ├── TestWorkflowPage.tsx
│   │   ├── ToolsPage.tsx
│   │   ├── KeyboardTestPage.tsx
│   │   ├── MouseTestPage.tsx
│   │   ├── MicrophoneTestPage.tsx
│   │   ├── HeadphonesTestPage.tsx
│   │   ├── CameraTestPage.tsx
│   │   ├── NetworkTestPage.tsx
│   │   └── NotFound.tsx
│   ├── App.tsx             # Main application component
│   ├── App.css             # App-specific styles
│   ├── main.tsx            # Application entry point
│   ├── index.css           # Global styles
│   └── vite-env.d.ts       # Vite type declarations
├── public/                 # Static assets
│   ├── favicon.ico         # Browser favicon
│   ├── favicon.svg         # SVG favicon
│   ├── logo.png            # Application logo
│   ├── manifest.json       # Web app manifest
│   ├── robots.txt          # Search engine instructions
│   └── sitemap.xml         # Site structure for search engines
├── docs/                   # Documentation
│   └── SEO_OPTIMIZATION.md # SEO documentation
└── dist/                   # Build output (generated)
```

## Key Architectural Patterns

### Component Organization
- **UI Components**: Base components from shadcn/ui in `src/components/ui/`
- **Layout Components**: Page layouts in `src/components/layouts/`
- **Page Components**: Full pages in `src/pages/`

### Routing Structure
- Language-prefixed routes (`/:lang/*`)
- Legacy route redirects for backward compatibility
- Nested routes for test scenarios and tools

### State Management
- React Query for server state
- React context for global state (language, theme)
- Local component state for UI interactions

### Code Conventions

#### File Naming
- React components: PascalCase (e.g., `ButtonComponent.tsx`)
- Utilities and hooks: camelCase (e.g., `useWindowSize.ts`)
- Configuration files: camelCase (e.g., `seo.ts`)

#### Import Order
1. React and framework imports
2. Third-party libraries
3. Local components and utilities (using path aliases)
4. Styles

#### Component Structure
- Functional components with TypeScript interfaces
- Props destructuring at the top
- Hooks before render logic
- Return JSX at the end

### Multi-language Support
- Language files in `src/locales/`
- Language selection in URL path (`/:lang/*`)
- Language-specific SEO metadata
- Automatic redirection based on browser language

### SEO Implementation
- Dynamic meta tags via `useSEO` hook
- Page-specific SEO configurations
- Structured data for rich search results
- Multi-language sitemap