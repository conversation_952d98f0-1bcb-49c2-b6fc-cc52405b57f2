import React, { useEffect } from 'react';
import { useDeviceTestTracking } from '@/hooks/useAnalytics';
import { DeviceTestEvent } from '@/types/analytics';

interface DeviceTestTrackerProps {
  deviceType: DeviceTestEvent['device_type'];
  isTestActive: boolean;
  testResult?: DeviceTestEvent['test_result'];
  error?: string;
  children: React.ReactNode;
}

/**
 * 设备测试跟踪组件 - 自动跟踪设备测试的生命周期
 */
export const DeviceTestTracker: React.FC<DeviceTestTrackerProps> = ({
  deviceType,
  isTestActive,
  testResult,
  error,
  children
}) => {
  const { trackTestStart, trackTestComplete, trackTestError } = useDeviceTestTracking();
  
  // 跟踪测试状态变化
  useEffect(() => {
    if (isTestActive) {
      trackTestStart(deviceType);
    }
  }, [isTestActive, deviceType, trackTestStart]);
  
  // 跟踪测试完成或错误
  useEffect(() => {
    if (!isTestActive && testResult) {
      trackTestComplete(deviceType, testResult);
    }
  }, [isTestActive, testResult, deviceType, trackTestComplete]);
  
  // 跟踪错误
  useEffect(() => {
    if (error) {
      trackTestError(deviceType, error);
    }
  }, [error, deviceType, trackTestError]);
  
  return <>{children}</>;
};
