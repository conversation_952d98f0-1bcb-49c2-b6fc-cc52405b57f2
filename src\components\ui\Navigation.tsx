import React, { useState } from "react";
import { NavLink } from "react-router-dom";
import { Monitor, Keyboard, Mouse, Home, Mic, Headphones, Camera, Menu, X, Wifi } from "lucide-react";
import { useLanguage } from "../../hooks/useLanguage";
import { LanguageSwitcher } from "./LanguageSwitcher";

export const Navigation: React.FC = () => {
  const { t, language } = useLanguage();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const navItems = [
    { path: `/${language}`, label: t("home"), icon: Home },
    { path: `/${language}/tools/network`, label: t("networkQualityTest"), icon: Wifi },
    { path: `/${language}/tools/keyboard`, label: t("keyboardTest"), icon: Keyboard },
    { path: `/${language}/tools/mouse`, label: t("mouseTest"), icon: Mouse },
    { path: `/${language}/tools/microphone`, label: t("microphoneTest"), icon: Mic },
    { path: `/${language}/tools/camera`, label: t("cameraTest"), icon: Camera },
    { path: `/${language}/tools/headphones`, label: t("headphonesTest"), icon: Headphones },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-lg border-b border-white/20">
      <div className="w-full px-4 py-3">
        {/* Desktop Navigation */}
        <div className="hidden xl:flex items-center justify-between w-full">
          {/* Left: Logo and Site Name */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
              <Monitor className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white whitespace-nowrap">{t("siteName")}</h1>
            </div>
          </div>

          {/* Center: Navigation Links */}
          <div className="flex items-center justify-center flex-1 px-6">
            <div className="flex items-center space-x-1">
              {navItems.map((item, index) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  end={index === 0} // 只给 Home 添加 end 属性
                  className={({ isActive }) =>
                    `flex items-center space-x-1.5 px-3 py-2 rounded-lg transition-all duration-500 relative overflow-hidden group transform whitespace-nowrap ${
                      isActive
                        ? "bg-white/30 text-white border border-white/50 shadow-xl backdrop-blur-md scale-105 before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:via-white/10 before:to-transparent before:opacity-100 after:absolute after:inset-0 after:bg-gradient-to-b after:from-transparent after:via-white/5 after:to-white/10"
                        : "text-white/70 hover:bg-white/20 hover:text-white hover:border hover:border-white/30 hover:shadow-lg hover:backdrop-blur-md hover:scale-102 hover:transform before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/10 before:via-white/5 before:to-transparent before:opacity-0 hover:before:opacity-100 after:absolute after:inset-0 after:bg-gradient-to-b after:from-transparent after:via-white/3 after:to-white/5 after:opacity-0 hover:after:opacity-100"
                    } before:transition-all before:duration-500 after:transition-all after:duration-500 hover:animate-pulse`
                  }
                >
                  <item.icon className="h-4 w-4 relative z-10 flex-shrink-0" />
                  <span className="text-sm font-medium relative z-10">{item.label}</span>
                </NavLink>
              ))}
            </div>
          </div>

          {/* Right: Language Switcher */}
          <div className="flex-shrink-0">
            <LanguageSwitcher />
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="flex xl:hidden items-center justify-between w-full">
          {/* Left: Logo */}
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
              <Monitor className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">{t("siteName")}</h1>
            </div>
          </div>

          {/* Right: Menu Button and Language Switcher */}
          <div className="flex items-center space-x-2">
            <LanguageSwitcher />
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu Dropdown */}
        {isMobileMenuOpen && (
          <div className="xl:hidden absolute top-full left-0 right-0 bg-black/40 backdrop-blur-xl border-b border-white/20 shadow-2xl">
            <div className="container mx-auto px-4 py-4">
              <div className="bg-black/60 backdrop-blur-xl rounded-lg border border-white/20 shadow-xl">
                <div className="flex flex-col">
                  {navItems.map((item, index) => (
                    <NavLink
                      key={item.path}
                      to={item.path}
                      end={index === 0} // 只给 Home 添加 end 属性
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={({ isActive }) =>
                        `flex items-center space-x-3 px-4 py-3 transition-all duration-300 first:rounded-t-lg last:rounded-b-lg ${
                          isActive
                            ? "bg-white/30 text-white"
                            : "text-white/90 hover:bg-white/25 hover:text-white"
                        }`
                      }
                    >
                      <item.icon className="h-5 w-5" />
                      <span className="text-base font-medium">{item.label}</span>
                    </NavLink>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};