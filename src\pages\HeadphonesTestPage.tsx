import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { HeadphonesTestModule } from "@/components/tests/HeadphonesTestModule";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

export const HeadphonesTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('headphones', t, window.location.origin);

  return (
    <MainLayout seoConfig={seoConfig}>
      <HeadphonesTestModule />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="headphones" />
        <FAQ pageType="headphones" />
        <Glossary pageType="headphones" />
        <TroubleshootingGuide pageType="headphones" />
        <SEOFooter pageType="headphones" />
      </div>
    </MainLayout>
  );
};