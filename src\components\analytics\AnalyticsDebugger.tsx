import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, Play, BarChart3 } from 'lucide-react';
import { useAnalytics, useDeviceTestTracking, useInteractionTracking } from '@/hooks/useAnalytics';
import { useCookieConsent } from '@/hooks/useCookieConsent';
import { ga4Config } from '@/config/analytics';

/**
 * GA4 分析调试器组件 - 仅在开发环境下显示
 */
export const AnalyticsDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState<Array<{
    name: string;
    status: 'success' | 'error' | 'warning';
    message: string;
  }>>([]);

  const { isInitialized } = useAnalytics();
  const { trackTestStart, trackTestComplete } = useDeviceTestTracking();
  const { trackClick } = useInteractionTracking();
  const { consent, isAnalyticsEnabled } = useCookieConsent();

  // 只在开发环境或调试模式下显示
  useEffect(() => {
    const isDev = import.meta.env.DEV;
    const isDebug = ga4Config.debug;
    setIsVisible(isDev || isDebug);
  }, []);

  // 运行诊断测试
  const runDiagnostics = () => {
    const results = [];

    // 检查配置
    if (ga4Config.measurementId && ga4Config.measurementId !== 'GA_MEASUREMENT_ID') {
      results.push({
        name: '测量 ID 配置',
        status: 'success' as const,
        message: `测量 ID: ${ga4Config.measurementId}`
      });
    } else {
      results.push({
        name: '测量 ID 配置',
        status: 'error' as const,
        message: '测量 ID 未正确配置'
      });
    }

    // 检查 GA4 初始化状态
    if (isInitialized) {
      results.push({
        name: 'GA4 初始化',
        status: 'success' as const,
        message: 'GA4 已成功初始化'
      });
    } else {
      results.push({
        name: 'GA4 初始化',
        status: 'error' as const,
        message: 'GA4 未初始化'
      });
    }

    // 检查 Cookie 同意状态
    if (isAnalyticsEnabled) {
      results.push({
        name: 'Cookie 同意',
        status: 'success' as const,
        message: '用户已同意分析 Cookie'
      });
    } else {
      results.push({
        name: 'Cookie 同意',
        status: 'warning' as const,
        message: '用户未同意分析 Cookie'
      });
    }

    // 检查 gtag 函数
    if (typeof window.gtag === 'function') {
      results.push({
        name: 'gtag 函数',
        status: 'success' as const,
        message: 'gtag 函数可用'
      });
    } else {
      results.push({
        name: 'gtag 函数',
        status: 'error' as const,
        message: 'gtag 函数不可用'
      });
    }

    // 检查 dataLayer
    if (window.dataLayer && Array.isArray(window.dataLayer)) {
      results.push({
        name: 'dataLayer',
        status: 'success' as const,
        message: `dataLayer 包含 ${window.dataLayer.length} 个事件`
      });
    } else {
      results.push({
        name: 'dataLayer',
        status: 'error' as const,
        message: 'dataLayer 不可用'
      });
    }

    setTestResults(results);
  };

  // 测试事件发送
  const testEventSending = () => {
    if (!isInitialized) {
      alert('GA4 未初始化，无法测试事件发送');
      return;
    }

    // 发送测试事件
    trackClick('debug-test-button', 'analytics-debugger', 'Debug Test Click');
    trackTestStart('camera');
    
    setTimeout(() => {
      trackTestComplete('camera', 'success', 1000);
    }, 1000);

    alert('测试事件已发送！请检查浏览器开发者工具的网络标签页，查找发送到 google-analytics.com 的请求。');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-500/10 text-green-400 border-green-500/20';
      case 'error':
        return 'bg-red-500/10 text-red-400 border-red-500/20';
      case 'warning':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20';
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="w-96 bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-lg shadow-2xl">
        <div className="p-4 border-b border-white/10">
          <h3 className="flex items-center gap-2 text-white font-semibold">
            <BarChart3 className="h-5 w-5" />
            GA4 调试器
          </h3>
        </div>
        <div className="p-4 space-y-4">
          {/* 基本信息 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/70">环境:</span>
              <Badge variant="outline" className="text-white border-white/20">
                {import.meta.env.DEV ? '开发' : '生产'}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/70">调试模式:</span>
              <Badge variant="outline" className={ga4Config.debug ? 'text-green-400 border-green-500/20' : 'text-gray-400 border-gray-500/20'}>
                {ga4Config.debug ? '启用' : '禁用'}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/70">分析状态:</span>
              <Badge variant="outline" className={isInitialized ? 'text-green-400 border-green-500/20' : 'text-red-400 border-red-500/20'}>
                {isInitialized ? '已初始化' : '未初始化'}
              </Badge>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={runDiagnostics}
              className="flex-1 bg-transparent border-white/20 text-white hover:bg-white/10"
            >
              <Play className="h-4 w-4 mr-1" />
              运行诊断
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={testEventSending}
              disabled={!isInitialized}
              className="flex-1 bg-transparent border-white/20 text-white hover:bg-white/10 disabled:opacity-50"
            >
              测试事件
            </Button>
          </div>

          {/* 诊断结果 */}
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white">诊断结果:</h4>
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`flex items-center gap-2 p-2 rounded-lg border ${getStatusColor(result.status)}`}
                  >
                    {getStatusIcon(result.status)}
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium">{result.name}</div>
                      <div className="text-xs opacity-80 truncate">{result.message}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 帮助信息 */}
          <div className="text-xs text-white/50 space-y-1">
            <p>• 在浏览器开发者工具中查看网络请求</p>
            <p>• 检查控制台中的 GA4 调试信息</p>
            <p>• 使用 GA4 实时报告验证数据</p>
          </div>
        </div>
      </div>
    </div>
  );
};
