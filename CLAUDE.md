# GlassEcho Device Check

一个现代化的设备检测工具，支持多种测试场景和多语言界面。

## 新增功能 🆕

### 智能网络测试 (Smart Network Testing)

网络测试现在根据用户的地理位置智能选择最适合的测试服务器，提供更准确的网络质量评估。

#### 主要特性：

1. **自动地区检测**
   - 基于用户时区智能判断地理位置
   - 支持语言偏好设置辅助检测
   - 通过网络延迟测试确认最优地区

2. **地区化测试端点**
   - **中国大陆**: 使用国内CDN (bootcdn, staticfile, baomitu)
   - **亚太地区**: 优化的亚洲CDN节点
   - **欧洲地区**: 欧洲专用测试端点
   - **北美地区**: 美国和加拿大优化节点
   - **全球默认**: 全球CDN备选方案

3. **多层次检测方法**
   ```typescript
   // 方法1: 时区检测
   Asia/Shanghai, Asia/Beijing → 中国大陆
   Asia/* → 亚太地区
   Europe/* → 欧洲地区
   America/* → 北美地区

   // 方法2: 语言检测
   zh-cn → 中国大陆
   zh/ja/ko → 亚太地区
   en-us → 北美地区
   de/fr/it/es → 欧洲地区

   // 方法3: 网络延迟测试
   并行测试各地区端点，选择延迟最低的地区
   ```

4. **智能CDN选择**
   - 中国大陆用户使用国内可访问的CDN
   - 其他地区使用全球CDN网络
   - 自动降级到备用端点

#### 技术实现：

```typescript
interface RegionConfig {
  name: string;
  latencyEndpoints: string[];     // 延迟测试端点
  downloadEndpoints: string[];    // 下载速度测试
  uploadEndpoints: string[];      // 上传速度测试
}
```

#### 使用方式：

- **独立使用**: `/network-test` 页面，无导航按钮
- **场景化测试**: 在会议或游戏场景中，带有完整导航流程

#### 用户体验改进：

- 显示当前测试地区信息
- 基于地理位置的个性化建议
- 更准确的网络质量评估
- 减少跨地区网络延迟影响

## 原有功能

### 核心功能
- 🎤 **麦克风测试**: 音频输入检测和质量分析
- 🔊 **扬声器测试**: 音频输出测试和立体声检验
- 📷 **摄像头测试**: 视频输入检测和画质评估
- ⌨️ **键盘测试**: 按键响应和功能检测
- 🖱️ **鼠标测试**: 点击、滚动和移动检测
- 🎧 **耳机测试**: 专业音频体验测试
- 🌐 **网络测试**: 延迟、速度和稳定性检测

### 测试场景
- **在线会议检查**: 完整的视频会议设备验证流程
- **游戏设置检查**: 专为游戏优化的外设性能测试

### 多语言支持
- 🇨🇳 中文 (简体)
- 🇺🇸 English
- 🇩🇪 Deutsch  
- 🇪🇸 Español
- 🇯🇵 日本語
- 🇰🇷 한국어

### 技术栈
- **前端**: React 18 + TypeScript + Vite
- **UI 框架**: shadcn/ui + Tailwind CSS
- **路由**: React Router v6
- **状态管理**: React Hooks + Tanstack Query
- **表单处理**: React Hook Form + Zod
- **设计**: Liquid Glass 设计系统

### 部署和构建
```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 预览
npm run preview
```

### 项目结构
```
src/
├── components/           # 可复用组件
│   ├── tests/           # 测试相关组件
│   ├── ui/              # 基础UI组件
│   └── layouts/         # 布局组件
├── hooks/               # 自定义Hooks
├── locales/             # 国际化配置
├── pages/               # 页面组件
└── lib/                 # 工具函数
```

## 开发指南

### 添加新的测试地区

1. 在 `useNetworkTest.ts` 中的 `REGION_CONFIGS` 添加新地区配置
2. 更新 `detectUserRegion` 函数中的检测逻辑
3. 添加相应的翻译键到所有语言文件

### 自定义测试端点

可以通过修改 `REGION_CONFIGS` 来自定义各地区的测试端点：

```typescript
'NEW_REGION': {
  name: '新地区',
  latencyEndpoints: ['https://your-cdn.com/test'],
  downloadEndpoints: ['https://your-cdn.com/large-file'],
  uploadEndpoints: ['https://your-api.com/upload']
}
```