import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { NetworkTest } from "@/components/tests/NetworkTest";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

export const NetworkTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('network', t, window.location.origin);

  return (
    <MainLayout seoConfig={seoConfig}>
      <NetworkTest showNavigation={false} />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="network" />
        <FAQ pageType="network" />
        <Glossary pageType="network" />
        <TroubleshootingGuide pageType="network" />
        <SEOFooter pageType="network" />
      </div>
    </MainLayout>
  );
}; 