import React, { useState } from "react";
import { use<PERSON>arams, useNavigate } from "react-router-dom";
import { Co<PERSON>, CheckCircle, Home } from "lucide-react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { TestStepController } from "@/components/ui/TestStepController";
import { TestWrapper } from "@/components/ui/TestWrapper";
import { useWorkflowManager } from "@/hooks/useWorkflowManager";
import { getScenarioConfig } from "@/config/testScenarios";
import {
  EnhancedTestResult,
  NavigationAction,
  TestStatus,
  TestStep as WorkflowTestStep
} from "@/types/testWorkflow";
import { NetworkTest } from "@/components/tests/NetworkTest";
import { EnhancedMicrophoneTest } from "@/components/tests/EnhancedMicrophoneTest";

import { EnhancedCameraTest } from "@/components/tests/EnhancedCameraTest";
import { KeyboardTestModule } from "@/components/tests/KeyboardTestModule";
import { MouseTestModule } from "@/components/tests/MouseTestModule";
import { HeadphonesTestModule } from "@/components/tests/HeadphonesTestModule";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

type MeetingTestStep = "network" | "microphone" | "camera" | "headphones" | "summary";
type GamingTestStep = "network" | "microphone" | "keyboard" | "mouse" | "headphones" | "summary";
type StreamingTestStep = "network" | "microphone" | "camera" | "headphones" | "summary";
type DiagnosticTestStep = "network" | "microphone" | "camera" | "keyboard" | "mouse" | "headphones" | "summary";
type TestStep = MeetingTestStep | GamingTestStep | StreamingTestStep | DiagnosticTestStep;

interface TestResult {
  completed: boolean;
  passed: boolean;
  details?: any;
  timestamp?: Date;
  failureReason?: string; // 添加失败原因
}

interface MeetingTestResults {
  network: TestResult;
  microphone: TestResult;
  camera: TestResult;
  headphones: TestResult;
}

interface GamingTestResults {
  network: TestResult;
  microphone: TestResult;
  keyboard: TestResult;
  mouse: TestResult;
  headphones: TestResult;
}

interface StreamingTestResults {
  network: TestResult;
  microphone: TestResult;
  camera: TestResult;
  headphones: TestResult;
}

interface DiagnosticTestResults {
  network: TestResult;
  microphone: TestResult;
  camera: TestResult;
  keyboard: TestResult;
  mouse: TestResult;
  headphones: TestResult;
}

type TestResults = MeetingTestResults | GamingTestResults | StreamingTestResults | DiagnosticTestResults;

export const TestWorkflowPage: React.FC = () => {
  const { scenarioName } = useParams<{ scenarioName: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useLanguage();
  
  const isGamingScenario = scenarioName === "gaming";
  const isStreamingScenario = scenarioName === "streaming";
  const isDiagnosticScenario = scenarioName === "diagnostic";

  // 根据场景生成SEO配置
  const getSeoPageKey = () => {
    if (isGamingScenario) return 'gaming';
    if (isStreamingScenario) return 'meeting'; // 复用meeting的SEO配置
    if (isDiagnosticScenario) return 'meeting'; // 复用meeting的SEO配置
    return 'meeting';
  };
  const seoConfig = generatePageSEO(getSeoPageKey(), t, window.location.origin);
  
  // Initialize test results based on scenario
  const getInitialTestResults = (): TestResults => {
    if (isGamingScenario) {
      return {
        network: { completed: false, passed: false },
        microphone: { completed: false, passed: false },
        keyboard: { completed: false, passed: false },
        mouse: { completed: false, passed: false },
        headphones: { completed: false, passed: false }
      };
    } else if (isStreamingScenario) {
      return {
        network: { completed: false, passed: false },
        microphone: { completed: false, passed: false },
        camera: { completed: false, passed: false },
        headphones: { completed: false, passed: false }
      };
    } else if (isDiagnosticScenario) {
      return {
        network: { completed: false, passed: false },
        microphone: { completed: false, passed: false },
        camera: { completed: false, passed: false },
        keyboard: { completed: false, passed: false },
        mouse: { completed: false, passed: false },
        headphones: { completed: false, passed: false }
      };
    } else {
      return {
        network: { completed: false, passed: false },
        microphone: { completed: false, passed: false },
        camera: { completed: false, passed: false },
        headphones: { completed: false, passed: false }
      };
    }
  };

  // 获取场景配置
  const scenarioConfig = getScenarioConfig(scenarioName || 'meeting');

  // 使用新的工作流程管理器
  const workflowManager = useWorkflowManager({
    steps: scenarioConfig.steps
  });

  // 保持向后兼容的状态
  const [testResults, setTestResults] = useState<TestResults>(getInitialTestResults());

  // 从工作流程管理器获取当前状态
  const currentStep = workflowManager.getCurrentStep();
  const currentStepKey = currentStep?.config.key as TestStep || "network";
  const navigationControl = workflowManager.getNavigationControl();
  const currentStepResult = currentStep?.result;

  const getMeetingSteps = (): { key: MeetingTestStep; title: string; number: number }[] => [
    { key: "network", title: t("networkQualityTest"), number: 1 },
    { key: "camera", title: t("cameraTest"), number: 2 },
    { key: "microphone", title: t("microphoneTest"), number: 3 },
    { key: "headphones", title: t("headphonesTest"), number: 4 },
    { key: "summary", title: t("testResults"), number: 5 },
  ];

  const getGamingSteps = (): { key: GamingTestStep; title: string; number: number }[] => [
    { key: "network", title: t("networkQualityTest"), number: 1 },
    { key: "microphone", title: t("microphoneTest"), number: 2 },
    { key: "keyboard", title: t("keyboardTest"), number: 3 },
    { key: "mouse", title: t("mouseTest"), number: 4 },
    { key: "headphones", title: t("headphonesTest"), number: 5 },
    { key: "summary", title: t("testResults"), number: 6 },
  ];

  const getStreamingSteps = (): { key: StreamingTestStep; title: string; number: number }[] => [
    { key: "network", title: t("networkQualityTest"), number: 1 },
    { key: "camera", title: t("cameraTest"), number: 2 },
    { key: "microphone", title: t("microphoneTest"), number: 3 },
    { key: "headphones", title: t("headphonesTest"), number: 4 },
    { key: "summary", title: t("testResults"), number: 5 },
  ];

  const getDiagnosticSteps = (): { key: DiagnosticTestStep; title: string; number: number }[] => [
    { key: "network", title: t("networkQualityTest"), number: 1 },
    { key: "camera", title: t("cameraTest"), number: 2 },
    { key: "microphone", title: t("microphoneTest"), number: 3 },
    { key: "keyboard", title: t("keyboardTest"), number: 4 },
    { key: "mouse", title: t("mouseTest"), number: 5 },
    { key: "headphones", title: t("headphonesTest"), number: 6 },
    { key: "summary", title: t("testResults"), number: 7 },
  ];

  const getSteps = () => {
    if (isGamingScenario) return getGamingSteps();
    if (isStreamingScenario) return getStreamingSteps();
    if (isDiagnosticScenario) return getDiagnosticSteps();
    return getMeetingSteps();
  };

  const steps = getSteps();

  const getCurrentStepIndex = () => {
    return workflowManager.state.currentStepIndex;
  };

  // 更新测试结果（保持向后兼容）
  const updateTestResult = (testType: keyof TestResults, result: Partial<TestResult>) => {
    setTestResults(prev => ({
      ...prev,
      [testType]: {
        ...prev[testType],
        ...result,
        timestamp: new Date()
      }
    }));
  };

  // 处理导航操作
  const handleAction = (action: NavigationAction, result?: EnhancedTestResult) => {
    // 如果是下一步操作且提供了测试结果，先更新向后兼容的状态
    if (action === NavigationAction.NEXT && result && currentStepKey !== "summary") {
      updateTestResult(currentStepKey as keyof TestResults, {
        completed: true,
        passed: result.passed,
        details: result.details,
        failureReason: result.failureReason
      });
    }

    // 执行工作流程操作
    const success = workflowManager.executeAction(action, result);

    // 如果是返回到首页的操作
    if (action === NavigationAction.BACK && workflowManager.state.currentStepIndex === 0) {
      navigate("/");
    }

    return success;
  };

  // 处理测试完成（从测试组件接收结果）
  const handleTestComplete = (result: { passed: boolean; details?: any; failureReason?: string }) => {
    // 转换为增强的测试结果格式
    const enhancedResult: EnhancedTestResult = {
      status: result.passed ? TestStatus.COMPLETED : TestStatus.FAILED,
      passed: result.passed,
      details: result.details,
      failureReason: result.failureReason,
      timestamp: new Date()
    };

    // 更新工作流程管理器中的步骤结果
    workflowManager.updateStepResult(workflowManager.state.currentStepIndex, enhancedResult);
  };

  const generateReport = () => {
    const userAgent = navigator.userAgent;
    const timestamp = new Date().toLocaleString();

    const completedTests = Object.values(testResults).filter(r => r.completed).length;
    const passedTests = Object.values(testResults).filter(r => r.completed && r.passed).length;

    // 通用的辅助函数
    const getTestStatus = (result: TestResult) => {
      if (!result.completed) return t("notTested");
      return result.passed ? t("testPassed") : t("failed");
    };

    const getTestIcon = (result: TestResult) => {
      if (!result.completed) return "⏸️";
      return result.passed ? "✅" : "❌";
    };

    const getTestDetails = (result: TestResult) => {
      if (!result.completed) return "";
      if (result.passed) return "";
      return result.failureReason ? `\n  ${t("failureReason")}: ${result.failureReason}` : "";
    };

    if (isGamingScenario) {
      const gamingResults = testResults as GamingTestResults;
      
      return `${t("gamingDeviceTestReport")}
${t("generatedTime")}: ${timestamp}
${t("testScenario")}: ${t("gamingScenario")}

${t("browserInfo")}:
${userAgent}

${t("testCompletionStatus")}: ${completedTests}/5 ${t("testCompletionCount")}

${getTestIcon(gamingResults.network)} ${t("networkQualityTest")} - ${getTestStatus(gamingResults.network)}${getTestDetails(gamingResults.network)}
${getTestIcon(gamingResults.microphone)} ${t("microphoneTest")} - ${getTestStatus(gamingResults.microphone)}${getTestDetails(gamingResults.microphone)}
${getTestIcon(gamingResults.keyboard)} ${t("keyboardTest")} - ${getTestStatus(gamingResults.keyboard)}${getTestDetails(gamingResults.keyboard)}
${getTestIcon(gamingResults.mouse)} ${t("mouseTest")} - ${getTestStatus(gamingResults.mouse)}${getTestDetails(gamingResults.mouse)}
${getTestIcon(gamingResults.headphones)} ${t("headphonesTest")} - ${getTestStatus(gamingResults.headphones)}${getTestDetails(gamingResults.headphones)}

${t("testResultsTitle")}:
${passedTests === 5 ? 
  t("allGamingTestsPassed") : 
  `${passedTests}/${completedTests} ${t("testsPassed")}. ${completedTests < 5 ? t("partialTestsCompleted") : t("checkFailedTests")}`
}

${t("optimizeGamingSetup")}:
${t("gamingTip1")}
${t("gamingTip2")}
${t("gamingTip3")}
${t("gamingTip4")}
${t("gamingTip5")}${passedTests < completedTests ? `\n${t("retestFailedPeripherals")}` : ""}`;
    } else if (isStreamingScenario) {
      const streamingResults = testResults as StreamingTestResults;

      return `Live Streaming & Content Creation Device Report
Generated Time: ${timestamp}
Test Scenario: Live Streaming & Content Creation

Browser Info:
${userAgent}

Test Completion Status: ${completedTests}/4 tests completed

${getTestIcon(streamingResults.network)} ${t("networkQualityTest")} - ${getTestStatus(streamingResults.network)}${getTestDetails(streamingResults.network)}
${getTestIcon(streamingResults.camera)} ${t("cameraTest")} - ${getTestStatus(streamingResults.camera)}${getTestDetails(streamingResults.camera)}
${getTestIcon(streamingResults.microphone)} ${t("microphoneTest")} - ${getTestStatus(streamingResults.microphone)}${getTestDetails(streamingResults.microphone)}
${getTestIcon(streamingResults.headphones)} ${t("headphonesTest")} - ${getTestStatus(streamingResults.headphones)}${getTestDetails(streamingResults.headphones)}

Test Results:
${passedTests === 4 ?
  "All streaming equipment tests passed! Your setup is ready for professional content creation." :
  `${passedTests}/${completedTests} tests passed. ${completedTests < 4 ? "Please complete all tests for full evaluation." : "Please check failed tests and optimize your setup."}`
}

Streaming Optimization Tips:
• Ensure stable high-speed internet (minimum 5 Mbps upload for 1080p streaming)
• Use a dedicated microphone for better audio quality
• Optimize lighting and camera positioning for professional appearance
• Test audio levels and eliminate background noise
• Consider using headphones to monitor audio quality${passedTests < completedTests ? `\nRetest failed equipment and optimize settings for better streaming quality.` : ""}`;
    } else if (isDiagnosticScenario) {
      const diagnosticResults = testResults as DiagnosticTestResults;

      return `Complete Device Diagnostic Report
Generated Time: ${timestamp}
Test Scenario: Comprehensive Hardware Diagnostic

Browser Info:
${userAgent}

Test Completion Status: ${completedTests}/6 tests completed

${getTestIcon(diagnosticResults.network)} ${t("networkQualityTest")} - ${getTestStatus(diagnosticResults.network)}${getTestDetails(diagnosticResults.network)}
${getTestIcon(diagnosticResults.camera)} ${t("cameraTest")} - ${getTestStatus(diagnosticResults.camera)}${getTestDetails(diagnosticResults.camera)}
${getTestIcon(diagnosticResults.microphone)} ${t("microphoneTest")} - ${getTestStatus(diagnosticResults.microphone)}${getTestDetails(diagnosticResults.microphone)}
${getTestIcon(diagnosticResults.keyboard)} ${t("keyboardTest")} - ${getTestStatus(diagnosticResults.keyboard)}${getTestDetails(diagnosticResults.keyboard)}
${getTestIcon(diagnosticResults.mouse)} ${t("mouseTest")} - ${getTestStatus(diagnosticResults.mouse)}${getTestDetails(diagnosticResults.mouse)}
${getTestIcon(diagnosticResults.headphones)} ${t("headphonesTest")} - ${getTestStatus(diagnosticResults.headphones)}${getTestDetails(diagnosticResults.headphones)}

Diagnostic Results:
${passedTests === 6 ?
  "All hardware components are functioning properly. No issues detected." :
  `${passedTests}/${completedTests} components passed. ${completedTests < 6 ? "Please complete all tests for full system evaluation." : "Issues detected - see failed tests above."}`
}

Troubleshooting Recommendations:
• Check device drivers and update if necessary
• Verify all hardware connections are secure
• Test devices with other applications to isolate issues
• Restart devices and browser if problems persist
• Contact technical support for persistent hardware failures${passedTests < completedTests ? `\nFocus on failed components and follow specific troubleshooting steps for each device type.` : ""}`;
    } else {
      const meetingResults = testResults as MeetingTestResults;
      
      return `${t("onlineMeetingDeviceReport")}
${t("generatedTime")}: ${timestamp}
${t("testScenario")}: ${scenarioName === "meeting" ? t("onlineMeetingScenario") : t("unknownScenario")}

${t("browserInfo")}:
${userAgent}

${t("testCompletionStatus")}: ${completedTests}/4 ${t("testCompletionCount")}

${getTestIcon(meetingResults.network)} ${t("networkQualityTest")} - ${getTestStatus(meetingResults.network)}${getTestDetails(meetingResults.network)}
${getTestIcon(meetingResults.camera)} ${t("cameraTest")} - ${getTestStatus(meetingResults.camera)}${getTestDetails(meetingResults.camera)}
${getTestIcon(meetingResults.microphone)} ${t("microphoneTest")} - ${getTestStatus(meetingResults.microphone)}${getTestDetails(meetingResults.microphone)}
${getTestIcon(meetingResults.headphones)} ${t("headphonesTest")} - ${getTestStatus(meetingResults.headphones)}${getTestDetails(meetingResults.headphones)}

${t("testResultsTitle")}:
${passedTests === 4 ? 
  t("allHardwareTestsPassed") : 
  `${passedTests}/${completedTests} ${t("testsPassed")}. ${completedTests < 4 ? t("partialTestsCompleted") : t("checkFailedTests")}`
}

${t("suggestions")}:
${t("suggestion1")}
${t("suggestion2")}
${t("suggestion3")}
${t("suggestion4")}${passedTests < completedTests ? `\n${t("retestFailedDevices")}` : ""}`;
    }
  };

  const copyReport = async () => {
    try {
      const report = generateReport();
      await navigator.clipboard.writeText(report);
      const getDescription = () => {
        if (isGamingScenario) return t("gamingDeviceTestComplete");
        if (isStreamingScenario) return t("streamingDeviceTestComplete");
        if (isDiagnosticScenario) return t("diagnosticDeviceTestComplete");
        return t("allHardwareTestsComplete");
      };

      toast({
        title: t("reportCopied"),
        description: getDescription(),
      });
    } catch (error) {
      toast({
        title: t("copyFailed"),
        description: t("copyFailedDesc"),
        variant: "destructive",
      });
    }
  };

  const renderCurrentTest = () => {
    const currentIndex = getCurrentStepIndex();
    const isLastStep = currentStepKey === "summary";

    switch (currentStepKey) {
      case "network":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <NetworkTest />
            </TestWrapper>
          </TestStepController>
        );

      case "microphone":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <EnhancedMicrophoneTest />
            </TestWrapper>
          </TestStepController>
        );

      case "camera":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <EnhancedCameraTest />
            </TestWrapper>
          </TestStepController>
        );

      case "headphones":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <HeadphonesTestModule onTestResult={handleTestComplete} />
            </TestWrapper>
          </TestStepController>
        );

      case "keyboard":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <KeyboardTestModule />
            </TestWrapper>
          </TestStepController>
        );

      case "mouse":
        return (
          <TestStepController
            onAction={handleAction}
            navigationControl={navigationControl}
            testResult={currentStepResult}
            stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
            stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
            showProgress={true}
            currentStep={currentIndex + 1}
            totalSteps={workflowManager.state.steps.length}
          >
            <TestWrapper onTestComplete={handleTestComplete}>
              <MouseTestModule />
            </TestWrapper>
          </TestStepController>
        );
        
      case "summary":
        const completedTests = Object.values(testResults).filter(r => r.completed).length;
        const passedTests = Object.values(testResults).filter(r => r.completed && r.passed).length;
        const getTotalTests = () => {
          if (isGamingScenario) return 5;
          if (isDiagnosticScenario) return 6;
          return 4; // meeting and streaming both have 4 tests
        };
        const totalTests = getTotalTests();
        const allTestsPassed = passedTests === totalTests;
        const hasFailedTests = Object.values(testResults).some(r => r.completed && !r.passed);

        const getTestStatusDisplay = (result: TestResult) => {
          if (!result.completed) {
            return { icon: "⏸️", text: t("notTested"), color: "text-yellow-300" };
          }
          return result.passed 
            ? { icon: "✅", text: t("testPassed"), color: "text-green-300" }
            : { icon: "❌", text: t("failed"), color: "text-red-300" };
        };

        return (
          <GlassCard className="max-w-4xl mx-auto text-center">
            <div className="mb-6">
              <CheckCircle className={`h-16 w-16 mx-auto mb-4 ${
                allTestsPassed ? "text-green-400" : hasFailedTests ? "text-red-400" : "text-yellow-400"
              }`} />
              <h2 className="text-3xl font-bold text-white mb-2">
                {allTestsPassed ? t("testComplete") : t("testResults")}
              </h2>
              <p className="text-white/70 text-lg">
                {allTestsPassed
                  ? (isGamingScenario ? t("allGamingTestsPassed") :
                     isStreamingScenario ? t("allStreamingTestsPassed") :
                     isDiagnosticScenario ? t("allDiagnosticTestsPassed") :
                     t("allTestsPassed"))
                  : `${t("testsCompletedCount").replace("{completed}", String(completedTests)).replace("{total}", String(totalTests))}, ${passedTests} ${t("testsPassed")}`
                }
              </p>
            </div>

            <div className={`${
              allTestsPassed 
                ? "bg-green-500/10 border-green-400/30" 
                : hasFailedTests 
                  ? "bg-red-500/10 border-red-400/30"
                  : "bg-yellow-500/10 border-yellow-400/30"
            } border rounded-xl p-6 mb-8`}>
              <h3 className={`font-semibold mb-3 ${
                allTestsPassed 
                  ? "text-green-200" 
                  : hasFailedTests 
                    ? "text-red-200"
                    : "text-yellow-200"
              }`}>
                {isGamingScenario ? t("gamingDeviceTestReport") :
                 isStreamingScenario ? t("streamingDeviceTestReport") :
                 isDiagnosticScenario ? t("diagnosticDeviceTestReport") :
                 t("meetingDeviceTestReport")}
              </h3>
              <div className="space-y-3 text-left">
                {/* 网络测试结果 */}
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/80">{t("networkQualityTest")}</span>
                    <span className={`font-medium ${getTestStatusDisplay(testResults.network).color}`}>
                      {getTestStatusDisplay(testResults.network).icon} {getTestStatusDisplay(testResults.network).text}
                    </span>
                  </div>
                  {testResults.network.completed && !testResults.network.passed && testResults.network.failureReason && (
                    <p className="text-red-300/80 text-xs mt-1 ml-2">
                      {t("reason")}: {testResults.network.failureReason}
                    </p>
                  )}
                </div>

                {/* 摄像头测试结果 - 会议、直播、诊断场景有 */}
                {("camera" in testResults) && (
                  <div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">{t("cameraTest")}</span>
                      <span className={`font-medium ${getTestStatusDisplay((testResults as any).camera).color}`}>
                        {getTestStatusDisplay((testResults as any).camera).icon} {getTestStatusDisplay((testResults as any).camera).text}
                      </span>
                    </div>
                    {testResults.camera.completed && !testResults.camera.passed && testResults.camera.failureReason && (
                      <p className="text-red-300/80 text-xs mt-1 ml-2">
                        {t("reason")}: {testResults.camera.failureReason}
                      </p>
                    )}
                  </div>
                )}

                {/* 麦克风测试结果 - 所有场景都有 */}
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/80">{t("microphoneTest")}</span>
                    <span className={`font-medium ${getTestStatusDisplay(testResults.microphone).color}`}>
                      {getTestStatusDisplay(testResults.microphone).icon} {getTestStatusDisplay(testResults.microphone).text}
                    </span>
                  </div>
                  {testResults.microphone.completed && !testResults.microphone.passed && testResults.microphone.failureReason && (
                    <p className="text-red-300/80 text-xs mt-1 ml-2">
                      {t("reason")}: {testResults.microphone.failureReason}
                    </p>
                  )}
                </div>

                {/* 键盘测试结果 - 游戏、诊断场景有 */}
                {("keyboard" in testResults) && (
                  <div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">{t("keyboardTest")}</span>
                      <span className={`font-medium ${getTestStatusDisplay((testResults as any).keyboard).color}`}>
                        {getTestStatusDisplay((testResults as any).keyboard).icon} {getTestStatusDisplay((testResults as any).keyboard).text}
                      </span>
                    </div>
                    {testResults.keyboard.completed && !testResults.keyboard.passed && testResults.keyboard.failureReason && (
                      <p className="text-red-300/80 text-xs mt-1 ml-2">
                        {t("reason")}: {testResults.keyboard.failureReason}
                      </p>
                    )}
                  </div>
                )}

                {/* 鼠标测试结果 - 游戏、诊断场景有 */}
                {("mouse" in testResults) && (
                  <div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">{t("mouseTest")}</span>
                      <span className={`font-medium ${getTestStatusDisplay((testResults as any).mouse).color}`}>
                        {getTestStatusDisplay((testResults as any).mouse).icon} {getTestStatusDisplay((testResults as any).mouse).text}
                      </span>
                    </div>
                    {testResults.mouse.completed && !testResults.mouse.passed && testResults.mouse.failureReason && (
                      <p className="text-red-300/80 text-xs mt-1 ml-2">
                        {t("reason")}: {testResults.mouse.failureReason}
                      </p>
                    )}
                  </div>
                )}

                {/* 耳机测试结果 - 所有场景都有 */}
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/80">{t("headphonesTest")}</span>
                    <span className={`font-medium ${getTestStatusDisplay(testResults.headphones).color}`}>
                      {getTestStatusDisplay(testResults.headphones).icon} {getTestStatusDisplay(testResults.headphones).text}
                    </span>
                  </div>
                  {testResults.headphones.completed && !testResults.headphones.passed && testResults.headphones.failureReason && (
                    <p className="text-red-300/80 text-xs mt-1 ml-2">
                      {t("reason")}: {testResults.headphones.failureReason}
                    </p>
                  )}
                </div>


              </div>
              <div className={`mt-4 pt-4 border-t ${
                allTestsPassed 
                  ? "border-green-400/20" 
                  : hasFailedTests 
                    ? "border-red-400/20"
                    : "border-yellow-400/20"
              }`}>
                <p className={`text-sm ${
                  allTestsPassed 
                    ? "text-green-200/90" 
                    : hasFailedTests 
                      ? "text-red-200/90"
                      : "text-yellow-200/90"
                }`}>
                  {allTestsPassed 
                    ? (isGamingScenario ? t("allPeripheralsReady") : t("allDevicesReady"))
                    : hasFailedTests
                      ? (isGamingScenario ? t("somePeripheralsFailed") : t("someTestsFailed"))
                      : (isGamingScenario ? t("completeAllGamingTests") : t("completeAllTests"))
                  }
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <PrimaryButton onClick={copyReport} size="lg" className="w-full">
                <Copy className="h-5 w-5" />
                {t("copyReport")}
              </PrimaryButton>

              <div className="flex space-x-4">
                <PrimaryButton
                  onClick={() => navigate("/")}
                  variant="outline"
                  className="flex-1"
                >
                  <Home className="h-5 w-5" />
                  {t("home")}
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => {
                    // 重置所有测试结果
                    setTestResults(getInitialTestResults());
                    // 重置工作流程到第一步
                    workflowManager.executeAction(NavigationAction.BACK);
                    while (workflowManager.state.currentStepIndex > 0) {
                      workflowManager.executeAction(NavigationAction.BACK);
                    }
                  }}
                  variant="secondary"
                  className="flex-1"
                >
                  {t("runTestsAgain")}
                </PrimaryButton>
              </div>
            </div>
          </GlassCard>
        );
      default:
        return null;
    }
  };

  // 验证场景名称
  const validScenarios = ["meeting", "gaming", "streaming", "diagnostic"];
  if (!scenarioName || !validScenarios.includes(scenarioName)) {
    return (
      <MainLayout>
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">{t("invalidScenario")}</h1>
          <PrimaryButton onClick={() => navigate("/")}>
            {t("home")}
          </PrimaryButton>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout seoConfig={seoConfig}>
      {/* Progress Header */}
      <div className="mb-8">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            {isGamingScenario ? t("gamingSetupCheckTitle") :
             isStreamingScenario ? t("streamingSetupCheckTitle") :
             isDiagnosticScenario ? t("diagnosticSetupCheckTitle") :
             t("onlineMeeting")}
          </h1>
          <p className="text-white/70">
            {t("stepOf").replace("{current}", String(getCurrentStepIndex() + 1)).replace("{total}", String(steps.length))}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="max-w-4xl mx-auto px-4">
          <div className="flex items-center justify-center flex-wrap gap-x-3 gap-y-2 mb-2">
            {steps.map((step, index) => (
              <React.Fragment key={step.key}>
                <div className="flex items-center flex-shrink-0 min-w-0">
                  <div
                    className={`w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center text-sm font-medium ${
                      getCurrentStepIndex() >= index
                        ? "bg-blue-500 text-white"
                        : "bg-white/20 text-white/60"
                    }`}
                  >
                    {step.number}
                  </div>
                  <span
                    className={`ml-2 text-sm font-medium whitespace-nowrap ${
                      getCurrentStepIndex() >= index
                        ? "text-white"
                        : "text-white/60"
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`h-0.5 w-6 md:w-8 flex-shrink-0 ${
                      getCurrentStepIndex() > index
                        ? "bg-blue-500"
                        : "bg-white/20"
                    }`}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {/* Current Test Component */}
      {renderCurrentTest()}
      
      {/* SEO组件 - 仅在摘要步骤显示 */}
      {currentStepKey === "summary" && (
        <div className="container mx-auto max-w-6xl px-4 mt-12">
          <EnhancedSEO pageType={
            isGamingScenario ? "gaming" :
            isStreamingScenario ? "streaming" :
            isDiagnosticScenario ? "diagnostic" :
            "meeting"
          } />
          <FAQ pageType={
            isGamingScenario ? "gaming" :
            isStreamingScenario ? "streaming" :
            isDiagnosticScenario ? "diagnostic" :
            "meeting"
          } />
          <Glossary pageType={
            isGamingScenario ? "gaming" :
            isStreamingScenario ? "streaming" :
            isDiagnosticScenario ? "diagnostic" :
            "meeting"
          } />
          <TroubleshootingGuide pageType={
            isGamingScenario ? "gaming" :
            isStreamingScenario ? "streaming" :
            isDiagnosticScenario ? "diagnostic" :
            "meeting"
          } />
          <SEOFooter pageType={
            isGamingScenario ? "gaming" :
            isStreamingScenario ? "streaming" :
            isDiagnosticScenario ? "diagnostic" :
            "meeting"
          } />
        </div>
      )}
    </MainLayout>
  );
};