---
globs:
  - "**/*.mdc"
description: 路由规则
---

# 路由

本项目使用 `react-router-dom` v6 进行客户端路由。

- **路由配置**: 路由的主要配置在 [`src/App.tsx`](mdc:src/App.tsx) 文件中。这里定义了应用的主要路由结构，包括对 `404 Not Found` 页面的处理。

- **页面组件**: 每个路由都对应一个页面组件，这些组件存放在 [`src/pages/`](mdc:src/pages/) 目录下。

- **导航**:
  - 程序化的导航可以通过 `useNavigate` hook 来实现。
  - UI 导航组件，如 [`Navigation.tsx`](mdc:src/components/ui/Navigation.tsx) 使用 `Link` 或 `NavLink` 组件来创建导航链接。

  - 程序化的导航可以通过 `useNavigate` hook 来实现。
  - UI 导航组件，如 [`Navigation.tsx`](mdc:src/components/ui/Navigation.tsx) 使用 `Link` 或 `NavLink` 组件来创建导航链接。
