import { TranslationKeys } from './types';

export const ko: TranslationKeys = {
  // 기본 내비게이션
  home: "홈",
  tools: "도구",
  meetingTest: "미팅 체크",
  keyboardTest: "키보드 테스트",
  mouseTest: "마우스 테스트",
  headphonesTest: "헤드폰 테스트",
  siteName: "Setup Check",
  siteSubtitle: "하드웨어 체크",
  selectScenario: "테스트 시나리오 선택",
  onlineMeeting: "온라인 미팅 체크",
  onlineMeetingDesc: "화상통화를 위한 마이크, 스피커, 카메라를 테스트하세요",
  startTest: "테스트 시작",
  microphoneTest: "마이크 테스트",
  speakerTest: "스피커 테스트",
  cameraTest: "카메라 테스트",
  next: "다음",
  back: "뒤로",
  finish: "완료",
  
  // ToolsPage 관련
  deviceTestingTools: "장치 테스트 도구",
  deviceTestingToolsDescription: "모든 시나리오에서 장치가 완벽하게 작동하는지 확인하기 위한 종합적인 하드웨어 테스트 도구 모음입니다.",
  testingTools: "테스트 도구",
  hardwareTesting: "하드웨어 테스트",
  deviceTools: "장치 도구",
  audioQuality: "오디오 품질",
  noiseLevel: "소음 수준",
  sensitivity: "감도",
  stereoBalance: "스테레오 밸런스",
  volumeLevel: "볼륨 수준",
  audioOutput: "오디오 출력",
  keyResponse: "키 응답",
  keyMapping: "키 매핑",
  typingSpeed: "타이핑 속도",
  clickAccuracy: "클릭 정확도",
  scrollFunction: "스크롤 기능",
  internetSpeed: "인터넷 속도",
  connectionStability: "연결 안정성",
  quickTest: "빠른 테스트",
  needHelp: "도움이 필요하신가요?",
  testingToolsDescription: "테스트 도구는 간단하고 정확하게 설계되었습니다. 각 도구는 하드웨어 설정을 최적화하는 데 도움이 되는 자세한 피드백을 제공합니다.",
  noRegistration: "등록 필요 없음",
  accuracy: "정확도",
  free: "무료",
  
  // 마이크 테스트
  micTestTitle: "마이크를 테스트하세요",
  micTestDesc: "마이크에 대고 말해보세요. 오디오 레벨이 반응하는 것을 볼 수 있을 것입니다.",
  selectMicrophone: "마이크 선택",
  microphoneWorking: "마이크가 작동합니다!",
  microphoneNotDetected: "마이크가 감지되지 않음",
  
  // 스피커 테스트
  speakerTestTitle: "스피커를 테스트하세요",
  speakerTestDesc: "아래 버튼을 클릭하여 테스트 소리를 재생하세요.",
  playTestSound: "테스트 소리 재생",
  canYouHear: "테스트 소리가 들리나요?",
  
  // 카메라 테스트
  cameraTestTitle: "카메라를 테스트하세요",
  cameraTestDesc: "카메라 영상이 아래에 나타날 것입니다.",
  selectCamera: "카메라 선택",
  cameraWorking: "카메라가 작동합니다!",
  cameraNotDetected: "카메라가 감지되지 않음",
  cameraTestModule: "카메라 테스트 모듈",
  smartScorecard: "스마트 스코어카드",
  startTestToSeeScore: "점수를 보려면 테스트를 시작하세요",
  resolution: "해상도",
  frameRate: "프레임 레이트",
  colorBrightness: "색상 및 밝기",
  overallRating: "전체 평가",
  takePhoto: "사진 촬영",
  mirror: "미러",
  fullscreen: "전체화면",
  downloadPhoto: "사진 다운로드",
  deletePhoto: "사진 삭제",
  photoPreview: "사진 미리보기",
  cameraCapture: "카메라 캡처",
  deviceName: "장치",
  realTimeInfo: "실시간 정보",
  
  // 키보드 테스트
  keyboardTestTitle: "키보드 테스트",
  keyboardTestDesc: "키보드의 아무 키나 눌러서 테스트하세요",
  pressAnyKey: "아무 키나 눌러서 테스트를 시작하세요...",
  keyPressed: "눌린 키:",
  
  // 마우스 테스트
  mouseTestTitle: "마우스 테스트",
  mouseTestDesc: "마우스 버튼과 스크롤 휠을 테스트하세요",
  leftClick: "왼쪽 클릭",
  rightClick: "오른쪽 클릭",
  middleClick: "가운데 클릭/스크롤",
  scrollUp: "위로 스크롤",
  scrollDown: "아래로 스크롤",
  clickButtons: "위의 버튼을 클릭하거나 마우스를 사용하세요",
  
  // 테스트 결과
  testComplete: "테스트 완료!",
  allTestsPassed: "모든 테스트가 성공적으로 완료되었습니다",
  copyReport: "클립보드에 보고서 복사",
  reportCopied: "보고서가 클립보드에 복사되었습니다!",
  
  // 페이지 컨텐츠
  gamingSetup: "게이밍 설정 체크",
  gamingSetupDesc: "최적의 게임 경험을 위해 주변기기를 테스트하세요",
  audioTest: "오디오 테스트",
  recommended: "추천",
  comingSoon: "출시 예정",
  individualDeviceTests: "개별 장치 테스트",
  stepOf: "단계 {current} / {total}",
  invalidScenario: "유효하지 않은 시나리오",
  runTestsAgain: "테스트 다시 실행",
  
  // 테스트 컴포넌트
  cameraTestTitle2: "카메라 테스트",
  cameraTestDesc2: "미팅 중에 다른 사람들이 당신을 명확하게 볼 수 있도록 카메라를 테스트하세요.",
  selectCamera2: "카메라 선택",
  cameraPreview: "카메라 미리보기",
  cameraPlaceholder: "카메라 미리보기가 여기에 표시됩니다",
  startCameraTest: "카메라 테스트 시작",
  stopCamera: "카메라 중지",
  cameraWorking2: "✅ 카메라가 작동합니다!",
  cameraWorkingNormally: "카메라가 정상적으로 작동합니다",
  cameraStartedSuccessfully: "카메라가 성공적으로 시작되었습니다",
  analyzingVideoQuality: "비디오 품질 분석 중",
  cameraWorkingDesc: "카메라가 제대로 작동하고 있습니다. 조명이 충분하고 프레임 중앙에 위치하는지 확인하세요.",
  cameraTips: "💡 카메라 팁:",
  cameraTip1: "• 앞쪽에 충분한 조명이 있는지 확인하세요",
  cameraTip2: "• 카메라를 눈높이에 위치시키세요",
  cameraTip3: "• 더 선명한 이미지를 위해 카메라 렌즈를 청소하세요",
  cameraTip4: "• 안정적인 인터넷 연결을 확인하세요",
  backSpeakerTest: "뒤로: 스피커 테스트",
  finishTesting: "테스트 완료",
  
  keyboardTestTitle2: "키보드 테스트",
  keyboardTestDesc2: "아무 키나 눌러서 테스트하세요. 키가 눌릴 때 불이 켜집니다.",
  resetTest: "테스트 리셋",
  backToHome: "홈으로 돌아가기",
  recentKeyPresses: "최근 키 입력",
  space: "스페이스",
  testingTips: "💡 테스트 팁:",
  keyboardTip1: "• 다양한 종류의 키를 눌러보세요",
  keyboardTip2: "• Shift, Ctrl, Alt 같은 특수키를 테스트하세요",
  keyboardTip3: "• 모든 키가 제대로 반응하는지 확인하세요",
  keyboardTip4: "• 부드러운 작동을 위해 텍스트를 입력해보세요",
  
  micTestTitle2: "마이크 테스트",
  micTestDesc2: "미팅 중에 다른 사람들이 당신의 목소리를 명확하게 들을 수 있도록 마이크를 테스트하세요.",
  audioLevel: "오디오 레벨",
  micInstructions: "마이크에 대고 말해서 오디오 레벨을 확인하세요",
  micInstructionsStart: "'테스트 시작'을 클릭하여 시작하세요",
  startMicTest: "마이크 테스트 시작",
  stopTest: "테스트 중지",
  nextSpeakerTest: "다음: 스피커 테스트",
  
  // 마우스 테스트
  mouseTestTitle2: "마우스 테스트",
  mouseTestDesc2: "아래 테스트 영역에서 마우스를 움직이고 버튼을 클릭하세요.",
  testArea: "테스트 영역",
  moveClickScroll: "움직이기, 클릭, 스크롤",
  tryMouseButtons: "왼쪽, 오른쪽, 가운데 마우스 버튼을 시도해보세요",
  position: "위치:",
  buttonStatus: "버튼 상태",
  leftButton: "왼쪽",
  wheelButton: "휠",
  middleButton: "가운데",
  rightButton: "오른쪽",
  sideButton1: "사이드 1",
  sideButton2: "사이드 2",
  pressed: "눌림",
  released: "해제됨",
  eventHistory: "이벤트 기록",
  noMouseEvents: "아직 마우스 이벤트가 없습니다",
  startMovingClicking: "움직이고 클릭을 시작하여 이벤트를 확인하세요",
  scrollDown2: "아래로 스크롤",
  scrollUp2: "위로 스크롤",
  mouseTip1: "• 모든 마우스 버튼(왼쪽, 오른쪽, 가운데)을 테스트하세요",
  mouseTip2: "• 양방향 스크롤 휠을 확인하세요",
  mouseTip3: "• 마우스 움직임 추적을 확인하세요",
  mouseTip4: "• 클릭 앤 드래그 기능을 테스트하세요",
  mouseTip5: "• 커서가 부드럽게 움직이는지 확인하세요",
  
  // 스피커 테스트
  speakerTestTitle2: "스피커 테스트",
  speakerTestDesc2: "미팅 중에 오디오를 들을 수 있도록 스피커나 헤드폰을 테스트하세요.",
  volume: "볼륨:",
  testAudioPlayback: "테스트 오디오 재생",
  testAudioDesc: "아래 버튼을 클릭하여 테스트 톤을 재생하세요. 스피커나 헤드폰에서 명확한 소리가 들려야 합니다.",
  playingTestSound: "테스트 사운드 재생 중...",
  playTestSound2: "테스트 사운드 재생",
  playingTone: "🔊 테스트 톤 재생 중 (440 Hz)",
  troubleshootingTips: "💡 문제 해결 팁:",
  speakerTip1: "• 스피커/헤드폰이 연결되어 있는지 확인하세요",
  speakerTip2: "• 필요시 시스템 볼륨을 조정하세요",
  speakerTip3: "• 다른 오디오 출력 장치를 시도해보세요",
  speakerTip4: "• 오디오 드라이버가 최신인지 확인하세요",
  backMicrophone: "뒤로: 마이크",
  nextCameraTest: "다음: 카메라 테스트",
  
  // 헤드폰 & 스피커 테스트
  headphonesTestTitle: "온라인 헤드폰 및 스피커 테스트",
  headphonesTestDesc: "최적의 청취 경험을 위한 포괄적인 오디오 테스트",
  outputDeviceSelector: "출력 장치 선택",
  noOutputDevices: "오디오 출력 장치를 찾을 수 없음",
  leftRightChannelTest: "좌우 채널 테스트",
  leftRightChannelDesc: "채널 역전 또는 한쪽 오디오 문제를 빠르게 진단",
  playLeft: "▶ 왼쪽 채널 재생",
  playRight: "▶ 오른쪽 채널 재생",
  playingLeft: "왼쪽 재생 중...",
  playingRight: "오른쪽 재생 중...",
  frequencyResponseTest: "주파수 응답 테스트",
  frequencyResponseDesc: "헤드폰의 저음과 고음 성능을 이해하세요",
  startSweep: "스윕 시작",
  sweepInProgress: "스윕 진행 중",
  frequencyTestTip: "어떤 주파수에서 소리가 약해지거나 사라지는지 주목하세요",
  dynamicRangeTest: "다이나믹 레인지 테스트",
  dynamicRangeDesc: "헤드폰의 세부사항 재현 능력을 테스트",
  dynamicRangeTestTip: "조용한 배경 디테일과 큰 전경 소리를 모두 들을 수 있나요?",
  stereoImagingTest: "스테레오 이미징 및 포지셔닝 테스트",
  stereoImagingDesc: "몰입형 3D 오디오 포지셔닝을 경험하세요",
  play3dAudio: "3D 오디오 재생",
  audioPlaying3d: "3D 오디오 재생 중",
  stereoImagingTestTip: "소리가 당신의 머리 주변 3D 공간에서 움직이는 것을 들어보세요",
  stopAllAudio: "모든 오디오 중지",
  testInProgress: "테스트 진행 중",
  testNotStarted: "테스트 시작되지 않음",
  testFailed: "테스트 실패",
  testSkipped: "테스트 건너뜀",
  step: "단계",
  of: "의",
  skip: "이 단계 건너뛰기",
  confirmSkipTest: "테스트 건너뛰기 확인",
  skipTestWarning: "이 테스트를 건너뛰면 완전한 장치 상태 평가를 받을 수 없습니다. 정말 건너뛰시겠습니까?",
  confirmSkip: "건너뛰기 확인",
  cancel: "취소",
  canSkipThisTest: "이 테스트를 건너뛸 수 있습니다",
  stopPlaying: "재생 중지",
  audioError: "오디오 오류",
  stopSweep: "스위프 중지",
  stopTestButton: "테스트 중지",
  startTestButton: "테스트 시작",
  testInstructions: "🎵 테스트 설명",
  testInstructionItem1: "• 스위프 중 어떤 주파수 범위에서 소리가 약해지거나 사라지는지 주의하세요",
  testInstructionItem2: "• 고품질 헤드폰은 전체 주파수 범위에서 균형 잡힌 성능을 유지해야 합니다",
  testInstructionItem3: "• 저음이 너무 강하면 중음을 가릴 수 있고, 고음이 너무 밝으면 자극적일 수 있습니다",
  testInstructionItem4: "• 정확한 결과를 위해 조용한 환경에서 테스트하는 것을 권장합니다",
  testInstructionItem5: "• 실시간 주파수 표시와 스펙트럼이 현재 테스트 주파수를 직관적으로 이해하는 데 도움이 됩니다",
  dynamicRangeTestInProgress: "다이나믹 레인지 테스트 중",
  quietSound: "조용함",
  loudSound: "큰 소리",
  bassDetail: "저음 디테일",
  backgroundEffects: "배경 효과",
  midrangeLayer: "중음역 레이어",
  voiceDialogue: "음성 대화",
  trebleImpact: "고음 임팩트",
  explosionEffects: "폭발 효과",
  frontDirection: "앞",
  backDirection: "뒤",
  leftDirection: "왼쪽",
  rightDirection: "오른쪽",
  leftChannel: "왼쪽 채널",
  rightChannel: "오른쪽 채널",
  center: "중앙",
  ultraLowBass: "초저음",
  lowBass: "저음",
  midLowBass: "중저음",
  midrange: "중음역",
  midTreble: "중고음",
  treble: "고음",
  ultraTreble: "초고음",
  bassDrumBass: "베이스 드럼, 베이스",
  voiceBase: "음성 기본 주파수",
  voiceClarity: "음성 명료도",
  detailAiriness: "디테일, 공기감",
  testProgress: "테스트 진행 상황",
  leftChannelTest: "왼쪽 채널 테스트",
  rightChannelTest: "오른쪽 채널 테스트",
  frequencyTest: "주파수 테스트",
  dynamicRangeTest: "다이나믹 레인지 테스트",
  stereoTest: "스테레오 테스트",
  basicTestRequired: "먼저 좌우 채널 기본 테스트를 완료해주세요",
  basicTestCompleted: "기본 테스트 완료",
  // 요약 및 보고서
  testResultsSummary: "✅ 테스트 결과 요약",
  microphoneTestResult: "마이크 테스트:",
  speakerTestResult: "스피커 테스트:",
  cameraTestResult: "카메라 테스트:",
  testPassed: "통과",
  deviceTestReport: "장치 테스트 보고서",
  generated: "생성됨:",
  scenario: "시나리오:",
  browserInfo: "브라우저 정보:",
  testsCompleted: "완료된 테스트:",
  allHardwareTestsComplete: "모든 하드웨어 테스트가 성공적으로 완료되었습니다.",
  deviceReadyForMeetings: "당신의 장치는 온라인 미팅을 위해 준비되었습니다!",
  copyFailed: "복사 실패",
  copyFailedDesc: "클립보드에 복사할 수 없습니다. 다시 시도하세요.",
  
  // UI 컴포넌트
  close: "닫기",
  previousButton: "이전",
  nextButton: "다음",
  more: "더 보기",
  previousSlide: "이전 슬라이드",
  nextSlide: "다음 슬라이드",
  morePages: "더 많은 페이지",
  toggleSidebar: "사이드바 토글",
  
  // 404 페이지
  pageNotFound: "404",
  oopsPageNotFound: "앗! 페이지를 찾을 수 없습니다",
  returnToHome: "홈으로 돌아가기",
  
  // 마이크 모듈 전용
  microphoneAccess: "마이크 액세스",
  initializingMicrophone: "마이크 초기화 중...",
  micAccessDenied: "마이크 액세스가 거부됨",
  micAccessDeniedDesc: "테스트를 위해 마이크 권한이 필요합니다. 브라우저 설정에서 마이크 액세스를 허용해주세요.",
  howToEnableMic: "💡 마이크 권한을 활성화하는 방법:",
  micPermissionStep1: "• 주소창 왼쪽의 자물쇠 아이콘을 클릭하세요",
  micPermissionStep2: "• 마이크 권한에 대해 \"허용\"을 선택하세요",
  micPermissionStep3: "• 페이지를 새로고침하여 다시 시도하세요",
  micPermissionStep4: "• 또는 브라우저 설정에서 마이크 권한을 수동으로 활성화하세요",
  retry: "다시 시도",
  readyToTest: "마이크 테스트 준비 완료",
  readyToTestDesc: "온라인 미팅 시 당신의 목소리가 명확하게 전달되도록 마이크 품질, 볼륨 레벨 및 실시간 응답 성능을 테스트합니다.",
  testContent: "🎯 테스트 내용:",
  testContentItem1: "• 마이크 장치 감지 및 권한 획득",
  testContentItem2: "• 실시간 오디오 캡처 및 볼륨 모니터링",
  testContentItem3: "• 오디오 품질 및 명확성 평가",
  testContentItem4: "• 환경 소음 감지 및 권장사항",
  testContentItem5: "• 장치 호환성 확인",
  startMicrophoneTest: "마이크 테스트 시작",
  
  // 마이크 테스트 인터페이스
  audioVisualization: "오디오 시각화",
  realTimeWaveform: "🎵 실시간 오디오 파형 - 말할 때 변동이 보입니다",
  startSpeaking: "말하기를 시작하세요...",
  usageTip: "💡 사용 팁: 마이크에 대고 말하면 위의 파형이 당신의 목소리에 따라 실시간으로 변합니다. 파형이 더 활발할수록 마이크가 더 잘 작동하고 있습니다.",
  microphoneStatus: "마이크 상태",
  micConnected: "🎤 마이크 연결됨",
  speakIntoMic: "마이크에 대고 말해서 오디오 입력 효과를 테스트하세요",
  deviceWorking: "장치가 정상적으로 작동 중",
  volumeDetection: "볼륨 감지",
  lowLevel: "낮음",
  goodLevel: "좋음",
  overload: "과부하!",
  dbLow: "-60dB 낮음",
  dbOverload: "0dB 과부하",
  goodRange: "-25dB ~ -8dB 좋은 범위",
  
  // 실시간 모니터링
  realTimeMonitoring: "실시간 모니터링 (자신의 목소리 듣기)",
  monitoring: "모니터링",
  closed: "닫힘",
  monitoringDesc: "활성화하면 실시간으로 자신의 목소리를 들을 수 있어 볼륨과 음질 조정에 도움이 됩니다. 헤드폰 사용을 권장합니다.",
  warningHeadphones: "⚠️ 이 기능을 사용할 때 헤드폰 사용을 권장합니다",
  preventFeedback: "피드백을 방지하기 위해 헤드폰을 사용하거나 스피커 볼륨을 낮춰주세요",
  audioDelay: "오디오 지연",
  avoidOverlap: "소리 겹침 방지",
  monitoringVolume: "모니터링 볼륨",
  notAffectRecording: "녹음에 영향 없음",
  speaker: "스피커",
  monitoringEnabled: "실시간 모니터링 활성화됨",
  adjustSettings: "위의 설정을 실시간으로 조정할 수 있습니다",
  
  // 녹음 테스트
  recordingTest: "녹음 테스트",
  startRecording: "녹음 시작",
  stopRecording: "녹음 중지",
  stopPlayback: "재생 중지",
  playRecording: "녹음 재생",
  recording: "녹음 중",
  playing: "재생 중",
  playbackProgress: "재생 진행률",
  playbackComplete: "재생 완료",
  paused: "일시정지됨",
  recordingInstructions: "💡 사용법: \"녹음 시작\"을 클릭하고 잠시 말한 다음 중지하고 \"녹음 재생\"을 클릭하여 효과를 들어보세요. 이렇게 하면 마이크가 제대로 작동하는지 확인할 수 있습니다.",
  advancedTest: "🔧 고급 테스트: 녹음 전에 위의 오디오 처리 설정을 조정한 다음 녹음하여 다른 설정에서의 음질 차이를 비교할 수 있습니다.",
  
  // 장치 정보
  deviceInfo: "장치 정보",
  sampleRate: "샘플 레이트:",
  channels: "채널:",
  mono: "모노",
  stereo: "스테레오",
  bitDepth: "비트 깊이:",
  
  // 고급 설정
  advancedSettings: "고급 설정",
  whenToAdjust: "🎯 언제 이 설정들을 조정해야 하나요?",
  echoCancellation: "에코 제거",
  echoCancellationDesc: "미팅 중에 상대방이 자신의 목소리 에코를 들을 때 → 이 기능을 활성화해보세요",
  noiseSuppression: "노이즈 억제",
  noiseSuppressionDesc: "배경 소음이 너무 커서 통화 품질에 영향을 줄 때 → 활성화하여 키보드, 팬 및 기타 소음을 줄이세요",
  autoGainControl: "자동 게인 제어",
  autoGainControlDesc: "음성 볼륨이 불안정하여 때로는 크고 때로는 작을 때 → 활성화하여 볼륨 레벨을 자동으로 조정하세요",
  realTimeEffect: "⚡ 실시간 효과: 스위치를 토글한 후 설정이 즉시 적용되며, 전후 효과를 비교하기 위해 녹음할 수 있습니다",
  enabled: "활성화됨",
  disabled: "비활성화됨",
  preventEcho: "상대방이 자신의 목소리 에코를 듣는 것을 방지하며, 스피커 시나리오에 적합합니다",
  mayEcho: "⚠️ 비활성화하면 에코가 발생할 수 있으니 헤드폰을 권장합니다",
  filterNoise: "키보드 소리, 팬 소리, 에어컨 소리 및 기타 배경 소음을 지능적으로 필터링",
  improveQuality: "✅ 활성화하면 통화 품질을 크게 향상시킬 수 있습니다",
  autoAdjustVolume: "볼륨 강도를 자동으로 조정하여 목소리가 너무 크거나 작아서 청취 경험에 영향을 주는 것을 방지",
  naturalVariation: "💡 비활성화하면 자연스러운 볼륨 변화를 들을 수 있습니다",
  
  // 테스트 제안
  testSuggestions: "💡 테스트 제안",
  defaultFirst: "먼저 기본 설정으로 녹음하여 기준 참조로 사용하세요",
  compareSettings: "다른 설정을 하나씩 토글하고 매번 녹음하여 효과 차이를 비교하세요",
  quietEnvironment: "조용한 환경에서는 \"노이즈 억제\"를 끄고, 시끄러운 환경에서는 켜세요",
  speakerEcho: "스피커 사용 시 \"에코 제거\"를 켜고, 헤드폰 사용 시에는 끌 수 있습니다",
  
  stopTestButton: "테스트 중지",
  backToHomeButton: "홈으로 돌아가기",
  nextSpeakerTestButton: "다음: 스피커 테스트",
  
  // 더블클릭 감지 기능
  startDoubleClickDetection: "더블클릭 감지 시작",
  stopMonitoring: "모니터링 중지",
  doubleClickDetectionInProgress: "더블클릭 감지 진행 중",
  duration: "지속 시간:",
  detectedSevereHardwareFailure: "심각한 하드웨어 오류 감지됨",
  mouseMayHaveIssues: "마우스에 문제가 있을 수 있습니다",
  sporadicIssuesContinueMonitoring: "간헐적 문제, 모니터링 계속",
  mouseWorkingNormally: "마우스가 정상적으로 작동합니다",
  detected: "감지됨",
  issues: "개 문제",
  performSingleClickTest: "단일 클릭 테스트 수행",
  tryNormalSingleClickOperations: "정상적인 단일 클릭 조작을 시도하세요",
  systemWillAutoDetectDoubleClickIssues: "시스템이 예상치 못한 더블클릭 문제를 자동으로 감지합니다",
  doubleClickIssueDetection: "더블클릭 문제 감지",
  noDoubleClickIssuesDetected: "더블클릭 문제가 감지되지 않았습니다",
  continueWithSingleClickTesting: "단일 클릭 테스트 계속",
  severeHardwareFailureShortInterval: "심각한 하드웨어 오류 - 극히 짧은 간격 더블클릭",
  possibleHardwareFailureUnexpectedDoubleClick: "하드웨어 오류 가능성 - 예상치 못한 더블클릭",
  potentialIssueFastDoubleClick: "잠재적 문제 - 빠른 더블클릭",
  interval: "간격:",
  normal: "정상:",
  doubleClickDetectionInstructions: "더블클릭 감지 설명",
  doubleClickTip1: "정상적인 단일 클릭 조작을 수행하면 시스템이 예상치 못한 더블클릭을 자동으로 감지합니다",
  doubleClickTip2: "빨간색 경고: 간격<20ms, 심각한 하드웨어 오류",
  doubleClickTip3: "노란색 경고: 간격<50ms, 하드웨어 오류 가능성",
  doubleClickTip4: "파란색 알림: 간격이 짧지만 정상 범위 내",
  doubleClickTip5: "위의 타이머는 문제의 심각도 진단에 도움이 되는 밀리초를 표시합니다",
  doubleClickTip6: "감지 완료 후 정상적인 단일 클릭 테스트로 마우스 기능을 확인할 수 있습니다",
  
  // TestWorkflowPage 전용 번역 키
  networkQualityTest: "네트워크 품질 테스트",
  notTested: "테스트 안됨",
  failed: "실패",
  failureReason: "실패 원인",
  onlineMeetingDeviceReport: "온라인 미팅 장치 테스트 보고서",
  generatedTime: "생성 시간",
  testScenario: "테스트 시나리오",
  onlineMeetingScenario: "온라인 미팅 시나리오",
  unknownScenario: "알 수 없는 시나리오",
  testCompletionStatus: "테스트 완료 상태",
  testCompletionCount: "테스트 완료",
  testResultsTitle: "테스트 결과",
  allHardwareTestsPassed: "모든 하드웨어 테스트가 통과했습니다! 장치가 온라인 미팅을 위해 준비되었습니다.",
  testsPassed: "테스트 통과",
  partialTestsCompleted: "일부 테스트가 미완료되었습니다. 모든 테스트 항목을 완료하는 것을 권장합니다.",
  checkFailedTests: "실패한 테스트 항목을 확인하는 것을 권장합니다.",
  suggestions: "제안 사항",
  suggestion1: "1. 안정적인 네트워크 연결을 확보하세요",
  suggestion2: "2. 카메라를 눈높이에 배치하세요",
  suggestion3: "3. 좋은 조명 조건을 유지하세요",
  suggestion4: "4. 더 나은 오디오 경험을 위해 헤드폰을 사용하세요",
  retestFailedDevices: "5. 실패한 장치의 경우 재테스트하거나 하드웨어 연결을 확인하는 것을 권장합니다",

  testResults: "테스트 결과",
  testsCompletedCount: "{completed}/{total} 테스트 완료",
  meetingDeviceTestReport: "미팅 장치 테스트 보고서",
  reason: "이유",
  allDevicesReady: "모든 장치가 정상적으로 작동하고 있으며, 미팅을 시작할 준비가 되었습니다!",
  someTestsFailed: "일부 테스트가 실패했습니다. 관련 장치를 확인하는 것을 권장합니다.",
  completeAllTests: "포괄적인 장치 상태 평가를 위해 모든 테스트를 완료해 주세요.",
  
  // 네트워크 테스트 기능
  networkTestDesc: "미팅 중 안정성을 보장하기 위해 네트워크 연결 품질을 테스트합니다",
  networkQualityTestDesc: "미팅 중 안정성을 보장하기 위해 네트워크 연결 품질을 테스트합니다",
  testProgress: "테스트 진행 상황",
  testingNetworkLatency: "네트워크 지연, 다운로드 및 업로드 속도를 테스트 중...",
  networkQualityAssessment: "네트워크 품질 평가",
  excellent: "우수",
  good: "양호",
  fair: "보통",
  poor: "불량",
  latency: "지연",
  download: "다운로드",
  upload: "업로드",
  jitter: "지터",
  recommendations: "권장 사항",
  networkExcellentDesc: "우수한 네트워크 품질입니다! HD 비디오 통화와 부드러운 화면 공유를 즐기실 수 있습니다.",
  networkGoodDesc: "양호한 네트워크 품질로 비디오 회의에 적합합니다. 다른 대역폭 소모 앱을 종료하는 것을 고려해 보세요.",
  networkFairDesc: "보통의 네트워크 품질입니다. 오디오 안정성을 보장하기 위해 비디오 품질을 낮추거나 비디오를 끄는 것을 고려해 보세요.",
  networkPoorDesc: "불량한 네트워크 품질입니다. 네트워크 연결을 확인하거나 네트워크 관리자에게 문의하는 것을 권장합니다.",
  serverIP: "서버 IP",
  timezone: "시간대",
  userLocation: "사용자 위치",
  serverLocation: "테스트 서버 위치",
  distance: "거리",
  approximateDistance: "대략적인 거리",
  gpsLocation: "GPS 위치",
  ipLocation: "IP 위치",
  timezoneLocation: "시간대 추정",
  unknownLocation: "알 수 없는 소스",
  loadingLocation: "위치 정보 로딩 중...",
  server: "서버",
  startNetworkTest: "네트워크 테스트 시작",
  retestNetwork: "네트워크 재테스트",
  networkOptimizationTips: "네트워크 최적화 팁",
  networkTip1: "• 더 안정적인 네트워크를 위해 WiFi 대신 유선 연결을 사용하세요",
  networkTip2: "• 다른 대역폭 소모 애플리케이션을 종료하세요",
  networkTip3: "• 라우터가 가까이 있고 신호가 양호한지 확인하세요",
  networkTip4: "• 네트워크 피크 시간대에 중요한 미팅을 피하세요",
  testRegion: "테스트 지역",
  nextMicrophoneTest: "다음: 마이크 테스트",
  
  // 향상된 스피커 테스트
  enhancedSpeakerTest: "향상된 스피커 테스트",
  comprehensiveAudioTest: "오디오 출력 장치의 종합적인 테스트",
  volumeControl: "볼륨 제어",
  stereoTest: "스테레오 테스트",
  leftChannel: "왼쪽 채널",
  rightChannel: "오른쪽 채널",
  bothChannels: "양쪽 채널",
  testLeftRightChannels: "왼쪽과 오른쪽 채널이 정상적으로 작동하는지 테스트합니다",
  confirmStereoTestPassed: "스테레오 테스트가 통과했음을 확인하세요",
  frequencySweep: "주파수 스윕",
  sweeping: "스위핑 중...",
  testTone1kHz: "1kHz 테스트 톤",
  testSpeakerFrequencyRange: "스피커의 주파수 응답 범위를 테스트합니다",
  confirmFrequencyTestPassed: "주파수 테스트가 통과했음을 확인하세요",
  speakerTestComplete: "✅ 스피커 테스트 완료",
  allAudioTestsPassed: "모든 오디오 테스트가 통과했습니다. 스피커가 정상적으로 작동합니다!",
  audioOptimizationTips: "오디오 최적화 팁",
  audioTip1: "• 헤드폰을 사용하면 더 나은 오디오 경험을 제공할 수 있습니다",
  audioTip2: "• 시스템 볼륨을 적절한 수준으로 조정하세요",
  audioTip3: "• 오디오 드라이버가 최신인지 확인하세요",
  audioTip4: "• 최상의 효과를 위해 조용한 환경에서 미팅을 진행하세요",
  
  // 향상된 카메라 테스트
  enhancedCameraTest: "향상된 카메라 테스트",
  comprehensiveVideoTest: "비디오 입력 장치의 종합적인 테스트",
  cameraPermissionDeniedDesc: "테스트에는 카메라 권한이 필요합니다. 브라우저 설정에서 카메라 접근을 허용해 주세요.",
  howToEnableCamera: "💡 카메라 권한을 활성화하는 방법:",
  cameraPermissionStep1: "• 주소 표시줄 왼쪽의 잠금 아이콘을 클릭하세요",
  cameraPermissionStep2: "• 카메라 권한에서 \"허용\"을 선택하세요",
  cameraPermissionStep3: "• 페이지를 새로 고침하여 다시 시도하세요",
  cameraPermissionStep4: "• 또는 브라우저 설정에서 카메라 권한을 수동으로 활성화하세요",
  readyToTestCamera: "카메라 테스트 준비 완료",
  readyToTestCameraDesc: "온라인 미팅 중 이미지가 명확하게 전송되도록 카메라의 품질, 해상도 및 실시간 성능을 테스트합니다.",
  cameraTestContent: "�� 테스트 내용:",
  cameraTestContentItem1: "• 카메라 장치 감지 및 권한 획득",
  cameraTestContentItem2: "• 실시간 비디오 캡처 및 품질 모니터링",
  cameraTestContentItem3: "• 비디오 품질 및 선명도 평가",
  cameraTestContentItem4: "• 조명 조건 감지 및 권장 사항",
  cameraTestContentItem5: "• 장치 호환성 검증",
  cameraStatus: "카메라 상태",
  cameraConnected: "📹 카메라 연결됨",
  lookIntoCameraTest: "비디오 입력 효과를 테스트하기 위해 카메라를 바라보세요",
  videoQualityAnalysis: "비디오 품질 분석",
  brightness: "밝기",
  contrast: "대비",
  sharpness: "선명도",
  videoQualityExcellentDesc: "우수한 비디오 품질입니다! 카메라 설정이 미팅 사용에 완벽합니다.",
  videoQualityGoodDesc: "양호한 비디오 품질로 대부분의 미팅 시나리오에 적합합니다.",
  videoQualityFairDesc: "보통의 비디오 품질입니다. 조명이나 카메라 위치 조정을 고려해 보세요.",
  videoQualityPoorDesc: "불량한 비디오 품질입니다. 카메라 설정을 확인하거나 장치를 교체하는 것을 권장합니다.",
  videoOptimizationTips: "비디오 최적화 팁",
  videoTip1: "• 앞쪽에 충분한 조명이 있는지 확인하세요",
  videoTip2: "• 카메라를 눈 높이에 배치하세요",
  videoTip3: "• 더 선명한 이미지를 위해 카메라 렌즈를 청소하세요",
  videoTip4: "• 안정적인 네트워크 연결을 유지하세요",
  completeTest: "테스트 완료",
  
  // 향상된 마이크 테스트
  enhancedMicrophoneTest: "향상된 마이크 테스트",
  comprehensiveMicTest: "오디오 입력 장치의 종합적인 테스트",
  audioQualityAnalysis: "오디오 품질 분석",
  signalToNoiseRatio: "신호 대 잡음비:",
  backgroundNoiseLevel: "배경 잡음 수준:",
  distortionLevel: "왜곡 수준:",
  echoDetection: "에코 감지:",
  notDetected: "감지되지 않음",
  micExcellentDesc: "우수한 오디오 품질입니다! 마이크 설정이 미팅 사용에 완벽합니다.",
  micGoodDesc: "양호한 오디오 품질로 대부분의 미팅 시나리오에 적합합니다.",
  micFairDesc: "보통의 오디오 품질입니다. 마이크 위치 조정이나 배경 잡음 감소를 고려해 보세요.",
  micPoorDesc: "불량한 오디오 품질입니다. 마이크 설정을 확인하거나 장치를 교체하는 것을 권장합니다.",
  echoDetectedWarning: "⚠️ 에코가 감지되었습니다. 헤드폰 사용이나 스피커 볼륨 조정을 권장합니다.",
  micOptimizationTips: "마이크 최적화 팁",
  micTip1: "• 마이크를 입에서 15-20cm 떨어뜨려 두세요",
  micTip2: "• 조용한 환경에서 테스트하세요",
  micTip3: "• 에코 문제를 피하기 위해 헤드폰을 사용하세요",
  micTip4: "• 마이크 볼륨을 적절한 수준으로 조정하세요",
  
  // 테스트 실패 이유
  networkNotTested: "네트워크 테스트가 수행되지 않음",
  latencyTooHigh: "지연이 너무 높음",
  downloadSpeedTooSlow: "다운로드 속도가 너무 느림",
  uploadSpeedTooSlow: "업로드 속도가 너무 느림",
  jitterTooHigh: "네트워크 지터가 너무 높음",
  networkQualityPoor: "네트워크 품질이 불량하여 비디오 회의에 적합하지 않음",
  micPermissionDenied: "마이크 권한이 거부됨, 브라우저 설정에서 마이크 접근을 허용해 주세요",
  micPermissionNotGranted: "마이크 권한이 부여되지 않음",
  noMicrophoneDevices: "사용 가능한 마이크 장치가 감지되지 않음",
  noAudioInput: "마이크에서 오디오 입력이 없음, 장치 연결이나 볼륨 설정을 확인해 주세요",
  audioQualityIssues: "오디오 품질 문제",
  signalToNoiseRatioLow: "신호 대 잡음비가 너무 낮음",
  backgroundNoiseTooHigh: "배경 잡음이 너무 높음",
  audioDistortionHigh: "오디오 왜곡이 높음",
  echoDetected: "에코가 감지됨",
  audioQualityPoor: "오디오 품질이 불량함",
  requiredTestsNotCompleted: "필요한 테스트 항목이 완료되지 않음",
  stereoTestNotCompleted: "스테레오 테스트가 완료되지 않음",
  frequencyTestNotCompleted: "주파수 응답 테스트가 완료되지 않음",
  cameraPermissionDenied: "카메라 권한이 거부됨, 브라우저 설정에서 카메라 접근을 허용해 주세요",
  noCameraDevices: "사용 가능한 카메라 장치가 감지되지 않음",
  cameraNotStarted: "카메라가 시작되지 않음, '카메라 테스트 시작' 버튼을 클릭해 주세요",
  cameraError: "카메라 오류",
  videoQualityIssues: "비디오 품질 문제",
  lightingTooDark: "조명이 너무 어두움",
  lightingTooBright: "조명이 너무 밝음",
  contrastTooLow: "대비가 너무 낮음",
  imageBlurry: "이미지가 흐림",
  videoQualityPoor: "비디오 품질이 불량함",
  
  // 미팅 장치 테스트 완료
  meetingDeviceTestComplete: "미팅 장치 테스트 완료",
  
  // Gaming Setup Check
  gamingSetupCheckTitle: "게이밍 설정 체크",
  gamingScenario: "게이밍 시나리오",
  gamingDeviceTestReport: "게이밍 장치 테스트 보고서",
  gamingDeviceTestComplete: "게이밍 장치 테스트 완료",
  allGamingTestsPassed: "모든 게이밍 테스트를 통과했습니다. 설정이 게이밍 준비 완료되었습니다!",
  gamingNetworkTestDesc: "최적의 게이밍 성능을 위해 네트워크 연결 품질을 테스트합니다",
  gamingKeyboardTestDesc: "게이밍 반응성과 기능을 위해 키보드를 테스트합니다",
  gamingMouseTestDesc: "게이밍용 마우스 정확도와 반응성을 테스트합니다",
  gamingAudioTestDesc: "몰입형 게이밍 경험을 위해 오디오 출력을 테스트합니다",
  keyboardResponseTime: "키보드 응답 시간",
  mouseAccuracy: "마우스 정확도",
  audioLatency: "오디오 지연",
  peripheralPerformance: "주변기기 성능",
  allPeripheralsReady: "모든 주변기기가 게이밍 준비 완료되었습니다!",
  somePeripheralsFailed: "일부 주변기기에 주의가 필요합니다. 위의 실패한 테스트를 확인하세요.",
  completeAllGamingTests: "게이밍 설정 보고서를 받으려면 모든 테스트를 완료하세요.",
  retestFailedPeripherals: "더 나은 성능을 위해 실패한 주변기기를 다시 테스트하는 것을 고려하세요.",
  optimizeGamingSetup: "게이밍 설정 최적화 팁",
  gamingTip1: "• 최저 지연을 위해 유선 주변기기를 사용하세요",
  gamingTip2: "• 마우스 감도를 편안한 수준으로 유지하세요",
  gamingTip3: "• 적절한 반응을 위해 모든 키보드 키를 테스트하세요",
  gamingTip4: "• 오디오에 지연이나 왜곡이 없는지 확인하세요",
  gamingTip5: "• 온라인 게이밍을 위한 네트워크 안정성을 확인하세요",

  // SEO 관련 번역
  siteDescription: "카메라, 마이크, 스피커, 키보드, 마우스 및 네트워크 품질을 위한 전문 장치 테스트 플랫폼. 온라인 회의와 게이밍 설정에 완벽합니다.",
  siteKeywords: "장치 테스트,카메라 테스트,마이크 테스트,스피커 테스트,키보드 테스트,마우스 테스트,네트워크 테스트,온라인 회의,하드웨어 검사,게이밍 설정",
  
  // 페이지 SEO 설명
  homePageDescription: "온라인으로 장치를 테스트하세요 - 카메라, 마이크, 스피커, 키보드, 마우스, 네트워크. 온라인 회의와 게이밍을 위한 무료 하드웨어 호환성 검사.",
  toolsPageDescription: "전문 장치 테스트 도구의 포괄적인 컬렉션. 카메라, 마이크, 스피커, 키보드, 마우스 및 네트워크 품질을 무료로 온라인 테스트하세요.",
  cameraTestDescription: "카메라 품질, 해상도 및 성능을 온라인으로 테스트하세요. 화상 통화와 스트리밍을 위한 카메라 호환성을 확인하세요.",
  microphoneTestDescription: "마이크 오디오 품질, 노이즈 레벨 및 선명도를 테스트하세요. 온라인 회의와 게이밍에서 완벽한 오디오를 보장하세요.",
  headphonesTestDescription: "헤드폰과 스피커의 오디오 품질, 스테레오 밸런스 및 볼륨 레벨을 테스트하세요. 오디오 호환성을 확인하세요.",
  keyboardTestDescription: "모든 키보드 키 기능, 응답 시간 및 키 매핑을 테스트하세요. 타이핑과 게이밍의 최적 성능을 보장하세요.",
  mouseTestDescription: "마우스 정확도, 클릭 반응성, 스크롤 기능 및 정밀도를 테스트하세요. 생산성과 게이밍을 최적화하세요.",
  networkTestDescription: "인터넷 속도, 지연 시간 및 연결 안정성을 테스트하세요. 온라인 회의와 게이밍의 네트워크 품질을 확인하세요.",
  meetingTestDescription: "온라인 회의를 위한 완전한 장치 호환성 검사. 카메라, 마이크, 스피커 및 네트워크 품질을 테스트하세요.",
  gamingTestDescription: "완전한 게이밍 설정 검사. 키보드, 마우스, 헤드폰, 마이크 및 네트워크 성능을 테스트하여 최적의 게이밍 경험을 얻으세요.",

  // 라이브 스트리밍 및 콘텐츠 제작 시나리오
  streamingScenario: "라이브 스트리밍 및 콘텐츠 제작",
  streamingScenarioDesc: "스트리머, 콘텐츠 제작자 및 온라인 교육자를 위한 전문가급 테스트",
  streamingSetupCheckTitle: "라이브 스트리밍 및 콘텐츠 제작 설정",
  streamingDeviceTestReport: "라이브 스트리밍 장치 테스트 보고서",
  streamingDeviceTestComplete: "스트리밍 장치 테스트 완료",
  allStreamingTestsPassed: "모든 스트리밍 장비 테스트를 통과했습니다! 전문적인 콘텐츠 제작을 위한 설정이 준비되었습니다.",
  streamingOptimizationTips: "스트리밍 최적화 팁",
  streamingTip1: "• 안정적인 고속 인터넷 연결 확보 (1080p 스트리밍을 위해 최소 5 Mbps 업로드 속도 필요)",
  streamingTip2: "• 더 나은 오디오 품질을 위해 전용 마이크 사용",
  streamingTip3: "• 전문적인 외관을 위해 조명과 카메라 위치 최적화",
  streamingTip4: "• 오디오 레벨 테스트 및 배경 소음 제거",
  streamingTip5: "• 오디오 품질 모니터링을 위해 헤드폰 사용 고려",
  streamingTestDescription: "완전한 스트리밍 설정 검사. 고품질 콘텐츠 제작을 위해 카메라, 마이크, 헤드폰 및 네트워크 성능을 테스트하세요.",

  // 장치 진단 시나리오
  diagnosticScenario: "완전한 장치 진단",
  diagnosticScenarioDesc: "연결된 모든 장치에 대한 포괄적인 하드웨어 스캔 및 문제 해결",
  diagnosticSetupCheckTitle: "완전한 장치 진단",
  diagnosticDeviceTestReport: "완전한 장치 진단 보고서",
  diagnosticDeviceTestComplete: "장치 진단 완료",
  allDiagnosticTestsPassed: "모든 하드웨어 구성 요소가 정상적으로 작동하고 있습니다. 문제가 감지되지 않았습니다.",
  diagnosticTroubleshootingTips: "문제 해결 권장 사항",
  diagnosticTip1: "• 장치 드라이버를 확인하고 필요시 업데이트",
  diagnosticTip2: "• 모든 하드웨어 연결이 안전한지 확인",
  diagnosticTip3: "• 문제를 격리하기 위해 다른 애플리케이션으로 장치 테스트",
  diagnosticTip4: "• 문제가 지속되면 장치와 브라우저 재시작",
  diagnosticTip5: "• 지속적인 하드웨어 장애에 대해서는 기술 지원에 문의",
  diagnosticTestDescription: "완전한 하드웨어 진단 스캔. 포괄적인 시스템 평가를 위해 카메라, 마이크, 헤드폰, 키보드, 마우스 및 네트워크를 포함한 모든 장치를 테스트하세요.",
  
  // Enhanced network test features
  testingLatency: "지연 시간 테스트 중",
  testingDownload: "다운로드 속도 테스트 중",
  testingUpload: "업로드 속도 테스트 중", 
  testingPacketLoss: "패킷 손실 테스트 중",
  testCompleted: "테스트 완료",
  preparingTest: "테스트 준비 중",
  aimScores: "AIM 사용 시나리오 점수",
  gaming: "게이밍",
  streaming: "스트리밍",
  realTimeCommunication: "실시간 통신",
  loadedMetrics: "로드된 네트워크 메트릭",
  loadedLatency: "로드된 지연 시간",
  loadedJitter: "로드된 지터",
  
  // Missing microphone test properties
  micPermissionDeniedTitle: "마이크 액세스가 필요합니다",
  micPermissionDeniedMessage: "이 테스트를 실행하려면 마이크 액세스가 필요합니다.",
  enableMicPermissionInstructions: "마이크 권한을 활성화하는 방법:",
  enableMicStep1: "1. 브라우저 주소 표시줄의 마이크 아이콘 클릭",
  enableMicStep2: "2. '항상 허용' 선택",
  enableMicStep3: "3. 페이지 새로고침",
  retryTest: "다시 시도",
  waitingConnection: "연결 대기 중",
  startTestingButton: "테스트 시작",
  waitingTesting: "테스트 대기 중",
  
  deviceReady: "장치 테스트 준비 완료",
  applyingNewSettings: "새 설정 적용 중",
  settingsApplied: "설정이 적용됨",
  applySettingsError: "새 설정 적용 오류, 다시 시도해주세요",
  realTimeAudioDelay: "실시간 오디오 지연",
  seconds: "초",
  preventSoundOverlap: "소리 겹침 방지",
  adjustAnytime: "위의 설정은 언제든지 조정할 수 있습니다",
  playingInProgress: "재생 중",
  recordingCompleted: "녹음 완료",
  playbackStopped: "재생 중지",
  microphoneInformation: "마이크 정보",
  gettingDeviceInfo: "장치 정보 가져오는 중...",
  pleaseTurnOnTest: "장치 정보를 얻으려면 먼저 테스트를 시작해주세요",
  whyAdjustSettings: "🎯 언제 이러한 설정을 조정해야 하나요?",
  echoWhenUsingSpeaker: "회의 중 다른 사람들이 자신의 목소리 에코를 들을 때 → 이 기능을 활성화해보세요",
  noisyBackground: "배경 소음이 통화 품질에 영향을 줄 때 → 키보드, 팬 소음 등을 줄이기 위해 활성화하세요",
  unstableVolume: "불안정한 볼륨 수준 → 자동으로 볼륨을 조정하기 위해 활성화하세요",
  settingsApplyImmediately: "⚡ 실시간 효과: 설정은 토글 시 즉시 적용되며, 녹음하여 효과를 비교할 수 있습니다",
  toggleOn: "활성화됨",
  toggleOff: "비활성화됨",
  useHeadphonesToPreventEcho: "⚠️ 비활성화 시 에코가 발생할 수 있으므로 헤드폰 사용을 권장합니다",
  improveCallQuality: "✅ 활성화 시 통화 품질이 크게 향상됩니다",
  hearNaturalVolumeChanges: "💡 비활성화 시 자연스러운 볼륨 변화를 들을 수 있습니다",
  testSuggestionsTitle: "💡 테스트 제안",
  defaultSettingsFirst: "먼저 기본 설정으로 녹음하여 참조로 사용하세요",
  compareEachSetting: "다른 설정을 하나씩 토글하고 매번 녹음하여 효과를 비교하세요",
  noiseSuppressionTip: "조용한 환경에서는 \"노이즈 억제\"를 끄고, 시끄러운 환경에서는 켜세요",
  echoCancellationTip: "스피커 사용 시 \"에코 제거\"를 켜고, 헤드폰 사용 시에는 끌 수 있습니다",
  returnButton: "돌아가기",

  // 오류 메시지 - 카메라
  cameraAccessDenied: "카메라 액세스가 거부되었습니다. 카메라 권한을 허용해주세요.",
  cameraNotFound: "카메라를 찾을 수 없습니다. 카메라를 연결해주세요.",
  cameraAccessFailed: "카메라 액세스에 실패했습니다",

  // 오류 메시지 - 마이크
  microphoneAccessDenied: "마이크 권한이 거부되었습니다. 마이크 액세스를 허용하고 다시 시도해주세요.",
  microphoneAccessFailed: "마이크 액세스에 실패했습니다. 장치를 확인하고 다시 시도해주세요.",
  recordingStartFailed: "녹음 시작에 실패했습니다",

  // SEO 검증 메시지
  seoTitleMissing: "페이지 제목이 없습니다",
  seoTitleTooLong: "페이지 제목이 너무 깁니다 (60자 이내 권장)",
  seoTitleTooShort: "페이지 제목이 너무 짧습니다 (10자 이상 권장)",
  seoDescriptionMissing: "페이지 설명이 없습니다",
  seoDescriptionTooLong: "페이지 설명이 너무 깁니다 (160자 이내 권장)",
  seoDescriptionTooShort: "페이지 설명이 너무 짧습니다 (50자 이상 권장)",

  // 기본 SEO 설정
  defaultSEOTitle: "장치 테스트 플랫폼",
  defaultSEODescription: "카메라, 마이크, 스피커, 키보드, 마우스, 네트워크 품질을 위한 전문 장치 테스트 플랫폼.",

  // 콘솔 메시지
  setLanguageWarning: "LanguageProvider에서 setLanguage가 호출되었습니다. 대신 useLanguageNavigation 훅을 사용하세요.",
  invalidLanguageCode: "유효하지 않은 언어 코드",

  // 시나리오 페이지 라벨
  includedTests: "포함된 테스트:",
  completeHardwareCheck: "완전한 하드웨어 호환성 검사",
  needIndividualTesting: "개별 장치 테스트가 필요하신가요?",
  individualTestingDesc: "특정 하드웨어 구성 요소만 테스트해야 하는 경우 개별 테스트 도구에 직접 액세스할 수 있습니다.",
  browseIndividualTools: "개별 도구 찾아보기",
  selectScenarioDesc: "귀하의 요구에 가장 적합한 테스트 시나리오를 선택하세요. 각 시나리오에는 특정 사용 사례를 위해 설계된 엄선된 테스트 세트가 포함되어 있습니다.",

  // 시나리오 시간
  duration5to8: "5-8분",
  duration8to12: "8-12분",
  duration6to10: "6-10분",
  duration10to15: "10-15분",

  // 난이도 수준
  difficultyBeginner: "초급",
  difficultyAdvanced: "고급",
  difficultyIntermediate: "중급",
  difficultyComprehensive: "포괄적",

  // 사용 설명
  usageMeeting: "화상 통화, 원격 근무 및 온라인 회의에 완벽",
  usageGaming: "게임 성능 및 경쟁 플레이에 최적화",
  usageStreaming: "고품질 스트리밍 및 콘텐츠 제작에 최적화",
  usageDiagnostic: "문제 해결 및 장치 검증을 위한 완전한 시스템 검사",

  // 키보드 테스트 모듈
  keyboardTestInstructions: "테스트 지침",
  keyboardTestInstruction1: "• 테스트 시작을 클릭한 후 일반적으로 사용되는 게임 키를 눌러주세요",
  keyboardTestInstruction2: "• 권장 테스트: WASD, 스페이스, Shift, Ctrl, Q, E 등",
  keyboardTestInstruction3: "• 키 응답 시간과 커버리지를 측정합니다",
  keyboardTestInstruction4: "• 완료하려면 최소 10번의 키 입력을 테스트하세요",
  startKeyboardTest: "키보드 테스트 시작",
  keyboardTestingActive: "키보드 테스트 중...",
  keyboardTestStopped: "키보드 테스트 중지됨",
  keyPressCount: "키 입력 횟수",
  averageLatency: "평균 지연 시간",
  keyCoverage: "키 커버리지",
  keyTestStatus: "키 테스트 상태",
  recentKeys: "최근 키 (지연 시간)",
  stopKeyboardTest: "테스트 중지",
  continueKeyboardTest: "테스트 계속",
  resetKeyboardTest: "재설정",
  keyboardTestComplete: "키보드 테스트 완료!",
  keyboardTestCompleteDesc: "더 많은 키를 테스트하거나 다음 단계로 진행할 수 있습니다",
  insufficientKeyCoverage: "키 커버리지 부족 - 더 많은 게임 키를 테스트해주세요",
  highInputLatency: "높은 입력 지연 시간이 감지됨",
  notEnoughKeyPresses: "키 입력 부족 - 더 많은 키를 테스트해주세요",

  // 키보드 지연 시간 테스트 (KeyboardTest.tsx)
  keyboardLatencyTest: "키보드 지연 시간 테스트",
  testKeyboardResponseTime: "키보드 응답 시간과 입력 지연을 테스트하세요",
  latencyShortestLatency: "최단 지연 시간",
  latencyAverageLatency: "평균 지연 시간",
  latencyScanRate: "스캔 속도",
  latencyConnection: "연결",
  latencyNoData: "데이터 없음",
  latencyPresses: "회 입력",
  latencyMaxLatency: "최대",
  latencyHistory: "지연 시간 기록",
  latencyTestInstructions: "지연 시간 테스트 지침",
  latencyInstruction1: "• 키를 눌렀다 떼어 응답 시간을 측정하세요",
  latencyInstruction2: "• 지연 시간이 짧을수록 = 성능이 좋음",
  latencyInstruction3: "• 스캔 속도는 최단 키 입력 시간으로 계산됩니다",
  latencyInstruction4: "• 게임용 키보드는 일반적으로 <5ms 지연 시간",
  latencyInstruction5: "• 일반 키보드는 평균 10-15ms 지연 시간",
  latencyPerformanceAssessment: "성능 평가",
  latencyOverallRating: "전체 평가",
  latencyRecommendation: "권장 사항",
  latencyKeyCharacteristics: "주요 특성",
  latencyEstimatedPollingRate: "예상 폴링 속도: ~1000Hz",
  latencyConnectionType: "연결 유형",
  latencyResponseConsistency: "응답 일관성",
  latencyVeryConsistent: "매우 일관됨",
  latencyVariable: "변동적",
  latencyExcellent: "우수",
  latencyVeryGood: "매우 좋음",
  latencyGood: "좋음",
  latencyAverage: "보통",
  latencyPoor: "나쁨",
  latencyExcellentForGaming: "게임 및 전문 용도에 우수함",
  latencyGoodForGeneral: "일반 사용에 적합하며 캐주얼 게임에 적합함",
  latencyConsiderUpgrading: "더 나은 성능을 위해 업그레이드를 고려하세요",
  latencyUsbEstimated: "USB (추정)",
  latencyUnknown: "알 수 없음",

  // 개인정보 보호정책 및 쿠키 정책
  privacyPolicy: "개인정보 보호정책",
  cookiePolicy: "쿠키 정책",
  privacyPolicyTitle: "개인정보 보호정책",
  cookiePolicyTitle: "쿠키 정책",
  lastUpdated: "최종 업데이트",
  effectiveDate: "시행일",
  contactUs: "문의하기",

  // 쿠키 동의
  cookieConsentTitle: "쿠키 설정",
  cookieConsentDescription: "저희는 브라우징 경험을 개선하고, 사이트 트래픽을 분석하며, 콘텐츠를 개인화하기 위해 쿠키를 사용합니다. 모든 쿠키를 수락하거나 설정을 사용자 정의할 수 있습니다.",
  cookieSettingsTitle: "쿠키 설정",
  cookieSettingsDescription: "저희는 귀하의 경험을 최적화하기 위해 다양한 유형의 쿠키를 사용합니다. 각 카테고리를 활성화하거나 비활성화할 수 있지만, 일부 기능이 영향을 받을 수 있음을 유의하시기 바랍니다.",
  acceptAll: "모두 수락",
  rejectAll: "모두 거부",
  customize: "사용자 정의",
  saveSettings: "설정 저장",
  required: "필수",

  // 쿠키 카테고리
  cookieNecessaryTitle: "필수 쿠키",
  cookieNecessaryDesc: "이러한 쿠키는 웹사이트의 적절한 기능에 필수적이며 비활성화할 수 없습니다. 일반적으로 개인정보 설정, 로그인 또는 양식 작성과 같은 귀하의 작업에 대한 응답으로만 설정됩니다.",
  cookieAnalyticsTitle: "분석 쿠키",
  cookieAnalyticsDesc: "이러한 쿠키는 방문자가 저희 웹사이트와 상호 작용하는 방식을 이해하는 데 도움이 되며, 익명 정보를 수집하고 보고합니다. 이는 웹사이트 성능과 사용자 경험을 개선하는 데 도움이 됩니다.",
  cookieMarketingTitle: "마케팅 쿠키",
  cookieMarketingDesc: "이러한 쿠키는 관련성 있고 개인화된 광고를 표시할 목적으로 웹사이트 간에 방문자를 추적하는 데 사용됩니다.",
  cookiePreferencesTitle: "환경설정 쿠키",
  cookiePreferencesDesc: "이러한 쿠키를 통해 웹사이트는 귀하가 선택한 사항(사용자 이름, 언어 또는 지역 등)을 기억하고 향상되고 더욱 개인화된 기능을 제공할 수 있습니다.",

  // 개인정보 보호정책 내용
  privacyIntroduction: "Setup Check(저희, 당사 또는 회사)는 귀하의 개인정보를 보호하는 것을 약속합니다. 이 개인정보 보호정책은 귀하가 저희 기기 테스트 서비스를 사용할 때 저희가 어떻게 정보를 수집, 사용 및 보호하는지 설명합니다.",
  dataCollectionTitle: "수집하는 정보",
  dataCollectionContent: "저희는 다음과 같은 유형의 정보를 수집합니다:\n\n• **기기 정보**: 브라우저 유형, 운영 체제, 기기 모델 및 화면 해상도\n• **사용 데이터**: 테스트 결과 및 상호 작용 패턴을 포함한 서비스 사용 방법\n• **기술 데이터**: IP 주소(익명화), 접근 시간 및 페이지 조회수\n• **쿠키 및 유사 기술**: 사용자 경험 개선 및 웹사이트 성능 분석에 사용\n\n자발적으로 제공하지 않는 한 이름, 이메일 주소 또는 전화번호와 같은 개인 식별 정보는 수집하지 않습니다.",
  dataUsageTitle: "정보 사용 방법",
  dataUsageContent: "수집된 정보는 다음 목적으로 사용됩니다:\n\n• **서비스 제공**: 기기 테스트 실행 및 결과 표시\n• **서비스 개선**: 사용자 경험을 최적화하기 위한 사용 패턴 분석\n• **기술 지원**: 기술적 문제 진단 및 해결\n• **보안**: 남용 또는 악의적인 활동 탐지 및 방지\n• **규정 준수**: 법적 의무 및 규제 요구 사항 충족",
  thirdPartyServicesTitle: "제3자 서비스",
  thirdPartyServicesContent: "저희는 다음과 같은 제3자 서비스를 사용합니다:\n\n• **Google Analytics 4**: 웹사이트 분석 및 성능 모니터링을 위해. Google은 익명의 사용 데이터를 수집할 수 있습니다. 쿠키 설정을 통해 이 데이터 수집을 제어할 수 있습니다.\n• **콘텐츠 전송 네트워크(CDN)**: 웹사이트 로딩 속도 향상을 위해\n\n이러한 서비스에는 자체 개인정보 보호정책이 있으며, 데이터 처리 관행을 이해하기 위해 해당 정책을 검토하는 것을 권장합니다.",
  userRightsTitle: "귀하의 권리",
  userRightsContent: "적용되는 데이터 보호법에 따라 귀하는 다음과 같은 권리를 가집니다:\n\n• **접근권**: 저희가 보유한 귀하에 대한 정보에 대한 접근 요청\n• **정정권**: 부정확한 정보의 정정 요청\n• **삭제권**: 개인정보 삭제 요청\n• **제한권**: 정보 처리 제한 요청\n• **이동권**: 구조화된 형식으로 데이터 수신 요청\n• **이의제기권**: 정보 처리에 대한 이의제기\n\n이러한 권리를 행사하려면 아래 연락처 정보를 사용하여 문의하시기 바랍니다.",
  dataSecurityTitle: "데이터 보안",
  dataSecurityContent: "저희는 귀하의 정보를 보호하기 위해 적절한 기술적 및 조직적 조치를 구현합니다:\n\n• **암호화**: 모든 데이터 전송에 HTTPS 암호화 사용\n• **접근 제어**: 승인된 직원에게만 데이터 접근 제한\n• **정기 감사**: 보안 조치의 정기적 검토\n• **데이터 최소화**: 필요한 정보만 수집\n• **익명화**: 가능한 경우 데이터 익명화",
  contactInformationTitle: "연락처 정보",
  contactInformationContent: "이 개인정보 보호정책에 대한 질문이 있거나 권리를 행사해야 하는 경우 문의하시기 바랍니다:\n\n• **이메일**: <EMAIL>\n• **주소**: [회사 주소]\n\n접수 후 30일 이내에 요청에 응답하겠습니다.",

  // 쿠키 정책 내용
  cookieIntroduction: "이 쿠키 정책은 Setup Check이 쿠키 및 유사한 기술을 사용하여 귀하가 저희 웹사이트를 방문할 때 귀하를 인식하는 방법을 설명합니다. 이러한 기술이 무엇이며 저희가 왜 사용하는지, 그리고 저희의 사용을 제어할 수 있는 귀하의 권리에 대해 설명합니다.",
  whatAreCookiesTitle: "쿠키란 무엇인가",
  whatAreCookiesContent: "쿠키는 웹사이트를 방문할 때 컴퓨터나 모바일 기기에 저장되는 작은 데이터 파일입니다. 쿠키는 웹사이트를 작동시키거나 더 효율적으로 작동시키고 보고 정보를 제공하기 위해 웹사이트 소유자가 널리 사용합니다.\n\n웹사이트 소유자(이 경우 Setup Check)가 설정하는 쿠키를 '퍼스트 파티 쿠키'라고 합니다. 웹사이트 소유자가 아닌 다른 당사자가 설정하는 쿠키를 '서드 파티 쿠키'라고 합니다. 서드 파티 쿠키를 통해 웹사이트에서 또는 웹사이트를 통해 서드 파티 기능이나 기능성을 제공할 수 있습니다(예: 광고, 대화형 콘텐츠 및 분석).",
  cookieTypesTitle: "사용하는 쿠키 유형",
  cookieTypesContent: "• **필수 쿠키**: 이러한 쿠키는 저희 웹사이트의 운영에 엄격히 필요합니다. 웹사이트를 탐색하고 웹사이트의 보안 영역에 액세스하는 등의 기능을 사용할 수 있게 해줍니다. 이러한 쿠키 없이는 요청된 서비스를 제공할 수 없습니다.\n\n• **분석 쿠키**: 이러한 쿠키는 방문자가 저희 웹사이트를 어떻게 사용하는지에 대한 정보를 수집합니다. 예를 들어, 방문자가 가장 자주 방문하는 페이지와 웹페이지에서 오류 메시지를 받는지 여부 등입니다. 이러한 쿠키는 방문자를 식별하는 정보를 수집하지 않습니다. 이러한 쿠키가 수집하는 모든 정보는 집계되므로 익명입니다.\n\n• **기능 쿠키**: 이러한 쿠키를 통해 저희 웹사이트는 귀하가 선택한 사항(사용자 이름, 언어 또는 귀하가 있는 지역 등)을 기억하고 향상되고 더욱 개인적인 기능을 제공할 수 있습니다.\n\n• **마케팅 쿠키**: 이러한 쿠키는 웹사이트 간에 방문자를 추적하는 데 사용됩니다. 목적은 개별 사용자에게 관련성 있고 매력적인 광고를 표시하는 것이며, 따라서 게시자와 서드 파티 광고주에게 더 가치가 있습니다.",
  manageCookiesTitle: "쿠키 관리 방법",
  manageCookiesContent: "쿠키는 여러 가지 방법으로 관리할 수 있습니다:\n\n• **쿠키 설정**: 저희 웹사이트의 쿠키 동의 배너를 사용하여 설정을 선택하세요\n• **브라우저 설정**: 대부분의 웹 브라우저에서는 브라우저 설정을 통해 쿠키를 제어할 수 있습니다\n• **옵트아웃 도구**: 다양한 온라인 도구를 사용하여 특정 추적을 옵트아웃할 수 있습니다\n\n특정 쿠키를 비활성화하면 웹사이트의 기능과 사용자 경험에 영향을 줄 수 있음을 유의하시기 바랍니다.",
  thirdPartyCookiesTitle: "서드 파티 쿠키",
  thirdPartyCookiesContent: "저희는 Google Analytics를 사용하여 웹사이트 사용을 분석합니다. Google Analytics는 쿠키를 사용하여 사용자가 사이트를 어떻게 사용하는지 분석하는 데 도움을 줍니다. 쿠키에 의해 생성된 웹사이트 사용에 관한 정보(IP 주소 포함)는 Google로 전송되어 미국의 서버에 Google에 의해 저장됩니다.\n\nGoogle은 웹사이트 사용을 평가하고, 웹사이트 운영자를 위한 웹사이트 활동 보고서를 작성하며, 웹사이트 활동 및 인터넷 사용과 관련된 기타 서비스를 제공할 목적으로 이 정보를 사용합니다.\n\n브라우저에서 적절한 설정을 선택하여 쿠키 사용을 거부할 수 있지만, 이렇게 하면 이 웹사이트의 전체 기능을 사용하지 못할 수 있음을 유의하시기 바랍니다.",

  // 연락처 - 추가 번역
  userFeedback: "사용자 피드백",
  contactEmailTemplate: "문제나 제안사항을 설명해 주세요:\n\n[여기에 문제나 제안사항을 자세히 설명해 주세요]\n\n기술적 문제인 경우 다음 정보를 포함해 주세요:\n- 사용 중인 기기 유형\n- 문제가 발생한 구체적인 단계\n- 오류 메시지 (해당하는 경우)",
  systemInfo: "시스템 정보",
  browser: "브라우저",
  language: "언어",
  timestamp: "타임스탬프",
  contactTitle: "문의하기",
  contactDescription: "문제가 있거나 제안사항이 있으신가요? 여러분의 피드백을 기다리고 있습니다! 이메일로 문의해 주시면 최대한 빨리 답변드리겠습니다.",
  feedbackTypes: "피드백 유형",
  bugReport: "버그 신고",
  featureRequest: "기능 요청",
  generalInquiry: "일반 문의",
  technicalSupport: "기술 지원",
};