/**
 * Français - Glossaire Technique
 * Contient les définitions des termes techniques liés au test d'appareils
 */

import type { GlossaryTranslation } from '../types';

export const frGlossary: GlossaryTranslation = {
  title: "Glossaire Technique",
  terms: {
    resolution: {
      title: "Résolution",
      description: "Dimensions en pixels de la vidéo comme 1920x1080, des valeurs plus élevées signifient une qualité d'image plus claire"
    },
    frameRate: {
      title: "Fréquence d'Images",
      description: "Nombre d'images affichées par seconde, généralement exprimé en fps, affecte la fluidité vidéo"
    },
    latency: {
      title: "Latence",
      description: "Délai de temps dans la transmission de données, mesuré en millisecondes (ms), plus bas est mieux"
    },
    bandwidth: {
      title: "Bande Passante",
      description: "Capacité de transmission réseau, généralement mesurée en Mbps, détermine la vitesse de transfert de données"
    },
    sampleRate: {
      title: "Taux d'Échantillonnage",
      description: "Nombre d'échantillons audio collectés par seconde, les taux communs incluent 44,1kHz, 48kHz"
    },
    bitRate: {
      title: "Débit Binaire",
      description: "Taux de transmission de données audio ou vidéo, affecte la qualité et la taille du fichier"
    },
    dpi: {
      title: "DPI",
      description: "Unité de sensibilité de souris, représente les pixels déplacés par pouce"
    },
    pollingRate: {
      title: "Taux de Sondage",
      description: "Fréquence à laquelle l'appareil rapporte son état à l'ordinateur, mesuré en Hz, plus élevé signifie réponse plus rapide"
    },
    fps: {
      title: "Images Par Seconde (FPS)",
      description: "Nombre d'images affichées par seconde, affecte la fluidité et la qualité vidéo"
    },
    megapixel: {
      title: "Mégapixel",
      description: "Unité de base des images numériques, les mégapixels déterminent la netteté de l'image"
    },
    exposure: {
      title: "Exposition",
      description: "Niveau de sensibilité à la lumière de la caméra, affecte la luminosité et la clarté de l'image"
    },
    noiseReduction: {
      title: "Réduction de Bruit",
      description: "Technologie pour supprimer le bruit et les sons de fond de l'audio"
    },
    sensitivity: {
      title: "Sensibilité",
      description: "Capacité du microphone à détecter les signaux sonores"
    },
    frequency: {
      title: "Fréquence",
      description: "Nombre de vibrations du son ou des signaux électriques, mesuré en Hz"
    },
    impedance: {
      title: "Impédance",
      description: "Résistance de l'appareil audio au courant, affecte l'adaptation de puissance"
    },
    soundStage: {
      title: "Scène Sonore",
      description: "Sens de l'espace et du positionnement de l'audio, reflète les couches de qualité sonore"
    },
    drivers: {
      title: "Haut-parleurs",
      description: "Composants centraux des écouteurs qui convertissent les signaux électriques en son"
    },
    thd: {
      title: "Distorsion Harmonique Totale",
      description: "Indicateur de mesure du niveau de distorsion du signal audio"
    },
    keyTravel: {
      title: "Course de Touche",
      description: "Distance qu'une touche parcourt de la position de repos à l'activation"
    },
    actuationForce: {
      title: "Force d'Activation",
      description: "Pression minimale requise pour activer une touche"
    },
    tactile: {
      title: "Tactile",
      description: "Retour tactile lorsqu'une touche est déclenchée"
    },
    linear: {
      title: "Linéaire",
      description: "Caractéristique où la pression de la touche est proportionnelle à la distance de course"
    },
    polling: {
      title: "Sondage",
      description: "Processus par lequel l'ordinateur vérifie l'état de l'appareil"
    },
    clickLatency: {
      title: "Latence de Clic",
      description: "Temps entre l'action de clic physique et la reconnaissance par le système"
    },
    scrollWheel: {
      title: "Molette de Défilement",
      description: "Composant de souris pour le défilement vertical et horizontal"
    },
    sensor: {
      title: "Capteur",
      description: "Composant qui détecte le mouvement de la souris"
    },
    optical: {
      title: "Optique",
      description: "Type de capteur de souris utilisant la lumière LED"
    },
    laser: {
      title: "Laser",
      description: "Type de capteur de souris utilisant la technologie laser pour une précision élevée"
    },
    cpi: {
      title: "CPI",
      description: "Comptes par pouce, mesure de précision du capteur de souris"
    },
    acceleration: {
      title: "Accélération",
      description: "Fonction qui ajuste la vitesse du curseur en fonction de la vitesse de mouvement de la souris"
    },
    jitter: {
      title: "Gigue",
      description: "Variation du délai de transmission réseau, affecte la stabilité de la connexion"
    },
    packetLoss: {
      title: "Perte de Paquets",
      description: "Pourcentage de paquets de données perdus pendant la transmission réseau"
    },
    ping: {
      title: "Ping",
      description: "Temps de réponse réseau, mesuré en millisecondes"
    },
    throughput: {
      title: "Débit",
      description: "Quantité réelle de données transmises avec succès par unité de temps"
    },
    upload: {
      title: "Téléversement",
      description: "Vitesse de transmission de données de l'appareil local vers le réseau"
    },
    download: {
      title: "Téléchargement",
      description: "Vitesse de réception de données du réseau vers l'appareil local"
    },
    codec: {
      title: "Codec",
      description: "Algorithme de compression et décompression audio/vidéo"
    },
    bitDepth: {
      title: "Profondeur de Bits",
      description: "Nombre de bits utilisés pour représenter chaque échantillon audio"
    },
    dynamicRange: {
      title: "Gamme Dynamique",
      description: "Différence entre les sons les plus forts et les plus faibles qu'un appareil peut reproduire"
    },
    snr: {
      title: "Rapport Signal/Bruit",
      description: "Rapport entre le niveau du signal utile et le niveau de bruit de fond"
    },
    phantom: {
      title: "Alimentation Fantôme",
      description: "Alimentation électrique fournie aux microphones à condensateur via le câble audio"
    },
    cardioid: {
      title: "Cardioïde",
      description: "Motif de captation de microphone en forme de cœur, sensible principalement à l'avant"
    },
    omnidirectional: {
      title: "Omnidirectionnel",
      description: "Motif de captation de microphone qui capte le son de toutes les directions"
    },
    directional: {
      title: "Directionnel",
      description: "Motif de captation de microphone qui se concentre sur une direction spécifique"
    }
  }
};
