# 多语言系统使用指南

这个多语言系统采用了集中化配置的设计，让添加新语言变得非常简单。

## 🌟 系统特点

- **集中化配置**：所有语言相关的配置都在 `src/config/languages.ts` 中
- **按需加载**：翻译文件只在需要时才加载，提高性能
- **类型安全**：完整的 TypeScript 支持，避免翻译键名错误
- **自动回退**：未完成翻译的语言会自动回退到英语
- **缓存机制**：已加载的翻译会被缓存，避免重复加载

## 📁 文件结构

```
src/
├── config/
│   └── languages.ts          # 语言配置中心
├── locales/
│   ├── types.ts              # 翻译键名类型定义
│   ├── index.ts              # 翻译加载器
│   ├── en.ts                 # 英语翻译
│   ├── zh.ts                 # 中文翻译
│   └── README.md             # 本文件
└── hooks/
    └── useLanguage.tsx       # 语言Hook
```

## 🚀 添加新语言（3步完成）

### 第1步：在语言配置中心添加语言

编辑 `src/config/languages.ts`：

```typescript
export const LANGUAGE_CONFIGS: Record<Language, LanguageConfig> = {
  // ... 现有语言 ...
  
  // 添加新语言（例如法语）
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    browserPrefixes: ['fr']
  }
};
```

同时更新 Language 类型：

```typescript
export type Language = 'en' | 'zh' | 'es' | 'de' | 'ja' | 'ko' | 'fr'; // 添加 'fr'
```

### 第2步：创建翻译文件

创建 `src/locales/fr.ts`：

```typescript
import { TranslationKeys } from './types';

export const fr: TranslationKeys = {
  home: "Accueil",
  siteName: "Test d'Appareil",
  // ... 添加所有翻译键值对
};
```

### 第3步：在翻译加载器中添加加载逻辑

编辑 `src/locales/index.ts`，在 `loadTranslation` 函数中添加：

```typescript
case 'fr':
  translationModule = await import('./fr');
  translationCache[language] = translationModule.fr;
  break;
```

## 🛠️ 开发工具和技巧

### 类型安全检查

TypeScript 会自动检查翻译文件是否包含所有必需的键名。如果遗漏了某个翻译，编译时会报错。

### 渐进式翻译

如果某个语言的翻译还没有完成，系统会自动：
1. 使用已翻译的部分
2. 未翻译的部分回退到英语
3. 在控制台显示警告信息

### 翻译键名规范

- 使用小驼峰命名法：`userName` 而不是 `user_name`
- 按功能模块分组，使用注释分隔
- 为相似功能使用一致的命名模式

### 参数替换

支持在翻译中使用参数：

```typescript
// 翻译文件中
stepOf: "第 {current} 步，共 {total} 步"

// 使用时
t('stepOf', { current: 1, total: 5 })
// 输出：第 1 步，共 5 步
```

## 📋 翻译检查清单

添加新语言时，请确保：

- [ ] 在 `languages.ts` 中添加了语言配置
- [ ] 更新了 `Language` 类型定义
- [ ] 创建了对应的翻译文件
- [ ] 在加载器中添加了加载逻辑
- [ ] 翻译文件实现了 `TranslationKeys` 接口
- [ ] 测试了语言切换功能
- [ ] 检查了所有页面的翻译效果

## 🔧 高级配置

### 自定义浏览器语言检测

在 `languages.ts` 中的 `browserPrefixes` 数组可以配置多个前缀：

```typescript
browserPrefixes: ['zh', 'zh-cn', 'zh-tw', 'zh-hk'] // 支持多种中文变体
```

### 添加RTL语言支持

对于从右到左的语言（如阿拉伯语），可以设置：

```typescript
ar: {
  code: 'ar',
  name: 'Arabic',
  nativeName: 'العربية',
  flag: '🇸🇦',
  browserPrefixes: ['ar'],
  isRTL: true  // 标记为RTL语言
}
```

### 翻译文件分割

对于大型项目，可以将翻译文件按模块分割：

```typescript
// locales/en/index.ts
export * from './common';
export * from './navigation';
export * from './testing';
```

## 🤝 贡献指南

1. 添加新语言前，请先检查是否已有相关Issue
2. 确保翻译质量，建议找母语使用者review
3. 测试所有功能页面的翻译效果
4. 更新相关文档

## 📞 获取帮助

如果在添加新语言时遇到问题：

1. 检查控制台是否有错误信息
2. 确认文件路径和命名是否正确
3. 验证 TypeScript 类型定义是否匹配
4. 查看现有语言文件作为参考 