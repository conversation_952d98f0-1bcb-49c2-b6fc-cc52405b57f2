import { TestStepConfig, ScenarioConfig } from '@/types/testWorkflow';

// 会议场景配置
export const meetingScenarioConfig: ScenarioConfig = {
  id: 'meeting',
  name: 'onlineMeeting',
  steps: [
    {
      key: 'network',
      title: 'networkQualityTest',
      description: 'networkTestDesc',
      number: 1,
      allowProceedOnFailure: true, // 网络质量差也可以继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过网络测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'camera',
      title: 'cameraTest',
      description: 'cameraTestDesc',
      number: 2,
      allowProceedOnFailure: true, // 摄像头问题可以继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过摄像头测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'microphone',
      title: 'microphoneTest',
      description: 'microphoneTestDesc',
      number: 3,
      allowProceedOnFailure: true, // 麦克风问题可以继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过麦克风测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'headphones',
      title: 'headphonesTest',
      description: 'headphonesTestDesc',
      number: 4,
      allowProceedOnFailure: true, // 耳机问题可以继续
      requiresUserConfirmation: true, // 需要用户确认听到声音
      canSkip: true, // 允许跳过耳机测试
      autoAdvance: false,
      maxRetries: 2
    },
    {
      key: 'summary',
      title: 'testResults',
      description: 'testResultsDesc',
      number: 5,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: false, // 结果页面不能跳过
      autoAdvance: false
    }
  ]
};

// 游戏场景配置
export const gamingScenarioConfig: ScenarioConfig = {
  id: 'gaming',
  name: 'gamingSetup',
  steps: [
    {
      key: 'network',
      title: 'networkQualityTest',
      description: 'networkTestDesc',
      number: 1,
      allowProceedOnFailure: true, // 允许网络质量差的情况下继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过网络测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'microphone',
      title: 'microphoneTest',
      description: 'microphoneTestDesc',
      number: 2,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过麦克风测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'keyboard',
      title: 'keyboardTest',
      description: 'keyboardTestDesc',
      number: 3,
      allowProceedOnFailure: true, // 允许键盘测试失败后继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过键盘测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'mouse',
      title: 'mouseTest',
      description: 'mouseTestDesc',
      number: 4,
      allowProceedOnFailure: true, // 允许鼠标测试失败后继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过鼠标测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'headphones',
      title: 'headphonesTest',
      description: 'headphonesTestDesc',
      number: 5,
      allowProceedOnFailure: true,
      requiresUserConfirmation: true,
      canSkip: true, // 允许跳过耳机测试
      autoAdvance: false,
      maxRetries: 2
    },
    {
      key: 'summary',
      title: 'testResults',
      description: 'testResultsDesc',
      number: 6,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: false, // 结果页面不能跳过
      autoAdvance: false
    }
  ]
};

// 直播场景配置
export const streamingScenarioConfig: ScenarioConfig = {
  id: 'streaming',
  name: 'streamingSetup',
  steps: [
    {
      key: 'network',
      title: 'networkQualityTest',
      description: 'networkTestDesc',
      number: 1,
      allowProceedOnFailure: true, // 允许网络质量差的情况下继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过网络测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'camera',
      title: 'cameraTest',
      description: 'cameraTestDesc',
      number: 2,
      allowProceedOnFailure: true, // 允许摄像头问题后继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过摄像头测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'microphone',
      title: 'microphoneTest',
      description: 'microphoneTestDesc',
      number: 3,
      allowProceedOnFailure: true, // 允许麦克风问题后继续
      requiresUserConfirmation: false,
      canSkip: true, // 允许跳过麦克风测试
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'headphones',
      title: 'headphonesTest',
      description: 'headphonesTestDesc',
      number: 4,
      allowProceedOnFailure: true,
      requiresUserConfirmation: true,
      canSkip: true, // 允许跳过耳机测试
      autoAdvance: false,
      maxRetries: 2
    },
    {
      key: 'summary',
      title: 'testResults',
      description: 'testResultsDesc',
      number: 5,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: false, // 结果页面不能跳过
      autoAdvance: false
    }
  ]
};

// 诊断场景配置
export const diagnosticScenarioConfig: ScenarioConfig = {
  id: 'diagnostic',
  name: 'diagnosticTest',
  steps: [
    {
      key: 'network',
      title: 'networkQualityTest',
      description: 'networkTestDesc',
      number: 1,
      allowProceedOnFailure: true, // 诊断模式允许所有测试失败
      requiresUserConfirmation: false,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'camera',
      title: 'cameraTest',
      description: 'cameraTestDesc',
      number: 2,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'microphone',
      title: 'microphoneTest',
      description: 'microphoneTestDesc',
      number: 3,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'keyboard',
      title: 'keyboardTest',
      description: 'keyboardTestDesc',
      number: 4,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'mouse',
      title: 'mouseTest',
      description: 'mouseTestDesc',
      number: 5,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 3
    },
    {
      key: 'headphones',
      title: 'headphonesTest',
      description: 'headphonesTestDesc',
      number: 6,
      allowProceedOnFailure: true,
      requiresUserConfirmation: true,
      canSkip: true,
      autoAdvance: false,
      maxRetries: 2
    },
    {
      key: 'summary',
      title: 'testResults',
      description: 'testResultsDesc',
      number: 7,
      allowProceedOnFailure: true,
      requiresUserConfirmation: false,
      canSkip: false,
      autoAdvance: false
    }
  ]
};

// 获取场景配置的辅助函数
export const getScenarioConfig = (scenarioId: string): ScenarioConfig => {
  switch (scenarioId) {
    case 'gaming':
      return gamingScenarioConfig;
    case 'streaming':
      return streamingScenarioConfig;
    case 'diagnostic':
      return diagnosticScenarioConfig;
    case 'meeting':
    default:
      return meetingScenarioConfig;
  }
};
