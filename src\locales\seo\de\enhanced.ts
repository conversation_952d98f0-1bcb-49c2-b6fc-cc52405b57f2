/**
 * Deutsch - Erweiterte SEO-Übersetzungen
 * Enthält Nutzungsanleitungen, technische Spezifikationen, bewährte Praktiken, Industriestandards
 */

import type { EnhancedTranslation } from '../types';

export const deEnhanced: EnhancedTranslation = {
  usageGuide: {
    title: "Umfassende Nutzungsanleitung",
    tips: {
      title: "Professionelle Tipps"
    },
    // Kamera-Nutzungsanleitung
    camera: {
      steps: [
        "Klicken Sie auf 'Erlauben', um dem Browser Zugriff auf Ihr Kameragerät zu gewähren",
        "Warten Sie auf die Kamera-Initialisierung und stellen Sie sicher, dass die Gerätestatus-LED aktiv ist",
        "Beobachten Sie die Vorschau und überprüfen Sie Bildschärfe, Farbgenauigkeit und Belichtung",
        "Testen Sie die Kameraleistung unter verschiedenen Beleuchtungsbedingungen einschließlich hellem und schwachem Licht",
        "Dokumentieren Sie die Testergebnisse einschließlich Auflösung, Bildrate und Gesamtbewertung der Videoqualität"
      ],
      tip: "Wir empfehlen Tests bei gut beleuchteten natürlichen Lichtbedingungen. Vermeiden Sie Gegenlicht oder zu dunkle Umgebungen, die die Testergebnisse beeinträchtigen könnten."
    },
    // Mikrofon-Nutzungsanleitung
    microphone: {
      steps: [
        "Gewähren Sie Browser-Mikrofonberechtigungen und überprüfen Sie die System-Audioeinstellungen",
        "Stellen Sie die angemessene Aufnahmelautstärke ein, um Audioverzerrungen zu vermeiden",
        "Führen Sie 10-15 Sekunden Testaufnahmen bei verschiedenen Lautstärken und Entfernungen durch",
        "Spielen Sie Aufnahmen ab, um die Audioqualität auf Rauschen, Echo oder Latenzprobleme zu überprüfen"
      ],
      tip: "Für beste Testergebnisse verwenden Sie das Mikrofon in einer ruhigen Umgebung und halten Sie einen angemessenen Abstand zum Gerät."
    },
    // Kopfhörer-Nutzungsanleitung
    headphones: {
      steps: [
        "Stellen Sie sicher, dass die Kopfhörer ordnungsgemäß mit dem Audio-Ausgabegerät verbunden sind",
        "Stellen Sie die Systemlautstärke auf einen angenehmen Hörpegel ein",
        "Spielen Sie Test-Audio für linken und rechten Kanal ab, um die Stereo-Balance zu überprüfen",
        "Testen Sie Audio verschiedener Frequenzen, um die Leistung von Bässen, Mitten und Höhen zu bewerten",
        "Führen Sie einen Komforttest für längere Nutzung durch"
      ],
      tip: "Die Lautstärke sollte während der Tests nicht zu hoch sein, da längeres Hören bei hoher Lautstärke das Gehör schädigen kann."
    },
    // Tastatur-Nutzungsanleitung
    keyboard: {
      steps: [
        "Stellen Sie sicher, dass die Tastatur ordnungsgemäß angeschlossen und vom System erkannt wird",
        "Drücken Sie alle Tasten nacheinander, um Antwort und Rückmeldung zu überprüfen",
        "Testen Sie Kombinationstasten-Funktionen wie Strg, Alt, Umschalt und andere Modifikatortasten",
        "Führen Sie einen kontinuierlichen Tipptest durch, um Tastenprellen und Antwortgeschwindigkeit zu überprüfen",
        "Testen Sie spezielle Funktionstasten wie F1-F12 und Multimedia-Steuerungstasten"
      ],
      tip: "Achten Sie darauf, ob Tasten klebrig sind oder doppelt klicken, da dies die Tippeffizienz und das Spielerlebnis beeinträchtigen kann."
    },
    // Maus-Nutzungsanleitung
    mouse: {
      steps: [
        "Bestätigen Sie den Verbindungsstatus der Maus und die Treiberinstallation",
        "Testen Sie die Links- und Rechtsklick-Funktionalität und Antwortgeschwindigkeit",
        "Überprüfen Sie die Glätte und Genauigkeit des Mausrads",
        "Testen Sie die Genauigkeit der Mausbewegung auf verschiedenen Oberflächen",
        "Für Gaming-Mäuse testen Sie zusätzliche Tasten und DPI-Anpassungsfunktionen"
      ],
      tip: "Die Verwendung eines Mauspads kann bessere Tracking-Genauigkeit bieten. Gaming-Benutzer sollten die Tracking-Stabilität bei schnellen Bewegungen testen."
    },
    // Netzwerk-Nutzungsanleitung
    network: {
      steps: [
        "Schließen Sie andere Anwendungen, die viel Netzwerkbandbreite verbrauchen",
        "Verbinden Sie sich mit einer stabilen Netzwerkumgebung, vermeiden Sie mobile Hotspots",
        "Warten Sie, bis der Test abgeschlossen ist, führen Sie während des Tests keine anderen Netzwerkaktivitäten durch",
        "Notieren Sie Download-Geschwindigkeit, Upload-Geschwindigkeit und Latenz-Daten",
        "Führen Sie mehrere Tests durch und nehmen Sie den Durchschnitt, um die Genauigkeit der Ergebnisse sicherzustellen"
      ],
      tip: "Netzwerk-Testergebnisse können von Faktoren wie Serverlast und Netzwerküberlastung beeinflusst werden. Es wird empfohlen, mehrere Tests zu verschiedenen Zeiten durchzuführen, um eine genauere Bewertung der Netzwerkleistung zu erhalten."
    },
    // Meeting-Nutzungsanleitung
    meeting: {
      steps: [
        "Führen Sie alle Gerätetests nacheinander in der empfohlenen Reihenfolge durch",
        "Notieren Sie die Testergebnisse jedes Geräts und mögliche bestehende Probleme",
        "Achten Sie besonders auf die kollaborative Arbeitsleistung von Kamera und Mikrofon",
        "Testen Sie die Gesamtleistung in einer simulierten Meeting-Umgebung",
        "Speichern Sie den Testbericht zur Referenz vor Meetings"
      ],
      tip: "Es wird empfohlen, 24 Stunden vor wichtigen Meetings eine vollständige Geräteerkennung durchzuführen, um ausreichend Zeit für die Behandlung entdeckter Probleme zu gewährleisten."
    },
    // Gaming-Nutzungsanleitung
    gaming: {
      steps: [
        "Testen Sie nacheinander alle Gaming-bezogenen Hardware-Geräte",
        "Konzentrieren Sie sich auf Antwortzeit und Genauigkeit der Eingabegeräte",
        "Testen Sie Gaming-Soundeffekt-Leistung und Sprachkommunikationsqualität der Audio-Geräte",
        "Überprüfen Sie, ob die Netzwerklatenz die Anforderungen für kompetitives Gaming erfüllt",
        "Notieren Sie alle Testdaten und erstellen Sie ein Geräteleistungsprofil"
      ],
      tip: "Esports-Spieler sollten regelmäßige Geräteerkennung durchführen, um sicherzustellen, dass der Gerätestatus auf optimalem Niveau ist. Beachten Sie die Bedeutung der Tastatur- und Maus-Antwortzeit für die Gaming-Leistung."
    },
    // Tools-Nutzungsanleitung
    tools: {
      steps: [
        "Durchsuchen Sie die umfassende Sammlung von Gerätetest-Tools",
        "Wählen Sie das spezifische Tool für das Gerät aus, das Sie testen möchten",
        "Befolgen Sie die detaillierten Testanweisungen für jedes Tool",
        "Vergleichen Sie Ergebnisse zwischen verschiedenen Geräten und Konfigurationen",
        "Verwenden Sie professionelle Berichte für Hardware-Optimierung"
      ],
      tip: "Jedes Tool ist für spezifische Gerätetypen konzipiert. Für umfassende Tests sollten Sie die Verwendung mehrerer Tools in Betracht ziehen, um sicherzustellen, dass alle Ihre Hardware-Komponenten optimal funktionieren."
    },
    // Startseiten-Nutzungsanleitung
    home: {
      steps: [
        "Wählen Sie das geeignete Testszenario für Ihre Bedürfnisse oder individuelle Gerätetests",
        "Führen Sie die Geräteerkennung in der vom System empfohlenen Reihenfolge durch",
        "Lesen Sie sorgfältig die Ergebnisse jedes Tests und die Optimierungsvorschläge",
        "Speichern oder drucken Sie den Testbericht für zukünftige Referenz",
        "Führen Sie regelmäßige Tests durch, um optimale Geräteleistung aufrechtzuerhalten"
      ],
      tip: "Verschiedene Nutzungsszenarien haben unterschiedliche Anforderungen an Geräte. Wählen Sie den am besten geeigneten Testplan entsprechend Ihren tatsächlichen Bedürfnissen."
    }
  },
  technicalSpecs: {
    title: "Technische Spezifikationen und Anforderungen",
    systemRequirements: "Systemanforderungen",
    parameters: "Technische Parameter",
    compatibility: "Kompatibilitätsinformationen",
    camera: {
      systemRequirements: [
        "Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+",
        "Chrome 88+, Firefox 85+, Safari 14+, Edge 88+",
        "Kameragerät mit USB 2.0 oder höheren Spezifikationen",
        "Mindestens 512MB verfügbarer Speicher für Videoverarbeitung",
        "Moderner Browser mit WebRTC-Technologie-Unterstützung"
      ],
      parameters: {
        "Unterstützte Auflösung": "480p-4K",
        "Bildrate-Bereich": "15-60 FPS",
        "Video-Kodierung": "H.264, VP8, VP9",
        "Minimale Bandbreite": "1 Mbps",
        "Latenz": "< 100ms"
      },
      compatibilityNote: "Kompatibel mit den meisten USB-Kameras, Laptop-integrierten Kameras und professioneller Kameraausrüstung. Unterstützt automatische Erkennung und Umschaltung in Multi-Kamera-Umgebungen."
    },
    microphone: {
      systemRequirements: [
        "Browser mit WebRTC Audio API-Unterstützung",
        "Ordnungsgemäß funktionierende Audio-Treiber",
        "Mikrofongerät mit Standard-Audio-Schnittstellen",
        "Normaler Betrieb des System-Audio-Dienstes",
        "Ausreichende CPU-Ressourcen für Echtzeit-Audioverarbeitung"
      ],
      parameters: {
        "Abtastrate": "8kHz-48kHz",
        "Bit-Tiefe": "16-bit, 24-bit",
        "Kanäle": "Mono/Stereo",
        "Frequenzgang": "20Hz-20kHz",
        "Signal-Rausch-Verhältnis": "> 60dB"
      },
      compatibilityNote: "Unterstützt USB-Mikrofone, 3,5mm-Schnittstellen-Mikrofone, drahtlose Mikrofone und andere Typen. Passt sich automatisch an verschiedene Geräte-Audio-Eigenschaften an."
    },
    headphones: {
      systemRequirements: [
        "Ordnungsgemäß funktionierendes Audio-Ausgabegerät",
        "Audio-Treiber mit Mehrkanalfähigkeit",
        "Browser-Audio-Berechtigungen gewährt",
        "Funktionierende Audio-Codecs"
      ],
      parameters: {
        "Frequenzgang": "20Hz-20kHz",
        "Impedanz-Bereich": "16Ω-600Ω",
        "Kanaltrennung": "> 40dB",
        "Gesamte harmonische Verzerrung": "< 0,1%",
        "Maximaler SPL": "100dB SPL"
      },
      compatibilityNote: "Kompatibel mit kabelgebundenen Kopfhörern, drahtlosen Kopfhörern, Lautsprechern und allen Audio-Ausgabegeräten. Unterstützt Stereo-, 5.1-, 7.1-Surround-Sound-Tests."
    },
    keyboard: {
      systemRequirements: [
        "Betriebssystem mit HID-Tastatur-Geräte-Unterstützung",
        "Ordnungsgemäß installierte Tastatur-Treiber",
        "Browser-Tastatur-Event-API-Unterstützung",
        "Angemessene USB/PS2-Schnittstellen-Stromversorgung"
      ],
      parameters: {
        "Antwortzeit": "< 1ms",
        "Tasten-Lebensdauer": "50 Millionen Klicks",
        "Gleichzeitige Tasten": "6-Key-Rollover",
        "Polling-Rate": "1000Hz",
        "Verbindung": "Kabelgebunden/Drahtlos"
      },
      compatibilityNote: "Unterstützt alle Standard-Tastatur-Layouts einschließlich 87-Tasten-, 104-Tasten-, 108-Tasten-Spezifikationen. Kompatibel mit mechanischen, Membran- und kapazitiven Tastaturen."
    },
    mouse: {
      systemRequirements: [
        "System mit Maus-HID-Protokoll-Unterstützung",
        "Ordnungsgemäß funktionierende Maus-Treiber",
        "Browser-Maus-Event-API-Unterstützung",
        "Geeignete Maus-Nutzungsoberfläche"
      ],
      parameters: {
        "DPI-Bereich": "400-16000",
        "Polling-Rate": "125-1000Hz",
        "Beschleunigung": "40G",
        "Maximale Geschwindigkeit": "400 IPS",
        "Klick-Lebensdauer": "20 Millionen Klicks"
      },
      compatibilityNote: "Unterstützt optische Mäuse, Laser-Mäuse, drahtlose Mäuse und alle Typen. Kompatibel mit verschiedenen Marken professioneller Gaming- und Büro-Mäuse."
    },
    network: {
      systemRequirements: [
        "Stabile Internetverbindung",
        "Browser-Netzwerk-API-Unterstützung",
        "Firewall, die Netzwerktests erlaubt",
        "Normaler DNS-Auflösungsdienst"
      ],
      parameters: {
        "Test-Bandbreite": "1Mbps-1Gbps",
        "Latenz-Erkennung": "1-1000ms",
        "Jitter-Messung": "< 5ms",
        "Paketverlust-Erkennung": "< 0,1%",
        "Test-Dauer": "10-30 Sekunden"
      },
      compatibilityNote: "Anwendbar auf alle Arten von Netzwerkverbindungen einschließlich Breitband, Glasfaser, mobile Netzwerke. Bietet genaue Netzwerkleistungsbewertung."
    }
  },
  technicalSpecs: {
    title: "Technische Spezifikationen und Anforderungen",
    systemRequirements: "Systemanforderungen",
    parameters: "Technische Parameter",
    compatibility: "Kompatibilität",
    home: {
      systemRequirements: [
        "Moderner Webbrowser (Chrome 88+, Firefox 85+, Safari 14+, Edge 88+)",
        "JavaScript aktiviert für interaktive Funktionalität",
        "Stabile Internetverbindung (mindestens 5 Mbps empfohlen)",
        "Geräteberechtigungen für Kamera-, Mikrofon- und anderen Hardware-Zugriff",
        "Mindestens 4GB RAM für optimale Multi-Geräte-Testleistung",
        "Aktualisierte Gerätetreiber für genaue Hardware-Erkennung"
      ],
      parameters: {
        "Plattform-Unterstützung": "Windows, macOS, Linux, Android, iOS",
        "Browser-Kompatibilität": "Alle modernen Browser mit WebRTC-Unterstützung",
        "Test-Genauigkeit": "Professionelle Präzision für alle Gerätetypen",
        "Antwortzeit": "Echtzeit-Feedback und sofortige Ergebnisse",
        "Mehrsprachige Unterstützung": "6 Sprachen mit vollständiger Lokalisierung",
        "Berichtserstellung": "Umfassende Testberichte mit detaillierter Analyse",
        "Geräte-Abdeckung": "Kamera, Mikrofon, Lautsprecher, Tastatur, Maus, Netzwerk"
      },
      compatibilityNote: "Unsere umfassende Testplattform unterstützt alle wichtigen Betriebssysteme und Browser und bietet eine konsistente und zuverlässige Testerfahrung auf verschiedenen Geräten und Umgebungen. Entwickelt für persönliche und professionelle Anwendungsfälle."
    }
  },
  bestPractices: {
    title: "Leitfaden für bewährte Praktiken",
    recommended: "Empfohlene Praktiken",
    avoid: "Diese vermeiden",
    optimization: "Leistungsoptimierungs-Tipps",
    home: {
      dos: [
        "Testen Sie Geräte in der tatsächlichen Umgebung, in der sie verwendet werden",
        "Gewähren Sie alle erforderlichen Berechtigungen für genaue Geräteerkennung",
        "Stellen Sie eine stabile Internetverbindung vor umfassenden Tests sicher",
        "Befolgen Sie die empfohlene Testreihenfolge für optimale Ergebnisse",
        "Speichern und vergleichen Sie Testberichte zur Verfolgung der Geräteleistung",
        "Aktualisieren Sie Gerätetreiber regelmäßig für beste Kompatibilität"
      ],
      donts: [
        "Testen Sie nicht mehrere Geräte gleichzeitig, um Ressourcenkonflikte zu vermeiden",
        "Vermeiden Sie Tests in Umgebungen mit elektromagnetischen Störungen",
        "Überspringen Sie keine Browser-Berechtigungsanfragen oder Sicherheitswarnungen",
        "Vermeiden Sie veraltete Browser, die möglicherweise neueste Funktionen nicht unterstützen",
        "Ignorieren Sie keine Systembenachrichtigungen über Gerätetreiber-Updates",
        "Vermeiden Sie Tests während Systemupdates oder schweren Hintergrundprozessen"
      ],
      optimizationTip: "Für optimale Testerfahrung schließen Sie unnötige Anwendungen, stellen Sie sicher, dass Ihr System die Mindestanforderungen erfüllt, und testen Sie Geräte einzeln. Regelmäßige Tests helfen dabei, die Geräteleistung zu erhalten und Probleme frühzeitig zu erkennen."
    }
  },
  industryStandards: {
    title: "Industriestandards und Zertifizierungen",
    compliance: "Standards-Compliance",
    home: {
      list: [
        {
          name: "WebRTC-Standards",
          description: "Web-Echtzeit-Kommunikationsstandards zur Gewährleistung plattformübergreifender Gerätekompatibilität",
          requirement: "Unterstützt WebRTC 1.0-Spezifikation"
        },
        {
          name: "W3C-Webstandards",
          description: "World Wide Web Consortium-Standards für Web-Zugänglichkeit und Kompatibilität",
          requirement: "Entspricht WCAG 2.1-Zugänglichkeitsrichtlinien"
        },
        {
          name: "ISO/IEC 27001",
          description: "Internationaler Standard für Informationssicherheits-Managementsystem",
          requirement: "Befolgt bewährte Datensicherheitspraktiken"
        },
        {
          name: "DSGVO-Konformität",
          description: "Europäische Datenschutz-Grundverordnung für Benutzerdatenschutz",
          requirement: "Gewährleistet Benutzerdatenschutz und Privatsphäre"
        },
        {
          name: "HTML5-Standards",
          description: "Moderne Webtechnologie-Standards für Gerätezugriff und Multimedia",
          requirement: "Unterstützt HTML5 Media Capture API"
        },
        {
          name: "Plattformübergreifende Kompatibilität",
          description: "Multi-Plattform-Gerätetest-Standards zur Gewährleistung universeller Kompatibilität",
          requirement: "Funktioniert auf Windows, macOS, Linux, iOS, Android"
        }
      ],
      complianceNote: "Unsere umfassende Gerätetest-Plattform hält sich an internationale Webstandards, Sicherheitsprotokolle und Zugänglichkeitsrichtlinien und gewährleistet eine zuverlässige und sichere Testerfahrung für Benutzer weltweit."
    }
  }
};
