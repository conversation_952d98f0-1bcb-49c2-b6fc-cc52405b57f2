import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { GlassCard } from "@/components/ui/GlassCard";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { getSEOTranslation } from "@/locales/seo";

interface FAQProps {
  pageType: 'home' | 'tools' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';
}

export const FAQ: React.FC<FAQProps> = ({ pageType }) => {
  const { t, language } = useLanguage();

  const getFAQs = () => {
    const faqData = getSEOTranslation(language, `faq.${pageType}`);
    if (!faqData) return [];
    
    const questions = [];
    for (let i = 1; i <= 5; i++) {
      const question = faqData[`q${i}`];
      if (question) {
        questions.push(`q${i}`);
      }
    }
    return questions;
  };

  const faqs = getFAQs();

  if (faqs.length === 0) return null;

  return (
    <GlassCard className="p-6 mt-8">
      <h3 className="text-xl font-semibold text-white mb-6">
        {getSEOTranslation(language, 'faq.title')}
      </h3>
      
      <Accordion type="single" collapsible className="w-full space-y-2">
        {faqs.map((questionKey, index) => {
          const answerKey = questionKey.replace('q', 'a');
          const faqData = getSEOTranslation(language, `faq.${pageType}`);
          return (
            <AccordionItem 
              key={index} 
              value={`item-${index}`}
              className="border border-white/10 rounded-lg bg-white/5"
            >
              <AccordionTrigger className="px-4 py-3 text-white hover:text-white/80 text-left">
                {faqData[questionKey]}
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-3 text-white/70">
                {faqData[answerKey]}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </GlassCard>
  );
};