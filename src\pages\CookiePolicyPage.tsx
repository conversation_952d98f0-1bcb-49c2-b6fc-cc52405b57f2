import React, { useState } from 'react';
import { ArrowLeft, <PERSON>ie, Calendar, Mail, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useCookieConsent } from '@/hooks/useCookieConsent';
import { GlassCard } from '@/components/ui/GlassCard';
import { PrimaryButton } from '@/components/ui/PrimaryButton';
import { CookieConsentModal } from '@/components/privacy/CookieConsentModal';

const CookiePolicyPage: React.FC = () => {
  const { t, language } = useLanguage();
  const { consent, showCustomizeModal } = useCookieConsent();
  const [showCookieSettings, setShowCookieSettings] = useState(false);

  const formatContent = (content: string) => {
    return content.split('\n').map((line, index) => {
      if (line.startsWith('• **') && line.includes('**:')) {
        // 格式化列表项
        const parts = line.split('**:');
        const title = parts[0].replace('• **', '');
        const description = parts[1];
        return (
          <li key={index} className="mb-3">
            <strong className="text-white">{title}:</strong>
            <span className="text-white/80">{description}</span>
          </li>
        );
      } else if (line.startsWith('• **') && line.endsWith('**')) {
        // 格式化粗体列表项
        const title = line.replace('• **', '').replace('**', '');
        return (
          <li key={index} className="mb-2">
            <strong className="text-white">{title}</strong>
          </li>
        );
      } else if (line.startsWith('• ')) {
        // 普通列表项
        return (
          <li key={index} className="mb-2 text-white/80">
            {line.replace('• ', '')}
          </li>
        );
      } else if (line.trim() === '') {
        // 空行
        return <br key={index} />;
      } else {
        // 普通段落
        return (
          <p key={index} className="mb-4 text-white/80 leading-relaxed">
            {line}
          </p>
        );
      }
    });
  };

  const sections = [
    {
      title: t('whatAreCookiesTitle'),
      content: t('whatAreCookiesContent')
    },
    {
      title: t('cookieTypesTitle'),
      content: t('cookieTypesContent')
    },
    {
      title: t('manageCookiesTitle'),
      content: t('manageCookiesContent')
    },
    {
      title: t('thirdPartyCookiesTitle'),
      content: t('thirdPartyCookiesContent')
    }
  ];

  const handleOpenCookieSettings = () => {
    setShowCookieSettings(true);
  };

  const handleCloseCookieSettings = () => {
    setShowCookieSettings(false);
  };

  const handleSaveCookieSettings = (newConsent: any) => {
    // 这个函数会通过 useCookieConsent hook 处理
    setShowCookieSettings(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="mb-8">
          <Link 
            to={`/${language}`}
            className="inline-flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('backToHome')}
          </Link>
        </div>

        {/* 主要内容 */}
        <GlassCard className="max-w-4xl mx-auto">
          <div className="p-8">
            {/* 标题 */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Cookie className="h-8 w-8 text-orange-400" />
                <h1 className="text-3xl font-bold text-white">
                  {t('cookiePolicyTitle')}
                </h1>
              </div>
              <div className="flex items-center justify-center gap-4 text-sm text-white/60">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{t('lastUpdated')}: 2024-01-19</span>
                </div>
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  <span>{t('effectiveDate')}: 2024-01-19</span>
                </div>
              </div>
            </div>

            {/* 介绍 */}
            <div className="mb-8">
              <p className="text-white/80 leading-relaxed text-lg">
                {t('cookieIntroduction')}
              </p>
            </div>

            {/* Cookie 设置快捷入口 */}
            <div className="mb-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-white font-medium mb-1">
                    {t('cookieSettingsTitle')}
                  </h3>
                  <p className="text-white/70 text-sm">
                    {t('cookieSettingsDescription')}
                  </p>
                </div>
                <PrimaryButton
                  onClick={handleOpenCookieSettings}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {t('customize')}
                </PrimaryButton>
              </div>
            </div>

            {/* 内容章节 */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <section key={index} className="border-l-4 border-orange-400 pl-6">
                  <h2 className="text-xl font-semibold text-white mb-4">
                    {section.title}
                  </h2>
                  <div className="prose prose-invert max-w-none">
                    {formatContent(section.content)}
                  </div>
                </section>
              ))}
            </div>

            {/* 底部操作 */}
            <div className="mt-12 pt-8 border-t border-white/10">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to={`/${language}/privacy-policy`}>
                  <PrimaryButton variant="outline" className="w-full sm:w-auto">
                    {t('privacyPolicy')}
                  </PrimaryButton>
                </Link>
                <PrimaryButton 
                  onClick={handleOpenCookieSettings}
                  className="w-full sm:w-auto flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {t('cookieSettingsTitle')}
                </PrimaryButton>
                <Link to={`/${language}`}>
                  <PrimaryButton className="w-full sm:w-auto">
                    {t('backToHome')}
                  </PrimaryButton>
                </Link>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Cookie 设置模态框 */}
      <CookieConsentModal
        isOpen={showCookieSettings}
        onClose={handleCloseCookieSettings}
        onSave={handleSaveCookieSettings}
        currentConsent={consent}
      />
    </div>
  );
};

export default CookiePolicyPage;
