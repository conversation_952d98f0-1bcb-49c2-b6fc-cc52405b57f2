import React, { useState, useEffect, useRef } from "react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { RotateCcw, Activity, Zap, Gauge } from "lucide-react";

import { useLanguage } from "@/hooks/useLanguage";
import { useDeviceTestTracking, useInteractionTracking } from "@/hooks/useAnalytics";

interface KeyPress {
  key: string;
  code: string;
  timestamp: number;
  latency: number;
}

interface LatencyStats {
  shortest: number;
  longest: number;
  average: number;
  scanRate: number;
  totalPresses: number;
  connectionType: string;
}

export const KeyboardTest: React.FC = () => {
  const { t } = useLanguage();
  const [pressedKeys, setPressedKeys] = useState<Set<string>>(new Set());
  const [keyHistory, setKeyHistory] = useState<KeyPress[]>([]);
  const [isActive, setIsActive] = useState(true);
  const [latencyStats, setLatencyStats] = useState<LatencyStats>({
    shortest: Infinity,
    longest: 0,
    average: 0,
    scanRate: 0,
    totalPresses: 0,
    connectionType: 'Unknown'
  });

  const keyDownTimeRef = useRef<Map<string, number>>(new Map());

  // 分析跟踪
  const { trackTestStart, trackTestComplete } = useDeviceTestTracking();
  const { trackClick } = useInteractionTracking();
  const testStartTimeRef = useRef<number | null>(null);

  // 检测连接类型
  const detectConnectionType = (): string => {
    // 这是一个简化的检测，实际检测会更复杂
    if (navigator.userAgent.includes('Chrome')) {
      return t('latencyUsbEstimated');
    }
    return t('latencyUnknown');
  };

  // 标准108键键盘布局 - 精确还原
  const keyboardLayout = {
    main: [
      ["`", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "-", "=", "Backspace"],
      ["Tab", "Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", "[", "]", "\\"],
      ["CapsLock", "A", "S", "D", "F", "G", "H", "J", "K", "L", ";", "'", "Enter"],
      ["ShiftLeft", "Z", "X", "C", "V", "B", "N", "M", ",", ".", "/", "ShiftRight"],
      ["ControlLeft", "MetaLeft", "AltLeft", "Space", "AltRight", "MetaRight", "ContextMenu", "ControlRight"]
    ],
    functions: [
      ["Escape", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"],
    ],
    extras: [
      ["PrintScreen", "ScrollLock", "Pause"],
      ["Insert", "Home", "PageUp"],
      ["Delete", "End", "PageDown"],
    ],
    arrows: [
      ["", "ArrowUp", ""],
      ["ArrowLeft", "ArrowDown", "ArrowRight"]
    ],
    numpad: [
      "NumLock", "NumpadDivide", "NumpadMultiply", "NumpadSubtract",
      "Numpad7", "Numpad8", "Numpad9", "NumpadAdd",
      "Numpad4", "Numpad5", "Numpad6",
      "Numpad1", "Numpad2", "Numpad3", "NumpadEnter",
      "Numpad0", "NumpadDecimal",
    ]
  };

  useEffect(() => {
    if (!isActive) return;

    // 跟踪测试开始
    if (testStartTimeRef.current === null) {
      testStartTimeRef.current = Date.now();
      trackTestStart('keyboard');
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      event.preventDefault();
      const keyCode = event.code;
      const timestamp = performance.now();

      keyDownTimeRef.current.set(keyCode, timestamp);

      setPressedKeys(prev => new Set(prev).add(keyCode));
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      const keyCode = event.code;
      const timestamp = performance.now();
      
      let latency = 0;
      if (keyDownTimeRef.current.has(keyCode)) {
        const downTime = keyDownTimeRef.current.get(keyCode)!;
        latency = timestamp - downTime;
        keyDownTimeRef.current.delete(keyCode);
        
        // 更新延迟统计
        setLatencyStats(prev => {
          const newStats = { ...prev };
          newStats.totalPresses += 1;
          newStats.shortest = Math.min(newStats.shortest, latency);
          newStats.longest = Math.max(newStats.longest, latency);
          newStats.average = (newStats.average * (newStats.totalPresses - 1) + latency) / newStats.totalPresses;
          newStats.scanRate = newStats.shortest > 0 ? Math.round(1000 / newStats.shortest) : 0;
          newStats.connectionType = detectConnectionType();
          return newStats;
        });
      }
      
      const newKeyPress: KeyPress = { 
        key: event.key, 
        code: event.code, 
        timestamp: timestamp,
        latency: latency
      };
      
      setKeyHistory(prev => [newKeyPress, ...prev.slice(0, 19)]);
      
      setPressedKeys(prev => {
        const newSet = new Set(prev);
        newSet.delete(keyCode);
        return newSet;
      });
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);
    
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [isActive]);

  const getKeyDisplayName = (key: string): string => {
    const keyMap: { [key: string]: string } = {
      "Escape": "Esc", "Backspace": "Backspace", "Tab": "Tab", "CapsLock": "Caps Lock", "Enter": "Enter",
      "ShiftLeft": "Shift", "ShiftRight": "Shift", "ControlLeft": "Ctrl", "ControlRight": "Ctrl",
      "AltLeft": "Alt", "AltRight": "Alt", "MetaLeft": "Win", "MetaRight": "Win",
      "Space": "Space", "ContextMenu": "Menu", "PrintScreen": "Print", "ScrollLock": "Scroll", "Pause": "Pause",
      "Insert": "Insert", "Delete": "Delete", "Home": "Home", "End": "End",
      "PageUp": "PgUp", "PageDown": "PgDn", "ArrowUp": "↑", "ArrowDown": "↓",
      "ArrowLeft": "←", "ArrowRight": "→", "NumLock": "Lock", "NumpadDivide": "/",
      "NumpadMultiply": "*", "NumpadSubtract": "-", "NumpadAdd": "+", "NumpadEnter": "Enter",
      "NumpadDecimal": ".", "Numpad0": "0", "Numpad1": "1", "Numpad2": "2", "Numpad3": "3",
      "Numpad4": "4", "Numpad5": "5", "Numpad6": "6", "Numpad7": "7", "Numpad8": "8", "Numpad9": "9"
    };
    return keyMap[key] || key;
  };

  const getKeyCode = (key: string): string => {
    const codeMap: { [key: string]: string } = {
      "Escape": "Escape", "`": "Backquote", "1": "Digit1", "2": "Digit2", "3": "Digit3", "4": "Digit4",
      "5": "Digit5", "6": "Digit6", "7": "Digit7", "8": "Digit8", "9": "Digit9", "0": "Digit0",
      "-": "Minus", "=": "Equal", "Backspace": "Backspace", "Tab": "Tab", "\\": "Backslash", "Enter": "Enter",
      "CapsLock": "CapsLock", ";": "Semicolon", "'": "Quote", "ShiftLeft": "ShiftLeft", "ShiftRight": "ShiftRight",
      "ControlLeft": "ControlLeft", "ControlRight": "ControlRight", "MetaLeft": "MetaLeft", "MetaRight": "MetaRight",
      "AltLeft": "AltLeft", "AltRight": "AltRight", "Space": "Space", "ContextMenu": "ContextMenu",
      "PrintScreen": "PrintScreen", "ScrollLock": "ScrollLock", "Pause": "Pause",
      "Insert": "Insert", "Delete": "Delete", "Home": "Home", "End": "End",
      "PageUp": "PageUp", "PageDown": "PageDown", "ArrowUp": "ArrowUp", "ArrowDown": "ArrowDown",
      "ArrowLeft": "ArrowLeft", "ArrowRight": "ArrowRight", "NumLock": "NumLock", "NumpadDivide": "NumpadDivide",
      "NumpadMultiply": "NumpadMultiply", "NumpadSubtract": "NumpadSubtract", "NumpadAdd": "NumpadAdd", "NumpadEnter": "NumpadEnter",
      "NumpadDecimal": "NumpadDecimal", "Numpad0": "Numpad0", "Numpad1": "Numpad1", "Numpad2": "Numpad2", "Numpad3": "Numpad3",
      "Numpad4": "Numpad4", "Numpad5": "Numpad5", "Numpad6": "Numpad6", "Numpad7": "Numpad7", "Numpad8": "Numpad8", "Numpad9": "Numpad9",
      "[": "BracketLeft", "]": "BracketRight", ",": "Comma", ".": "Period", "/": "Slash"
    };
    if (key.length === 1 && key.match(/[A-Z]/)) return `Key${key}`;
    if (key.match(/^F\d+$/)) return key;
    return codeMap[key] || key;
  };

  const isKeyPressed = (key: string): boolean => {
    if (!key) return false;
    const code = getKeyCode(key);
    return pressedKeys.has(code);
  };

  const getKeyClass = (key: string): string => {
    const baseClass = "h-12 flex items-center justify-center rounded-lg border font-medium text-sm transition-all duration-150";
    const colorClass = isKeyPressed(key)
      ? "bg-blue-500 border-blue-400 text-white shadow-lg transform scale-95"
      : "bg-white/10 border-white/20 text-white/80 hover:bg-white/15";
    
    // 标准按键尺寸 (1u = 48px)
    let sizeClass = "w-12"; // 1u - 标准按键
    if (key === 'Backspace') sizeClass = 'w-24'; // 2u
    if (key === 'Tab') sizeClass = 'w-[72px]'; // 1.5u
    if (key === '\\') sizeClass = 'w-[72px]'; // 1.5u
    if (key === 'CapsLock') sizeClass = 'w-[84px]'; // 1.75u
    if (key === 'Enter') sizeClass = 'w-[113px]'; // 2.5u - 调整为与Backspace对齐
    if (key === 'ShiftLeft') sizeClass = 'w-[108px]'; // 2.25u
    if (key === 'ShiftRight') sizeClass = 'w-[140px]'; // 3u - 调整为与Backspace对齐
    if (key === 'Space') sizeClass = 'w-[324px]'; // 6.25u
    if (key === 'ControlLeft' || key === 'ControlRight') sizeClass = 'w-[60px]'; // 1.25u
    if (key === 'MetaLeft' || key === 'MetaRight' || key === 'AltLeft' || key === 'AltRight') sizeClass = 'w-[60px]'; // 1.25u
    if (key === 'ContextMenu') sizeClass = 'w-[60px]'; // 1.25u

    return `${baseClass} ${colorClass} ${sizeClass}`;
  };

  const renderKey = (key: string) => (
    <div key={key} className={getKeyClass(key)}>
      <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis px-1">
        {getKeyDisplayName(key)}
      </span>
    </div>
  );

  const renderFunctionKey = (key: string) => (
    <div key={key} className={`w-12 h-10 flex items-center justify-center rounded-lg border font-medium text-xs transition-all duration-150 ${
      isKeyPressed(key)
        ? "bg-blue-500 border-blue-400 text-white shadow-lg transform scale-95"
        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/15"
    }`}>
      <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis px-1">
        {getKeyDisplayName(key)}
      </span>
    </div>
  );

  const renderEditKey = (key: string) => (
    <div key={key} className={`w-12 h-10 flex items-center justify-center rounded-lg border font-medium text-xs transition-all duration-150 ${
      isKeyPressed(key)
        ? "bg-blue-500 border-blue-400 text-white shadow-lg transform scale-95"
        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/15"
    }`}>
      <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis px-1">
        {getKeyDisplayName(key)}
      </span>
    </div>
  );

  const renderArrowKey = (key: string) => (
    <div key={key} className={`w-12 h-10 flex items-center justify-center rounded-lg border font-medium text-xs transition-all duration-150 ${
      isKeyPressed(key)
        ? "bg-blue-500 border-blue-400 text-white shadow-lg transform scale-95"
        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/15"
    }`}>
      <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis px-1">
        {getKeyDisplayName(key)}
      </span>
    </div>
  );

  const renderNumpadKey = (key: string) => {
    const baseClass = "flex items-center justify-center rounded-lg border font-medium text-sm transition-all duration-150";
    const colorClass = isKeyPressed(key)
      ? "bg-blue-500 border-blue-400 text-white shadow-lg transform scale-95"
      : "bg-white/10 border-white/20 text-white/80 hover:bg-white/15";
    
    const sizeClass = (key === 'NumpadAdd' || key === 'NumpadEnter') ? 'h-auto' : 'h-12';

    let gridClass = 'col-span-1 row-span-1';
    if (key === 'Numpad0') gridClass = 'col-span-2';
    if (key === 'NumpadAdd' || key === 'NumpadEnter') gridClass = 'row-span-2';

    return (
      <div key={key} className={`${baseClass} ${colorClass} ${gridClass} ${sizeClass}`}>
        <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis px-1">
          {getKeyDisplayName(key)}
        </span>
      </div>
    );
  };
  
  const formatKeyCode = (code: string): string => {
    return code.replace(/(Key|Digit|Numpad|Arrow)/g, '$1 ');
  };

  const getLatencyColor = (latency: number): string => {
    if (latency < 10) return 'text-green-400';
    if (latency < 20) return 'text-yellow-400';
    if (latency < 30) return 'text-orange-400';
    return 'text-red-400';
  };

  const getLatencyLevel = (latency: number): string => {
    if (latency < 5) return t('latencyExcellent');
    if (latency < 10) return t('latencyVeryGood');
    if (latency < 15) return t('latencyGood');
    if (latency < 25) return t('latencyAverage');
    return t('latencyPoor');
  };

  const reset = () => {
    // 跟踪测试完成
    if (testStartTimeRef.current !== null && latencyStats.totalPresses > 0) {
      const duration = Date.now() - testStartTimeRef.current;
      const result = latencyStats.average < 30 ? 'success' : 'partial';
      trackTestComplete('keyboard', result, duration);
    }

    setPressedKeys(new Set());
    setKeyHistory([]);
    setLatencyStats({
      shortest: Infinity,
      longest: 0,
      average: 0,
      scanRate: 0,
      totalPresses: 0,
      connectionType: t('latencyUnknown')
    });
    keyDownTimeRef.current.clear();
    testStartTimeRef.current = null;
  };

  return (
    <div className="min-h-screen w-full">
      <main className="w-full px-4 py-8 flex justify-center">
        <GlassCard className="mb-6 p-6 w-fit">
          <div className="flex items-center justify-between mb-8 min-w-0">
            <div className="flex-1 min-w-0">
              <h1 className="text-3xl font-bold text-white mb-2">
                {t('keyboardLatencyTest')}
              </h1>
              <p className="text-white/70">
                {t('testKeyboardResponseTime')}
              </p>
            </div>
            <div className="flex space-x-3 flex-shrink-0">
              <PrimaryButton onClick={reset} variant="outline" size="sm">
                <RotateCcw className="h-4 w-4 mr-2" />{t("resetTest")}
              </PrimaryButton>
            </div>
          </div>

          {/* Latency Stats Dashboard */}
          <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white/10 rounded-xl p-4 border border-white/20">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70 text-sm">{t('latencyShortestLatency')}</span>
                <Zap className="h-4 w-4 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {latencyStats.shortest === Infinity ? '---' : `${latencyStats.shortest.toFixed(1)}ms`}
              </div>
              <div className="text-xs text-white/50">
                {latencyStats.shortest !== Infinity ? getLatencyLevel(latencyStats.shortest) : t('latencyNoData')}
              </div>
            </div>

            <div className="bg-white/10 rounded-xl p-4 border border-white/20">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70 text-sm">{t('latencyAverageLatency')}</span>
                <Activity className="h-4 w-4 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {latencyStats.average === 0 ? '---' : `${latencyStats.average.toFixed(1)}ms`}
              </div>
              <div className="text-xs text-white/50">
                {latencyStats.average > 0 ? getLatencyLevel(latencyStats.average) : t('latencyNoData')}
              </div>
            </div>

            <div className="bg-white/10 rounded-xl p-4 border border-white/20">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70 text-sm">{t('latencyScanRate')}</span>
                <Gauge className="h-4 w-4 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">
                {latencyStats.scanRate === 0 ? '---' : `${latencyStats.scanRate}Hz`}
              </div>
              <div className="text-xs text-white/50">
                {latencyStats.scanRate > 0 ? `${latencyStats.totalPresses} ${t('latencyPresses')}` : t('latencyNoData')}
              </div>
            </div>

            <div className="bg-white/10 rounded-xl p-4 border border-white/20">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70 text-sm">{t('latencyConnection')}</span>
                <Activity className="h-4 w-4 text-orange-400" />
              </div>
              <div className="text-lg font-bold text-white">
                {latencyStats.connectionType}
              </div>
              <div className="text-xs text-white/50">
                {latencyStats.longest > 0 ? `${t('latencyMaxLatency')}: ${latencyStats.longest.toFixed(1)}ms` : t('latencyNoData')}
              </div>
            </div>
          </div>

          <div className="flex justify-center">
            <div className="keyboard-grid flex flex-col gap-4">
              {/* Top Row: F-Keys */}
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  {renderFunctionKey("Escape")}
                  <div className="flex gap-1 ml-12">
                    {keyboardLayout.functions[0].slice(1, 5).map(renderFunctionKey)}
                  </div>
                  <div className="flex gap-1 ml-8">
                    {keyboardLayout.functions[0].slice(5, 9).map(renderFunctionKey)}
                  </div>
                  <div className="flex gap-1 ml-8">
                    {keyboardLayout.functions[0].slice(9, 13).map(renderFunctionKey)}
                  </div>
                  <div className="flex gap-1 ml-4">
                    {keyboardLayout.extras[0].map(renderEditKey)}
                  </div>
                </div>
                
              </div>

              {/* Main Keyboard Area */}
              <div className="flex gap-4">
                {/* Main Keys */}
                <div className="flex flex-col gap-1">
                  {keyboardLayout.main.map((row, i) => (
                    <div key={i} className="flex gap-1">
                      {row.map(renderKey)}
                    </div>
                  ))}
                </div>

                {/* Edit Keys & Arrows Column */}
                <div className="flex flex-col gap-4">
                  <div className="grid grid-cols-3 gap-1">
                    {keyboardLayout.extras.slice(1).flat().map(renderEditKey)}
                  </div>
                  <div className="flex flex-col items-center gap-1 mt-16">
                    {keyboardLayout.arrows.map((row, i) => (
                      <div key={i} className="flex gap-1 justify-center">
                        {row.map(k => k ? renderArrowKey(k) : <div key={Math.random()} className="w-12 h-10"></div>)}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Numpad */}
                <div className="grid grid-cols-4 grid-rows-5 gap-1 w-[204px]">
                  {keyboardLayout.numpad.map(renderNumpadKey)}
                </div>
              </div>
            </div>
          </div>
          
          {keyHistory.length > 0 && (
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-white mb-3">
                {t('latencyHistory')}
              </h3>
              <div className="bg-white/5 rounded-xl p-4 max-h-48 overflow-y-auto glass-scrollbar">
                <div className="space-y-2">
                  {keyHistory.map((k, i) => (
                    <div key={`${k.code}-${k.timestamp}`} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-3">
                        <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-xs w-20 text-center">
                          {k.key === " " ? t("space") : k.key}
                        </span>
                        <span className="text-white/60 font-mono">{formatKeyCode(k.code)}</span>
                        <span className={`font-mono text-xs px-2 py-1 rounded ${getLatencyColor(k.latency)} bg-white/10`}>
                          {k.latency.toFixed(1)}ms
                        </span>
                      </div>
                      <span className="text-white/40 text-xs">{new Date(k.timestamp).toLocaleTimeString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">
              {t('latencyTestInstructions')}
            </h4>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>{t('latencyInstruction1')}</li>
              <li>{t('latencyInstruction2')}</li>
              <li>{t('latencyInstruction3')}</li>
              <li>{t('latencyInstruction4')}</li>
              <li>{t('latencyInstruction5')}</li>
            </ul>
          </div>

          {/* Performance Assessment */}
          {latencyStats.totalPresses > 5 && (
            <div className="mt-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-400/30 rounded-xl p-4">
              <h4 className="text-purple-200 font-medium mb-2">{t('latencyPerformanceAssessment')}</h4>
              <div className="text-purple-200/80 text-sm space-y-2">
                <div>
                  <strong>{t('latencyOverallRating')}:</strong> {getLatencyLevel(latencyStats.average)}
                </div>
                <div>
                  <strong>{t('latencyRecommendation')}:</strong> {
                    latencyStats.average < 10
                      ? t('latencyExcellentForGaming')
                      : latencyStats.average < 20
                        ? t('latencyGoodForGeneral')
                        : t('latencyConsiderUpgrading')
                  }
                </div>
                <div>
                  <strong>{t('latencyKeyCharacteristics')}:</strong>
                  <ul className="ml-4 mt-1 list-disc">
                    <li>{t('latencyEstimatedPollingRate')}</li>
                    <li>{t('latencyConnectionType')}: {latencyStats.connectionType}</li>
                    <li>{t('latencyResponseConsistency')}: {((latencyStats.longest - latencyStats.shortest) < 10) ? t('latencyVeryConsistent') : t('latencyVariable')}</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </GlassCard>
      </main>
    </div>
  );
};