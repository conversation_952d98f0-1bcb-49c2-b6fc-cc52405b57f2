import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  initializeGA4, 
  trackPageView, 
  trackEvent, 
  trackDeviceTest, 
  trackUserInteraction,
  isGA4Initialized 
} from '@/lib/analytics';
import { 
  GA4Event, 
  DeviceTestEvent, 
  UserInteractionEvent 
} from '@/types/analytics';

/**
 * GA4 分析 Hook
 */
export const useAnalytics = () => {
  const location = useLocation();

  // 初始化 GA4
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeGA4();
      } catch (error) {
        console.error('GA4 初始化失败:', error);
      }
    };

    initialize();
  }, []);

  // 跟踪页面浏览
  const trackPage = useCallback((title?: string, customPath?: string) => {
    if (!isGA4Initialized()) return;

    const pageData = {
      page_title: title || document.title,
      page_location: window.location.href,
      page_path: customPath || location.pathname,
      language: location.pathname.split('/')[1] || 'en'
    };

    trackPageView(pageData);
  }, [location]);

  // 跟踪自定义事件
  const track = useCallback((event: GA4Event) => {
    if (!isGA4Initialized()) return;
    trackEvent(event);
  }, []);

  // 跟踪设备测试
  const trackDevice = useCallback((event: DeviceTestEvent) => {
    if (!isGA4Initialized()) return;
    trackDeviceTest(event);
  }, []);

  // 跟踪用户交互
  const trackInteraction = useCallback((event: UserInteractionEvent) => {
    if (!isGA4Initialized()) return;
    trackUserInteraction(event);
  }, []);

  return {
    trackPage,
    track,
    trackDevice,
    trackInteraction,
    isInitialized: isGA4Initialized()
  };
};

/**
 * 页面跟踪 Hook - 自动跟踪路由变化
 */
export const usePageTracking = (pageTitle?: string) => {
  const { trackPage } = useAnalytics();
  const location = useLocation();

  useEffect(() => {
    // 延迟跟踪，确保页面完全加载
    const timer = setTimeout(() => {
      trackPage(pageTitle);
    }, 100);

    return () => clearTimeout(timer);
  }, [location.pathname, location.search, trackPage, pageTitle]);
};

/**
 * 设备测试跟踪 Hook
 */
export const useDeviceTestTracking = () => {
  const { trackDevice } = useAnalytics();

  const trackTestStart = useCallback((deviceType: DeviceTestEvent['device_type']) => {
    trackDevice({
      action: 'test_start',
      device_type: deviceType,
      test_result: 'success', // 临时值，实际结果在测试完成时更新
      category: 'device_test'
    });
  }, [trackDevice]);

  const trackTestComplete = useCallback((
    deviceType: DeviceTestEvent['device_type'],
    result: DeviceTestEvent['test_result'],
    duration?: number,
    errorMessage?: string
  ) => {
    trackDevice({
      action: 'test_complete',
      device_type: deviceType,
      test_result: result,
      test_duration: duration,
      error_message: errorMessage,
      category: 'device_test',
      value: duration
    });
  }, [trackDevice]);

  const trackTestError = useCallback((
    deviceType: DeviceTestEvent['device_type'],
    errorMessage: string
  ) => {
    trackDevice({
      action: 'test_error',
      device_type: deviceType,
      test_result: 'failure',
      error_message: errorMessage,
      category: 'device_test'
    });
  }, [trackDevice]);

  return {
    trackTestStart,
    trackTestComplete,
    trackTestError
  };
};

/**
 * 用户交互跟踪 Hook
 */
export const useInteractionTracking = () => {
  const { trackInteraction } = useAnalytics();

  const trackClick = useCallback((
    elementId?: string, 
    elementClass?: string, 
    label?: string
  ) => {
    trackInteraction({
      action: 'click',
      interaction_type: 'click',
      element_id: elementId,
      element_class: elementClass,
      label: label,
      category: 'user_interaction'
    });
  }, [trackInteraction]);

  const trackFormSubmit = useCallback((formName: string, success: boolean) => {
    trackInteraction({
      action: 'form_submit',
      interaction_type: 'form_submit',
      label: formName,
      value: success ? 1 : 0,
      category: 'user_interaction'
    });
  }, [trackInteraction]);

  const trackExternalLink = useCallback((url: string, linkText?: string) => {
    trackInteraction({
      action: 'external_link_click',
      interaction_type: 'external_link',
      label: linkText || url,
      custom_parameters: { destination_url: url },
      category: 'user_interaction'
    });
  }, [trackInteraction]);

  return {
    trackClick,
    trackFormSubmit,
    trackExternalLink
  };
};
