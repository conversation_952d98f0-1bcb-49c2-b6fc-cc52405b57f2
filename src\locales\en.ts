import { TranslationKeys } from './types';

export const en: TranslationKeys = {
  // 基础导航
  home: "Home",
  tools: "Tools",
  meetingTest: "Meeting Check",
  keyboardTest: "Keyboard Test",
  mouseTest: "Mouse Test",
  headphonesTest: "Headphones Test",
  siteName: "Setup Check",
  siteSubtitle: "Hardware Check",
  selectScenario: "Select Testing Scenario",
  onlineMeeting: "Online Meeting Check-up",
  onlineMeetingDesc: "Test your microphone, speakers, and camera for video calls",
  startTest: "Start Test",
  microphoneTest: "Microphone Test",
  speakerTest: "Speaker Test",
  cameraTest: "Camera Test",
  next: "Next",
  back: "Back",
  finish: "Finish",
  
  // ToolsPage 新增翻译键
  deviceTestingTools: "Device Testing Tools",
  deviceTestingToolsDescription: "Comprehensive collection of hardware testing tools to ensure your devices work perfectly for any scenario.",
  testingTools: "Testing Tools",
  hardwareTesting: "Hardware Testing",
  deviceTools: "Device Tools",
  audioQuality: "Audio Quality",
  noiseLevel: "Noise Level",
  sensitivity: "Sensitivity",
  stereoBalance: "Stereo Balance",
  volumeLevel: "Volume Level",
  audioOutput: "Audio Output",
  keyResponse: "Key Response",
  keyMapping: "Key Mapping",
  typingSpeed: "Typing Speed",
  clickAccuracy: "Click Accuracy",
  scrollFunction: "Scroll Function",
  internetSpeed: "Internet Speed",
  connectionStability: "Connection Stability",
  quickTest: "Quick Test",
  needHelp: "Need Help?",
  testingToolsDescription: "Our testing tools are designed to be simple and accurate. Each tool provides detailed feedback to help you optimize your hardware setup.",
  noRegistration: "No Registration",
  accuracy: "Accuracy",
  free: "Free",
  
  // 麦克风测试
  micTestTitle: "Test Your Microphone",
  micTestDesc: "Speak into your microphone. You should see the audio levels respond.",
  selectMicrophone: "Select Microphone",
  microphoneWorking: "Microphone is working!",
  microphoneNotDetected: "No microphone detected",
  
  // 扬声器测试
  speakerTestTitle: "Test Your Speakers",
  speakerTestDesc: "Click the button below to play a test sound.",
  playTestSound: "Play Test Sound",
  canYouHear: "Can you hear the test sound?",
  
  // 摄像头测试
  cameraTestTitle: "Test Your Camera",
  cameraTestDesc: "Your camera feed should appear below.",
  selectCamera: "Select Camera",
  cameraWorking: "Camera is working!",
  cameraNotDetected: "No camera detected",
  cameraTestModule: "Camera Test Module",
  smartScorecard: "Smart Scorecard",
  startTestToSeeScore: "Start test to see score",
  resolution: "Resolution",
  frameRate: "Frame Rate",
  colorBrightness: "Color & Brightness",
  overallRating: "Overall Rating",
  takePhoto: "Take Photo",
  mirror: "Mirror",
  fullscreen: "Fullscreen",
  downloadPhoto: "Download Photo",
  deletePhoto: "Delete Photo",
  photoPreview: "Photo Preview",
  cameraCapture: "Camera Capture",
  deviceName: "Device",
  realTimeInfo: "Real-time Info",
  
  // 键盘测试
  keyboardTestTitle: "Keyboard Test",
  keyboardTestDesc: "Press any key on your keyboard to test it",
  pressAnyKey: "Press any key to start testing...",
  keyPressed: "Key pressed:",
  
  // 鼠标测试
  mouseTestTitle: "Mouse Test",
  mouseTestDesc: "Test your mouse buttons and scroll wheel",
  leftClick: "Left Click",
  rightClick: "Right Click",
  middleClick: "Middle Click/Scroll",
  scrollUp: "Scroll Up",
  scrollDown: "Scroll Down",
  clickButtons: "Click the buttons above or use your mouse",
  
  // 测试结果
  testComplete: "Testing Complete!",
  allTestsPassed: "All tests completed successfully",
  copyReport: "Copy Report to Clipboard",

  
  // 页面内容
  gamingSetup: "Gaming Setup Check",
  gamingSetupDesc: "Test your peripherals for optimal gaming experience",
  audioTest: "Audio Test",
  recommended: "Recommended",
  comingSoon: "Coming Soon",
  individualDeviceTests: "Individual Device Tests",
  stepOf: "Step {current} of {total}",
  invalidScenario: "Invalid Scenario",
  runTestsAgain: "Run Tests Again",
  
  // 测试组件
  cameraTestTitle2: "Camera Test",
  cameraTestDesc2: "Test your camera to ensure others can see you clearly during your meeting.",
  selectCamera2: "Select Camera",
  cameraPreview: "Camera Preview",
  cameraPlaceholder: "Camera preview will appear here",
  startCameraTest: "Start Camera Test",
  stopCamera: "Stop Camera",
  cameraWorking2: "✅ Camera Working!",
  cameraWorkingNormally: "Camera Working Normally",
  cameraStartedSuccessfully: "Camera started successfully",
  analyzingVideoQuality: "Analyzing video quality",
  cameraWorkingDesc: "Your camera is working properly. Make sure you're well-lit and centered in the frame.",
  cameraTips: "💡 Camera Tips:",
  cameraTip1: "• Ensure good lighting in front of you",
  cameraTip2: "• Position camera at eye level",
  cameraTip3: "• Clean your camera lens for clearer image",
  cameraTip4: "• Check for stable internet connection",
  backSpeakerTest: "Back: Speaker Test",
  finishTesting: "Finish Testing",
  
  keyboardTestTitle2: "Keyboard Test",
  keyboardTestDesc2: "Press any key to test it. Keys will light up when pressed.",
  resetTest: "Reset Test",
  backToHome: "Back to Home",
  recentKeyPresses: "Recent Key Presses",
  space: "Space",
  testingTips: "💡 Testing Tips:",
  keyboardTip1: "• Press different types of keys",
  keyboardTip2: "• Test special keys like Shift, Ctrl, Alt",
  keyboardTip3: "• Check if all keys respond properly",
  keyboardTip4: "• Try typing some text to ensure smooth operation",
  
  micTestTitle2: "Microphone Test",
  micTestDesc2: "Test your microphone to ensure others can hear you clearly during your meeting.",
  audioLevel: "Audio Level",
  micInstructions: "Speak into your microphone to see the audio level",
  micInstructionsStart: "Click 'Start Test' to begin",
  startMicTest: "Start Microphone Test",
  stopTest: "Stop Test",
  nextSpeakerTest: "Next: Speaker Test",
  
  // 鼠标测试
  mouseTestTitle2: "Mouse Test",
  mouseTestDesc2: "Move your mouse and click buttons in the test area below.",
  testArea: "Test Area",
  moveClickScroll: "Move, Click & Scroll",
  tryMouseButtons: "Try left, right, and middle mouse buttons",
  position: "Position:",
  buttonStatus: "Button Status",
  leftButton: "Left",
  wheelButton: "Wheel",
  middleButton: "Middle", 
  rightButton: "Right",
  sideButton1: "Side 1",
  sideButton2: "Side 2",
  pressed: "Pressed",
  released: "Released",
  eventHistory: "Event History",
  noMouseEvents: "No mouse events yet",
  startMovingClicking: "Start moving and clicking to see events",
  scrollDown2: "Scroll Down",
  scrollUp2: "Scroll Up",
  mouseTip1: "• Test all mouse buttons (left, right, middle)",
  mouseTip2: "• Check scroll wheel in both directions",
  mouseTip3: "• Verify mouse movement tracking",
  mouseTip4: "• Test click and drag functionality",
  mouseTip5: "• Ensure cursor moves smoothly",
  
  // 扬声器测试
  speakerTestTitle2: "Speaker Test",
  speakerTestDesc2: "Test your speakers or headphones to ensure you can hear audio during your meeting.",
  volume: "Volume:",
  testAudioPlayback: "Test Audio Playback",
  testAudioDesc: "Click the button below to play a test tone. You should hear a clear sound from your speakers or headphones.",
  playingTestSound: "Playing Test Sound...",
  playTestSound2: "Play Test Sound",
  playingTone: "🔊 Playing test tone (440 Hz)",
  troubleshootingTips: "💡 Troubleshooting Tips:",
  speakerTip1: "• Check if speakers/headphones are connected",
  speakerTip2: "• Adjust system volume if needed",
  speakerTip3: "• Try different audio output devices",
  speakerTip4: "• Ensure audio drivers are up to date",
  backMicrophone: "Back: Microphone",
  nextCameraTest: "Next: Camera Test",
  
  // Headphones & Speaker Test
  headphonesTestTitle: "Online Headphones & Speaker Testing",
  headphonesTestDesc: "Comprehensive audio testing for optimal listening experience",
  outputDeviceSelector: "Select Output Device",
  noOutputDevices: "No audio output devices found",
  leftRightChannelTest: "Left-Right Channel Test",
  leftRightChannelDesc: "Quickly diagnose channel reversal or single-sided audio issues",
  playLeft: "▶ Play Left Channel",
  playRight: "▶ Play Right Channel", 
  playingLeft: "Playing Left...",
  playingRight: "Playing Right...",
  frequencyResponseTest: "Frequency Response Test",
  frequencyResponseDesc: "Understand your headphones' bass and treble performance",
  startSweep: "Start Sweep",
  sweepInProgress: "Sweep in Progress",
  frequencyTestTip: "Notice at which frequencies the sound becomes weak or disappears",
  dynamicRangeTest: "Dynamic Range Test",
  dynamicRangeDesc: "Test your headphones' ability to reproduce details",
  dynamicRangeTestTip: "Can you hear both quiet background details and loud foreground sounds?",
  stereoImagingTest: "Stereo Imaging & Positioning Test",
  stereoImagingDesc: "Experience immersive 3D audio positioning",
  play3dAudio: "Play 3D Audio",
  audioPlaying3d: "3D Audio Playing",
  stereoImagingTestTip: "Listen as the sound moves around your head in 3D space",
  stopAllAudio: "Stop All Audio",
  stopPlaying: "Stop Playing",
  audioError: "Audio Error",
  stopSweep: "Stop Sweep",
  stopTestButton: "Stop Test",
  startTestButton: "Start Test",
  testInstructions: "🎵 Test Instructions",
  testInstructionItem1: "• During the sweep, notice which frequency ranges become weak or disappear",
  testInstructionItem2: "• Quality headphones should maintain balanced performance across the entire frequency range",
  testInstructionItem3: "• Excessive bass may mask midrange, overly bright treble may be harsh",
  testInstructionItem4: "• Testing in a quiet environment is recommended for accurate results",
  testInstructionItem5: "• Real-time frequency display and spectrum help you understand the current test frequency",
  dynamicRangeTestInProgress: "Dynamic Range Test in Progress",
  quietSound: "Quiet",
  loudSound: "Loud",
  bassDetail: "Bass Detail",
  backgroundEffects: "Background Effects",
  midrangeLayer: "Midrange Layer",
  voiceDialogue: "Voice Dialogue",
  trebleImpact: "Treble Impact",
  explosionEffects: "Explosion Effects",
  frontDirection: "Front",
  backDirection: "Back",
  leftDirection: "Left",
  rightDirection: "Right",
  leftChannel: "Left Channel",
  rightChannel: "Right Channel",
  center: "Center",
  ultraLowBass: "Ultra Low Bass",
  lowBass: "Low Bass",
  midLowBass: "Mid-Low Bass",
  midrange: "Midrange",
  midTreble: "Mid-Treble",
  treble: "Treble",
  ultraTreble: "Ultra Treble",
  bassDrumBass: "Bass Drum, Bass",
  voiceBase: "Voice Base",
  voiceClarity: "Voice Clarity",
  detailAiriness: "Detail, Airiness",
  testInProgress: "Test in Progress",
  testProgress: "Test Progress",
  leftChannelTest: "Left Channel Test",
  rightChannelTest: "Right Channel Test",
  frequencyTest: "Frequency Test",
  stereoTest: "Stereo Test",
  basicTestRequired: "Please complete basic left-right channel tests first",
  basicTestCompleted: "Basic tests completed",
  
  // 总结和报告
  testResultsSummary: "✅ Test Results Summary",
  microphoneTestResult: "Microphone Test:",
  speakerTestResult: "Speaker Test:",
  cameraTestResult: "Camera Test:",
  testPassed: "Passed",
  deviceTestReport: "Device Testing Report",
  generated: "Generated:",
  scenario: "Scenario:",
  browserInfo: "Browser Information:",
  testsCompleted: "Tests Completed:",
  allHardwareTestsComplete: "All hardware tests completed successfully.",
  deviceReadyForMeetings: "Your device is ready for online meetings!",
  copyFailed: "Copy Failed",
  copyFailedDesc: "Unable to copy to clipboard. Please try again.",
  
  // UI 组件
  close: "Close",
  previousButton: "Previous",
  nextButton: "Next",
  more: "More",
  previousSlide: "Previous slide",
  nextSlide: "Next slide",
  morePages: "More pages",
  toggleSidebar: "Toggle Sidebar",
  
  // 404 页面
  pageNotFound: "404",
  oopsPageNotFound: "Oops! Page not found",
  returnToHome: "Return to Home",
  
  // 麦克风模块特定
  microphoneAccess: "Microphone Access",
  initializingMicrophone: "Initializing microphone...",
  micAccessDenied: "Microphone Access Denied",
  micAccessDeniedDesc: "Microphone permission is required for testing. Please allow microphone access in your browser settings.",
  howToEnableMic: "💡 How to enable microphone permissions:",
  micPermissionStep1: "• Click the lock icon on the left side of the address bar",
  micPermissionStep2: "• Select \"Allow\" for microphone permission",
  micPermissionStep3: "• Refresh the page to try again",
  micPermissionStep4: "• Or manually enable microphone permission in browser settings",
  retry: "Retry",
  readyToTest: "Ready to test your microphone",
  readyToTestDesc: "We will test your microphone quality, volume levels and real-time response performance to ensure your voice is clearly transmitted during online meetings.",
  testContent: "🎯 Test Content:",
  testContentItem1: "• Microphone device detection and permission acquisition",
  testContentItem2: "• Real-time audio capture and volume monitoring",
  testContentItem3: "• Audio quality and clarity assessment",
  testContentItem4: "• Environmental noise detection and recommendations",
  testContentItem5: "• Device compatibility verification",
  startMicrophoneTest: "Start Microphone Test",
  
  // 麦克风测试界面
  audioVisualization: "Audio Visualization",
  realTimeWaveform: "🎵 Real-time audio waveform - you'll see fluctuations when speaking",
  startSpeaking: "Please start speaking...",
  usageTip: "💡 Usage Tip: Speak into the microphone, the waveform above will change in real-time based on your voice. The more active the waveform, the better the microphone is working.",
  microphoneStatus: "Microphone Status",
  micConnected: "🎤 Microphone connected",
  speakIntoMic: "Please speak into the microphone to test audio input effect",
  deviceWorking: "Device working normally",
  volumeDetection: "Volume Detection",
  lowLevel: "Low",
  goodLevel: "Good",
  overload: "Overload!",
  dbLow: "-60dB Low",
  dbOverload: "0dB Overload",
  goodRange: "-25dB ~ -8dB Good range",
  
  // 实时监听
  realTimeMonitoring: "Real-time Monitoring (Hear Yourself)",
  monitoring: "Monitoring",
  closed: "Closed",
  monitoringDesc: "When enabled, you can hear yourself in real-time, helping to adjust volume and sound quality. Recommended to use with headphones.",
  warningHeadphones: "⚠️ Recommend using headphones with this feature",
  preventFeedback: "To prevent feedback, please ensure you're using headphones or turn down speaker volume",
  audioDelay: "Audio Delay",
  avoidOverlap: "Avoid sound overlap",
  monitoringVolume: "Monitoring Volume",
  notAffectRecording: "Does not affect recording",
  speaker: "Speaker",
  monitoringEnabled: "Real-time monitoring enabled",
  adjustSettings: "You can adjust the above settings in real-time",
  
  // 录音测试
  recordingTest: "Recording Test",
  startRecording: "Start Recording",
  stopRecording: "Stop Recording",
  stopPlayback: "Stop Playback",
  playRecording: "Play Recording",
  recording: "Recording",
  playing: "Playing",
  playbackProgress: "Playback Progress",
  playbackComplete: "Playback complete",
  paused: "Paused",
  recordingInstructions: "💡 Instructions: Click \"Start Recording\", then speak for a while, stop and click \"Play Recording\" to listen to the effect. This confirms the microphone is working properly.",
  advancedTest: "🔧 Advanced Test: You can adjust the audio processing settings above before recording, then record to compare the sound quality differences under different settings.",
  
  // 设备信息
  deviceInfo: "Device Information",
  sampleRate: "Sample Rate:",
  channels: "Channels:",
  mono: "Mono",
  stereo: "Stereo",
  bitDepth: "Bit Depth:",
  
  // 高级设置
  advancedSettings: "Advanced Settings",
  whenToAdjust: "🎯 When do you need to adjust these settings?",
  echoCancellation: "Echo Cancellation",
  echoCancellationDesc: "When the other party hears their own voice echo during meetings → Try enabling this feature",
  noiseSuppression: "Noise Suppression",
  noiseSuppressionDesc: "Background noise is too loud affecting call quality → Enable to reduce keyboard, fan and other noise",
  autoGainControl: "Auto Gain Control",
  autoGainControlDesc: "Voice volume is unstable, sometimes loud sometimes quiet → Enable to automatically adjust volume levels",
  realTimeEffect: "⚡ Real-time Effect: Settings will be applied immediately after toggling switches, you can record to compare before and after effects",
  enabled: "Enabled",
  disabled: "Disabled",
  preventEcho: "Prevent the other party from hearing your own voice echo, suitable for speaker scenarios",
  mayEcho: "⚠️ May cause echo when disabled, recommend using headphones",
  filterNoise: "Intelligently filter keyboard sounds, fan sounds, air conditioning sounds and other background noise",
  improveQuality: "✅ Can significantly improve call quality when enabled",
  autoAdjustVolume: "Automatically adjust volume intensity to prevent voice from being too loud or too quiet affecting listening experience",
  naturalVariation: "💡 When disabled, you can hear natural volume variations",
  
  // 测试建议
  testSuggestions: "💡 Test Suggestions",
  defaultFirst: "First record with default settings as a baseline reference",
  compareSettings: "Toggle different settings one by one, recording each time to compare effect differences",
  quietEnvironment: "Turn off \"Noise Suppression\" in quiet environments, turn on in noisy environments",
  speakerEcho: "Turn on \"Echo Cancellation\" when using speakers, can turn off when using headphones",

  backToHomeButton: "Back to Home",
  nextSpeakerTestButton: "Next: Speaker Test",
  
  // Double-click detection feature
  startDoubleClickDetection: "Start Double-Click Detection",
  stopMonitoring: "Stop Monitoring",
  doubleClickDetectionInProgress: "Double-Click Detection in Progress",
  duration: "Duration:",
  detectedSevereHardwareFailure: "Severe Hardware Failure Detected",
  mouseMayHaveIssues: "Mouse May Have Issues",
  sporadicIssuesContinueMonitoring: "Sporadic Issues, Continue Monitoring",
  mouseWorkingNormally: "Mouse Working Normally",
  detected: "Detected",
  issues: "issues",
  performSingleClickTest: "Perform Single-Click Test",
  tryNormalSingleClickOperations: "Try normal single-click operations",
  systemWillAutoDetectDoubleClickIssues: "System will automatically detect unexpected double-click issues",
  doubleClickIssueDetection: "Double-Click Issue Detection",
  noDoubleClickIssuesDetected: "No double-click issues detected",
  continueWithSingleClickTesting: "Continue with single-click testing",
  severeHardwareFailureShortInterval: "Severe Hardware Failure - Extremely Short Interval Double-Click",
  possibleHardwareFailureUnexpectedDoubleClick: "Possible Hardware Failure - Unexpected Double-Click",
  potentialIssueFastDoubleClick: "Potential Issue - Fast Double-Click",
  interval: "Interval:",
  normal: "Normal:",
  doubleClickDetectionInstructions: "Double-Click Detection Instructions",
  doubleClickTip1: "Perform normal single-click operations, the system will automatically detect unexpected double-clicks",
  doubleClickTip2: "Red warning: Interval <20ms, severe hardware failure",
  doubleClickTip3: "Yellow warning: Interval <50ms, possible hardware failure",
  doubleClickTip4: "Blue notice: Interval is short but within normal range",
  doubleClickTip5: "Timer above shows milliseconds which helps diagnose issue severity",
  doubleClickTip6: "After detection, you can proceed with normal single-click testing to verify mouse functionality",
  
  // TestWorkflowPage 专用翻译键
  networkQualityTest: "Network Quality Test",
  notTested: "Not Tested",
  failed: "Failed",
  failureReason: "Failure Reason",
  onlineMeetingDeviceReport: "Online Meeting Device Test Report",
  generatedTime: "Generated Time",
  testScenario: "Test Scenario",
  onlineMeetingScenario: "Online Meeting Scenario",
  unknownScenario: "Unknown Scenario",
  testCompletionStatus: "Test Completion Status",
  testCompletionCount: "tests completed",
  testResultsTitle: "Test Results",
  allHardwareTestsPassed: "All hardware tests passed! Your device is ready for online meetings.",
  testsPassed: "tests passed",
  partialTestsCompleted: "Some tests incomplete, recommend completing all test items.",
  checkFailedTests: "Recommend checking failed test items.",
  suggestions: "Suggestions",
  suggestion1: "1. Ensure stable network connection",
  suggestion2: "2. Position camera at eye level",
  suggestion3: "3. Maintain good lighting conditions",
  suggestion4: "4. Use headphones for better audio experience",
  retestFailedDevices: "5. For failed devices, recommend retesting or checking hardware connections",
  reportCopied: "Report Copied!",
  testResults: "Test Results",
  testsCompletedCount: "{completed}/{total} tests completed",
  meetingDeviceTestReport: "Meeting Device Test Report",
  reason: "Reason",
  allDevicesReady: "All devices working properly, ready to start meeting!",
  someTestsFailed: "Some tests failed, recommend checking related devices.",
  completeAllTests: "Please complete all tests for comprehensive device status evaluation.",
  
  // Network test features
  networkTestDesc: "Test your network connection quality to ensure stability during meetings",
  networkQualityTestDesc: "Test your network connection quality to ensure stability during meetings",
  testingNetworkLatency: "Testing network latency, download and upload speeds...",
  networkQualityAssessment: "Network Quality Assessment",
  excellent: "Excellent",
  good: "Good",
  fair: "Fair",
  poor: "Poor",
  latency: "Latency",
  download: "Download",
  upload: "Upload",
  jitter: "Jitter",
  recommendations: "Recommendations",
  networkExcellentDesc: "Excellent network quality! You can enjoy HD video calls and smooth screen sharing.",
  networkGoodDesc: "Good network quality, suitable for video conferencing. Consider closing other bandwidth-consuming apps.",
  networkFairDesc: "Fair network quality, consider lowering video quality or turning off video to ensure audio stability.",
  networkPoorDesc: "Poor network quality, recommend checking network connection or contacting network administrator.",
  serverIP: "Server IP",
  timezone: "Timezone",
  userLocation: "Your Location",
  serverLocation: "Test Server Location",
  distance: "Distance",
  approximateDistance: "Approximate Distance",
  gpsLocation: "GPS Location",
  ipLocation: "IP Location",
  timezoneLocation: "Timezone Inference",
  unknownLocation: "Unknown Source",
  loadingLocation: "Loading location information...",
  server: "Server",
  
  // Enhanced network test features
  testingLatency: "Testing Latency",
  testingDownload: "Testing Download Speed",
  testingUpload: "Testing Upload Speed",
  testingPacketLoss: "Testing Packet Loss",
  testCompleted: "Test Completed",
  preparingTest: "Preparing Test",
  aimScores: "AIM Usage Scenario Scores",
  gaming: "Gaming",
  streaming: "Streaming",
  realTimeCommunication: "Real-time Communication",
  loadedMetrics: "Loaded Network Metrics",
  loadedLatency: "Loaded Latency",
  loadedJitter: "Loaded Jitter",
  
  startNetworkTest: "Start Network Test",
  retestNetwork: "Retest Network",
  networkOptimizationTips: "Network Optimization Tips",
  networkTip1: "• Use wired connection instead of WiFi for more stable network",
  networkTip2: "• Close other bandwidth-consuming applications",
  networkTip3: "• Ensure router is close and signal is good",
  networkTip4: "• Avoid important meetings during network peak hours",
  testRegion: "Test Region",
  nextMicrophoneTest: "Next: Microphone Test",
  
  // Enhanced speaker test
  enhancedSpeakerTest: "Enhanced Speaker Test",
  comprehensiveAudioTest: "Comprehensive test of your audio output device",
  volumeControl: "Volume Control",
  bothChannels: "Both Channels",
  testLeftRightChannels: "Test if left and right channels work properly",
  confirmStereoTestPassed: "Confirm stereo test passed",
  frequencySweep: "Frequency Sweep",
  sweeping: "Sweeping...",
  testTone1kHz: "1kHz Test Tone",
  testSpeakerFrequencyRange: "Test speaker frequency response range",
  confirmFrequencyTestPassed: "Confirm frequency test passed",
  speakerTestComplete: "✅ Speaker Test Complete",
  allAudioTestsPassed: "All audio tests passed, your speakers are working properly!",
  audioOptimizationTips: "Audio Optimization Tips",
  audioTip1: "• Using headphones can provide better audio experience",
  audioTip2: "• Adjust system volume to appropriate level",
  audioTip3: "• Ensure audio drivers are up to date",
  audioTip4: "• Conduct meetings in quiet environment for best effect",
  
  // Enhanced camera test
  enhancedCameraTest: "Enhanced Camera Test",
  comprehensiveVideoTest: "Comprehensive test of your video input device",
  cameraPermissionDeniedDesc: "Camera permission is required for testing. Please allow camera access in your browser settings.",
  howToEnableCamera: "💡 How to enable camera permissions:",
  cameraPermissionStep1: "• Click the lock icon on the left side of the address bar",
  cameraPermissionStep2: "• Select \"Allow\" for camera permission",
  cameraPermissionStep3: "• Refresh the page to try again",
  cameraPermissionStep4: "• Or manually enable camera permission in browser settings",
  readyToTestCamera: "Ready to test your camera",
  readyToTestCameraDesc: "We will test your camera quality, resolution and real-time performance to ensure your image is clearly transmitted during online meetings.",
  cameraTestContent: "🎯 Test Content:",
  cameraTestContentItem1: "• Camera device detection and permission acquisition",
  cameraTestContentItem2: "• Real-time video capture and quality monitoring",
  cameraTestContentItem3: "• Video quality and clarity assessment",
  cameraTestContentItem4: "• Lighting condition detection and recommendations",
  cameraTestContentItem5: "• Device compatibility verification",
  cameraStatus: "Camera Status",
  cameraConnected: "📹 Camera Connected",
  lookIntoCameraTest: "Please look into the camera to test video input effect",
  videoQualityAnalysis: "Video Quality Analysis",
  brightness: "Brightness",
  contrast: "Contrast",
  sharpness: "Sharpness",
  videoQualityExcellentDesc: "Excellent video quality! Your camera settings are perfect for meeting use.",
  videoQualityGoodDesc: "Good video quality, suitable for most meeting scenarios.",
  videoQualityFairDesc: "Fair video quality, consider adjusting lighting or camera position.",
  videoQualityPoorDesc: "Poor video quality, recommend checking camera settings or replacing device.",
  videoOptimizationTips: "Video Optimization Tips",
  videoTip1: "• Ensure adequate lighting in front of you",
  videoTip2: "• Position camera at eye level",
  videoTip3: "• Clean camera lens for clearer image",
  videoTip4: "• Maintain stable network connection",
  completeTest: "Complete Test",
  
  // Enhanced microphone test
  enhancedMicrophoneTest: "Enhanced Microphone Test",
  comprehensiveMicTest: "Comprehensive test of your audio input device",
  audioQualityAnalysis: "Audio Quality Analysis",
  signalToNoiseRatio: "Signal-to-Noise Ratio:",
  backgroundNoiseLevel: "Background Noise Level:",
  distortionLevel: "Distortion Level:",
  echoDetection: "Echo Detection:",
  notDetected: "Not Detected",
  micExcellentDesc: "Excellent audio quality! Your microphone settings are perfect for meeting use.",
  micGoodDesc: "Good audio quality, suitable for most meeting scenarios.",
  micFairDesc: "Fair audio quality, consider adjusting microphone position or reducing background noise.",
  micPoorDesc: "Poor audio quality, recommend checking microphone settings or replacing device.",
  echoDetectedWarning: "⚠️ Echo detected, recommend using headphones or adjusting speaker volume.",
  micOptimizationTips: "Microphone Optimization Tips",
  micTip1: "• Keep microphone 15-20cm from your mouth",
  micTip2: "• Test in quiet environment",
  micTip3: "• Use headphones to avoid echo problems",
  micTip4: "• Adjust microphone volume to appropriate level",
  
  // Test failure reasons
  networkNotTested: "Network test not performed",
  latencyTooHigh: "Latency too high",
  downloadSpeedTooSlow: "Download speed too slow",
  uploadSpeedTooSlow: "Upload speed too slow",
  jitterTooHigh: "Network jitter too high",
  networkQualityPoor: "Poor network quality, not suitable for video conferencing",
  micPermissionDenied: "Microphone permission denied, please allow microphone access in browser settings",
  micPermissionNotGranted: "Microphone permission not granted",
  noMicrophoneDevices: "No available microphone devices detected",
  noAudioInput: "No audio input from microphone, please check device connection or volume settings",
  audioQualityIssues: "Audio quality issues",
  signalToNoiseRatioLow: "Signal-to-noise ratio too low",
  backgroundNoiseTooHigh: "Background noise too high",
  audioDistortionHigh: "High audio distortion",
  echoDetected: "Echo detected",
  audioQualityPoor: "Poor audio quality",
  requiredTestsNotCompleted: "Required test items not completed",
  stereoTestNotCompleted: "Stereo test not completed",
  frequencyTestNotCompleted: "Frequency response test not completed",
  cameraPermissionDenied: "Camera permission denied, please allow camera access in browser settings",
  noCameraDevices: "No available camera devices detected",
  cameraNotStarted: "Camera not started, please click 'Start Camera Test' button",
  cameraError: "Camera error",
  videoQualityIssues: "Video quality issues",
  lightingTooDark: "Lighting too dark",
  lightingTooBright: "Lighting too bright",
  contrastTooLow: "Contrast too low",
  imageBlurry: "Image blurry",
  videoQualityPoor: "Poor video quality",
  
  // Meeting device test complete
  meetingDeviceTestComplete: "Meeting Device Test Complete",
  
  // Gaming Setup Check
  gamingSetupCheckTitle: "Gaming Setup Check",
  gamingScenario: "Gaming Scenario", 
  gamingDeviceTestReport: "Gaming Device Test Report",
  gamingDeviceTestComplete: "Gaming Device Test Complete",
  allGamingTestsPassed: "All gaming tests passed, your setup is ready for gaming!",
  gamingNetworkTestDesc: "Test your network connection quality to ensure optimal gaming performance",
  gamingKeyboardTestDesc: "Test your keyboard for gaming responsiveness and functionality",
  gamingMouseTestDesc: "Test your mouse accuracy and responsiveness for gaming",
  gamingAudioTestDesc: "Test your audio output for immersive gaming experience",
  keyboardResponseTime: "Keyboard Response Time",
  mouseAccuracy: "Mouse Accuracy", 
  audioLatency: "Audio Latency",
  peripheralPerformance: "Peripheral Performance",
  allPeripheralsReady: "All peripherals are ready for gaming!",
  somePeripheralsFailed: "Some peripherals need attention. Check the failed tests above.",
  completeAllGamingTests: "Complete all tests to get your gaming setup report.",
  retestFailedPeripherals: "Consider retesting failed peripherals for better performance.",
  optimizeGamingSetup: "Gaming Setup Optimization Tips",
  gamingTip1: "• Use wired peripherals for lowest latency",
  gamingTip2: "• Keep mouse sensitivity at comfortable level", 
  gamingTip3: "• Test all keyboard keys for proper response",
  gamingTip4: "• Ensure audio has no delay or distortion",
  gamingTip5: "• Check network stability for online gaming",

  // 麦克风测试模块新增翻译键
  micPermissionDeniedTitle: "Microphone Access Denied",
  micPermissionDeniedMessage: "Please allow microphone access to test your audio input device.",
  enableMicPermissionInstructions: "💡 How to enable microphone permission:",
  enableMicStep1: "• Click the 🔒 icon in the address bar",
  enableMicStep2: "• Select \"Allow\" microphone permission",
  enableMicStep3: "• Refresh the page to retry",
  retryTest: "Retry",
  waitingConnection: "Waiting for connection",
  startTestingButton: "Click start test button",
  waitingTesting: "Waiting for test",
  deviceReady: "Device ready for testing",
  applyingNewSettings: "Applying new settings",
  settingsApplied: "Settings applied",
  applySettingsError: "Error applying new settings, please retry",
  realTimeAudioDelay: "Real-time audio delay",
  seconds: "seconds",
  preventSoundOverlap: "Prevent sound overlap",
  adjustAnytime: "Can adjust settings above anytime",
  playingInProgress: "Playing",
  recordingCompleted: "Recording completed",
  playbackStopped: "Playback stopped",
  microphoneInformation: "Microphone Information",
  gettingDeviceInfo: "Getting device information...",
  pleaseTurnOnTest: "Please start the test first to get device information",
  whyAdjustSettings: "🎯 When do you need to adjust these settings?",
  echoWhenUsingSpeaker: "When others hear their own voice echo during meetings → Try enabling this feature",
  noisyBackground: "Background noise affecting call quality → Enable to reduce keyboard, fan noise etc.",
  unstableVolume: "Unstable volume levels → Enable to automatically adjust volume",
  settingsApplyImmediately: "⚡ Real-time effect: Settings apply immediately when toggled, you can record to compare effects",
  toggleOn: "Enabled",
  toggleOff: "Disabled",
  useHeadphonesToPreventEcho: "⚠️ May cause echo when disabled, recommend using headphones",
  improveCallQuality: "✅ Significantly improves call quality when enabled",
  hearNaturalVolumeChanges: "💡 You can hear natural volume changes when disabled",
  testSuggestionsTitle: "💡 Test Suggestions",
  defaultSettingsFirst: "First record with default settings as a reference",
  compareEachSetting: "Toggle different settings one by one, record each time to compare effects",
  noiseSuppressionTip: "Turn off \"Noise Suppression\" in quiet environments, turn on in noisy environments",
  echoCancellationTip: "Turn on \"Echo Cancellation\" when using speakers, can turn off when using headphones",
  returnButton: "Return",

  // SEO相关翻译
  siteDescription: "Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality. Perfect for online meetings and gaming setups.",
  siteKeywords: "device test, camera test, microphone test, speaker test, keyboard test, mouse test, network test, online meeting, hardware check, gaming setup",
  
  // 页面SEO描述
  homePageDescription: "Test your devices online - camera, microphone, speakers, keyboard, mouse, and network. Free hardware compatibility check for online meetings and gaming.",
  toolsPageDescription: "Comprehensive collection of professional device testing tools. Test cameras, microphones, speakers, keyboards, mice, and network quality online for free.",
  cameraTestDescription: "Test your camera quality, resolution, and performance online. Check camera compatibility for video calls and streaming.",
  microphoneTestDescription: "Test your microphone audio quality, noise levels, and clarity. Ensure perfect audio for online meetings and gaming.",
  headphonesTestDescription: "Test your headphones and speakers audio quality, stereo balance, and volume levels. Check audio compatibility.",
  keyboardTestDescription: "Test all keyboard keys functionality, response time, and key mapping. Ensure optimal typing and gaming performance.",
  mouseTestDescription: "Test mouse accuracy, click responsiveness, scroll functionality, and precision. Optimize for productivity and gaming.",
  networkTestDescription: "Test your internet speed, latency, and connection stability. Check network quality for online meetings and gaming.",
  meetingTestDescription: "Complete device compatibility check for online meetings. Test camera, microphone, speakers, and network quality.",
  gamingTestDescription: "Complete gaming setup check. Test keyboard, mouse, headphones, microphone, and network performance for optimal gaming experience.",

  // Streaming & Content Creation Scenario
  streamingScenario: "Live Streaming & Content Creation",
  streamingScenarioDesc: "Professional-grade testing for streamers, content creators, and online educators",
  streamingSetupCheckTitle: "Live Streaming & Content Creation Setup",
  streamingDeviceTestReport: "Live Streaming & Content Creation Device Report",
  streamingDeviceTestComplete: "Streaming Device Test Complete",
  allStreamingTestsPassed: "All streaming equipment tests passed! Your setup is ready for professional content creation.",
  streamingOptimizationTips: "Streaming Optimization Tips",
  streamingTip1: "• Ensure stable high-speed internet (minimum 5 Mbps upload for 1080p streaming)",
  streamingTip2: "• Use a dedicated microphone for better audio quality",
  streamingTip3: "• Optimize lighting and camera positioning for professional appearance",
  streamingTip4: "• Test audio levels and eliminate background noise",
  streamingTip5: "• Consider using headphones to monitor audio quality",
  streamingTestDescription: "Complete streaming setup check. Test camera, microphone, headphones, and network performance for high-quality content creation.",

  // Device Diagnostic Scenario
  diagnosticScenario: "Complete Device Diagnostic",
  diagnosticScenarioDesc: "Comprehensive hardware scan and troubleshooting for all connected devices",
  diagnosticSetupCheckTitle: "Complete Device Diagnostic",
  diagnosticDeviceTestReport: "Complete Device Diagnostic Report",
  diagnosticDeviceTestComplete: "Device Diagnostic Complete",
  allDiagnosticTestsPassed: "All hardware components are functioning properly. No issues detected.",
  diagnosticTroubleshootingTips: "Troubleshooting Recommendations",
  diagnosticTip1: "• Check device drivers and update if necessary",
  diagnosticTip2: "• Verify all hardware connections are secure",
  diagnosticTip3: "• Test devices with other applications to isolate issues",
  diagnosticTip4: "• Restart devices and browser if problems persist",
  diagnosticTip5: "• Contact technical support for persistent hardware failures",
  diagnosticTestDescription: "Complete hardware diagnostic scan. Test all devices including camera, microphone, headphones, keyboard, mouse, and network for comprehensive system evaluation.",

  // Error Messages - Camera
  cameraAccessDenied: "Camera access denied. Please allow camera permissions.",
  cameraNotFound: "No camera found. Please connect a camera.",
  cameraAccessFailed: "Failed to access camera",

  // Error Messages - Microphone
  microphoneAccessDenied: "Microphone permission denied. Please allow microphone access and try again.",
  microphoneAccessFailed: "Failed to access microphone. Please check your device and try again.",
  recordingStartFailed: "Failed to start recording",

  // SEO Validation Messages
  seoTitleMissing: "Missing page title",
  seoTitleTooLong: "Page title too long (recommended under 60 characters)",
  seoTitleTooShort: "Page title too short (recommended over 10 characters)",
  seoDescriptionMissing: "Missing page description",
  seoDescriptionTooLong: "Page description too long (recommended under 160 characters)",
  seoDescriptionTooShort: "Page description too short (recommended over 50 characters)",

  // Default SEO Configuration
  defaultSEOTitle: "Device Testing Platform",
  defaultSEODescription: "Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality.",

  // Console Messages
  setLanguageWarning: "setLanguage called from LanguageProvider. Use useLanguageNavigation hook instead.",
  invalidLanguageCode: "Invalid language code",

  // Scenario Page Labels
  includedTests: "Included Tests:",
  completeHardwareCheck: "Complete hardware compatibility check",
  needIndividualTesting: "Need Individual Tool Testing?",
  individualTestingDesc: "If you only need to test specific hardware components, you can access individual testing tools directly.",
  browseIndividualTools: "Browse Individual Tools",
  selectScenarioDesc: "Choose the testing scenario that best fits your needs. Each scenario includes a curated set of tests designed for specific use cases.",

  // Scenario Durations
  duration5to8: "5-8 minutes",
  duration8to12: "8-12 minutes",
  duration6to10: "6-10 minutes",
  duration10to15: "10-15 minutes",

  // Difficulty Levels
  difficultyBeginner: "Beginner",
  difficultyAdvanced: "Advanced",
  difficultyIntermediate: "Intermediate",
  difficultyComprehensive: "Comprehensive",

  // Usage Descriptions
  usageMeeting: "Perfect for video calls, remote work, and online meetings",
  usageGaming: "Optimized for gaming performance and competitive play",
  usageStreaming: "Optimized for high-quality streaming and content creation",
  usageDiagnostic: "Complete system check for troubleshooting and device validation",

  // Keyboard Test Module
  keyboardTestInstructions: "Test Instructions",
  keyboardTestInstruction1: "• After clicking start test, please press commonly used gaming keys",
  keyboardTestInstruction2: "• Recommended to test: WASD, Space, Shift, Ctrl, Q, E, etc.",
  keyboardTestInstruction3: "• We will measure key response time and coverage",
  keyboardTestInstruction4: "• Test at least 10 key presses to complete",
  startKeyboardTest: "Start Keyboard Test",
  keyboardTestingActive: "Testing keyboard...",
  keyboardTestStopped: "Keyboard test stopped",
  keyPressCount: "Key Presses",
  averageLatency: "Average Latency",
  keyCoverage: "Key Coverage",
  keyTestStatus: "Key Test Status",
  recentKeys: "Recent Keys (Latency)",
  stopKeyboardTest: "Stop Test",
  continueKeyboardTest: "Continue Test",
  resetKeyboardTest: "Reset",
  keyboardTestComplete: "Keyboard test completed!",
  keyboardTestCompleteDesc: "You can continue testing more keys or proceed to the next step",
  insufficientKeyCoverage: "Insufficient key coverage - please test more gaming keys",
  highInputLatency: "High input latency detected",
  notEnoughKeyPresses: "Not enough key presses - please test more keys",

  // Keyboard Latency Test (KeyboardTest.tsx)
  keyboardLatencyTest: "Keyboard Latency Test",
  testKeyboardResponseTime: "Test your keyboard response time and input lag",
  latencyShortestLatency: "Shortest Latency",
  latencyAverageLatency: "Average Latency",
  latencyScanRate: "Scan Rate",
  latencyConnection: "Connection",
  latencyNoData: "No data",
  latencyPresses: "presses",
  latencyMaxLatency: "Max",
  latencyHistory: "Latency History",
  latencyTestInstructions: "Latency Test Instructions",
  latencyInstruction1: "• Press and release keys to measure response time",
  latencyInstruction2: "• Shorter latency = better performance",
  latencyInstruction3: "• Scan rate is calculated from shortest key press time",
  latencyInstruction4: "• Gaming keyboards typically have <5ms latency",
  latencyInstruction5: "• Regular keyboards average 10-15ms latency",
  latencyPerformanceAssessment: "Performance Assessment",
  latencyOverallRating: "Overall Rating",
  latencyRecommendation: "Recommendation",
  latencyKeyCharacteristics: "Key Characteristics",
  latencyEstimatedPollingRate: "Estimated polling rate: ~1000Hz",
  latencyConnectionType: "Connection type",
  latencyResponseConsistency: "Response consistency",
  latencyVeryConsistent: "Very consistent",
  latencyVariable: "Variable",
  latencyExcellent: "Excellent",
  latencyVeryGood: "Very Good",
  latencyGood: "Good",
  latencyAverage: "Average",
  latencyPoor: "Poor",
  latencyExcellentForGaming: "Excellent for gaming and professional use",
  latencyGoodForGeneral: "Good for general use, suitable for casual gaming",
  latencyConsiderUpgrading: "Consider upgrading for better performance",
  latencyUsbEstimated: "USB (Estimated)",
  latencyUnknown: "Unknown",

  // Privacy Policy and Cookie Policy
  privacyPolicy: "Privacy Policy",
  cookiePolicy: "Cookie Policy",
  privacyPolicyTitle: "Privacy Policy",
  cookiePolicyTitle: "Cookie Policy",
  lastUpdated: "Last Updated",
  effectiveDate: "Effective Date",
  contactUs: "Contact Us",

  // Cookie Consent
  cookieConsentTitle: "Cookie Settings",
  cookieConsentDescription: "We use cookies to improve your browsing experience, analyze site traffic, and personalize content. You can choose to accept all cookies or customize your preferences.",
  cookieSettingsTitle: "Cookie Settings",
  cookieSettingsDescription: "We use different types of cookies to optimize your experience. You can choose to enable or disable each category, but please note that some functionality may be affected.",
  acceptAll: "Accept All",
  rejectAll: "Reject All",
  customize: "Customize",
  saveSettings: "Save Settings",
  required: "Required",

  // Cookie Categories
  cookieNecessaryTitle: "Necessary Cookies",
  cookieNecessaryDesc: "These cookies are essential for the website to function properly and cannot be disabled. They are usually only set in response to actions made by you, such as setting privacy preferences, logging in, or filling in forms.",
  cookieAnalyticsTitle: "Analytics Cookies",
  cookieAnalyticsDesc: "These cookies help us understand how visitors interact with our website by collecting and reporting anonymous information. This helps us improve website performance and user experience.",
  cookieMarketingTitle: "Marketing Cookies",
  cookieMarketingDesc: "These cookies are used to track visitors across websites with the intention of displaying relevant and personalized advertisements.",
  cookiePreferencesTitle: "Preference Cookies",
  cookiePreferencesDesc: "These cookies enable the website to remember choices you make (such as your username, language, or region) and provide enhanced, more personalized features.",

  // Privacy Policy Content
  privacyIntroduction: "Setup Check (\"we\", \"our\", or \"Company\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and protect your information when you use our device testing services.",
  dataCollectionTitle: "Information We Collect",
  dataCollectionContent: "We collect the following types of information:\n\n• **Device Information**: Browser type, operating system, device model, and screen resolution\n• **Usage Data**: How you use our services, including test results and interaction patterns\n• **Technical Data**: IP address (anonymized), access times, and page views\n• **Cookies and Similar Technologies**: Used to improve user experience and analyze website performance\n\nWe do not collect personal identification information such as names, email addresses, or phone numbers unless you voluntarily provide them.",
  dataUsageTitle: "How We Use Information",
  dataUsageContent: "We use the collected information to:\n\n• **Provide Services**: Run device tests and display results\n• **Improve Services**: Analyze usage patterns to optimize user experience\n• **Technical Support**: Diagnose and resolve technical issues\n• **Security**: Detect and prevent abuse or malicious activities\n• **Compliance**: Meet legal obligations and regulatory requirements",
  thirdPartyServicesTitle: "Third-Party Services",
  thirdPartyServicesContent: "We use the following third-party services:\n\n• **Google Analytics 4**: For website analytics and performance monitoring. Google may collect anonymous usage data. You can control this data collection through Cookie settings.\n• **Content Delivery Networks (CDN)**: To improve website loading speed\n\nThese services have their own privacy policies, and we recommend reviewing their policies to understand their data handling practices.",
  userRightsTitle: "Your Rights",
  userRightsContent: "Under applicable data protection laws, you have the following rights:\n\n• **Access**: Request access to information we hold about you\n• **Correction**: Request correction of inaccurate information\n• **Deletion**: Request deletion of your personal information\n• **Restriction**: Request restriction of processing your information\n• **Portability**: Request to receive your data in a structured format\n• **Objection**: Object to processing of your information\n\nTo exercise these rights, please contact us using the information below.",
  dataSecurityTitle: "Data Security",
  dataSecurityContent: "We implement appropriate technical and organizational measures to protect your information:\n\n• **Encryption**: All data transmission uses HTTPS encryption\n• **Access Control**: Restrict data access to authorized personnel only\n• **Regular Audits**: Regularly review our security measures\n• **Data Minimization**: Collect only necessary information\n• **Anonymization**: Anonymize data where possible",
  contactInformationTitle: "Contact Information",
  contactInformationContent: "If you have any questions about this Privacy Policy or need to exercise your rights, please contact us:\n\n• **Email**: <EMAIL>\n• **Address**: [Company Address]\n\nWe will respond to your request within 30 days of receipt.",

  // Cookie Policy Content
  cookieIntroduction: "This Cookie Policy explains how Setup Check uses cookies and similar technologies to recognize you when you visit our website. It explains what these technologies are and why we use them, as well as your rights to control our use of them.",
  whatAreCookiesTitle: "What Are Cookies",
  whatAreCookiesContent: "Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work, or to work more efficiently, as well as to provide reporting information.\n\nCookies set by the website owner (in this case, Setup Check) are called \"first-party cookies\". Cookies set by parties other than the website owner are called \"third-party cookies\". Third-party cookies enable third-party features or functionality to be provided on or through the website (e.g., advertising, interactive content, and analytics).",
  cookieTypesTitle: "Types of Cookies We Use",
  cookieTypesContent: "• **Necessary Cookies**: These cookies are strictly necessary for the operation of our website. They enable you to navigate around the website and use its features, such as accessing secure areas of the website. Without these cookies, services you have asked for cannot be provided.\n\n• **Analytics Cookies**: These cookies collect information about how visitors use our website, for instance which pages visitors go to most often, and if they get error messages from web pages. These cookies don't collect information that identifies a visitor. All information these cookies collect is aggregated and therefore anonymous.\n\n• **Functionality Cookies**: These cookies allow our website to remember choices you make (such as your user name, language, or the region you are in) and provide enhanced, more personal features.\n\n• **Marketing Cookies**: These cookies are used to track visitors across websites. The intention is to display ads that are relevant and engaging for the individual user and thereby more valuable for publishers and third-party advertisers.",
  manageCookiesTitle: "How to Manage Cookies",
  manageCookiesContent: "You can manage cookies in several ways:\n\n• **Cookie Settings**: Use the cookie consent banner on our website to choose your preferences\n• **Browser Settings**: Most web browsers allow you to control cookies through their browser settings\n• **Opt-out Tools**: You can use various online tools to opt out of specific tracking\n\nPlease note that disabling certain cookies may affect the functionality of the website and your user experience.",
  thirdPartyCookiesTitle: "Third-Party Cookies",
  thirdPartyCookiesContent: "We use Google Analytics to analyze the use of our website. Google Analytics uses cookies to help us analyze how users use the site. The information generated by the cookie about your use of the website (including your IP address) will be transmitted to and stored by Google on servers in the United States.\n\nGoogle will use this information for the purpose of evaluating your use of the website, compiling reports on website activity for website operators, and providing other services relating to website activity and internet usage.\n\nYou may refuse the use of cookies by selecting the appropriate settings on your browser, however please note that if you do this you may not be able to use the full functionality of this website.",

  // Contact Us - Additional translations
  userFeedback: "User Feedback",
  contactEmailTemplate: "Please describe the issue or suggestion you have:\n\n[Please describe your issue or suggestion in detail here]\n\nIf this is a technical issue, please include:\n- The type of device you're using\n- The specific steps that led to the problem\n- Any error messages (if applicable)",
  systemInfo: "System Information",
  browser: "Browser",
  language: "Language",
  timestamp: "Timestamp",
  contactTitle: "Contact Us",
  contactDescription: "Having issues or suggestions? We'd love to hear your feedback! Please contact us via email and we'll get back to you as soon as possible.",
  feedbackTypes: "Feedback Types",
  bugReport: "Bug Report",
  featureRequest: "Feature Request",
  generalInquiry: "General Inquiry",
  technicalSupport: "Technical Support",
};