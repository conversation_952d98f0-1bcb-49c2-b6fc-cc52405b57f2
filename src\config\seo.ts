import { SEOConfig } from '@/hooks/useSEO';

export interface PageSEOData {
  [key: string]: {
    titleKey: string;
    descriptionKey: string;
    keywords: string[];
    structuredData?: object;
  };
}

// 页面SEO数据配置
export const pageSEOData: PageSEOData = {
  home: {
    titleKey: 'siteName',
    descriptionKey: 'homePageDescription',
    keywords: [
      'device test', 'hardware check', 'online meeting', 'gaming setup',
      'camera test', 'microphone test', 'speaker test', 'keyboard test',
      'mouse test', 'network test', 'free device testing', 'hardware compatibility',
      'video call test', 'audio quality test', 'browser device test', 'setup check'
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Setup Check",
      "description": "Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality",
      "applicationCategory": "UtilityApplication",
      "operatingSystem": "Web Browser",
      "url": "https://setupcheck.com",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Camera Testing",
        "Microphone Testing",
        "Speaker Testing",
        "Keyboard Testing",
        "Mouse Testing",
        "Network Testing",
        "Real-time Testing",
        "Professional Reports",
        "Multi-language Support"
      ],
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "1250"
      }
    }
  },
  tools: {
    titleKey: 'deviceTestingTools',
    descriptionKey: 'toolsPageDescription',
    keywords: [
      'testing tools', 'hardware testing', 'device tools', 'professional testing',
      'camera test', 'microphone test', 'keyboard test', 'mouse test', 'speaker test',
      'network test', 'free testing tools', 'online device test', 'hardware diagnostics',
      'device compatibility', 'quality assurance', 'performance testing'
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Device Testing Tools",
      "description": "Professional collection of hardware testing tools for cameras, microphones, speakers, keyboards, mice, and network quality testing",
      "url": "https://setupcheck.com/tools",
      "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": 6,
        "itemListElement": [
          {
            "@type": "SoftwareApplication",
            "name": "Camera Test Tool",
            "description": "Test camera quality, resolution, and performance online",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          },
          {
            "@type": "SoftwareApplication",
            "name": "Microphone Test Tool",
            "description": "Test microphone audio quality, noise levels, and clarity",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          },
          {
            "@type": "SoftwareApplication",
            "name": "Speaker Test Tool",
            "description": "Test headphones and speakers audio quality and stereo balance",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          },
          {
            "@type": "SoftwareApplication",
            "name": "Keyboard Test Tool",
            "description": "Test all keyboard keys functionality and response time",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          },
          {
            "@type": "SoftwareApplication",
            "name": "Mouse Test Tool",
            "description": "Test mouse accuracy, click responsiveness, and scroll functionality",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          },
          {
            "@type": "SoftwareApplication",
            "name": "Network Test Tool",
            "description": "Test internet speed, latency and connection stability",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          }
        ]
      }
    }
  },
  camera: {
    titleKey: 'cameraTest',
    descriptionKey: 'cameraTestDescription',
    keywords: ['camera test', 'webcam test', 'video quality', 'camera resolution', 'video call'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Camera Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test your camera quality and compatibility online"
    }
  },
  microphone: {
    titleKey: 'microphoneTest',
    descriptionKey: 'microphoneTestDescription',
    keywords: ['microphone test', 'mic test', 'audio quality', 'voice recording', 'audio input'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Microphone Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test your microphone audio quality and noise levels"
    }
  },
  headphones: {
    titleKey: 'headphonesTest',
    descriptionKey: 'headphonesTestDescription',
    keywords: ['headphones test', 'speaker test', 'audio output', 'stereo test', 'volume test'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Headphones Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test your headphones and speakers audio quality"
    }
  },
  keyboard: {
    titleKey: 'keyboardTest',
    descriptionKey: 'keyboardTestDescription',
    keywords: ['keyboard test', 'key test', 'typing test', 'keyboard mapping', 'gaming keyboard'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Keyboard Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test all keyboard keys functionality and response time"
    }
  },
  mouse: {
    titleKey: 'mouseTest',
    descriptionKey: 'mouseTestDescription',
    keywords: ['mouse test', 'click test', 'scroll test', 'mouse accuracy', 'gaming mouse'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Mouse Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test mouse accuracy, clicks, and scroll functionality"
    }
  },
  network: {
    titleKey: 'networkQualityTest',
    descriptionKey: 'networkTestDescription',
    keywords: ['network test', 'speed test', 'latency test', 'internet speed', 'connection test'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Network Test Tool",
      "applicationCategory": "UtilityApplication",
      "description": "Test your internet speed, latency and connection stability"
    }
  },
  test: {
    titleKey: 'selectScenario',
    descriptionKey: 'selectScenario',
    keywords: ['test scenarios', 'device testing', 'hardware compatibility', 'online meeting test', 'gaming setup test'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Test Scenarios",
      "description": "Choose from comprehensive testing scenarios designed for different use cases",
      "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": 2,
        "itemListElement": [
          {
            "@type": "TestAction",
            "name": "Online Meeting Test",
            "description": "Complete device compatibility check for online meetings"
          },
          {
            "@type": "TestAction",
            "name": "Gaming Setup Test", 
            "description": "Complete gaming setup check for optimal gaming experience"
          }
        ]
      }
    }
  },
  meeting: {
    titleKey: 'onlineMeeting',
    descriptionKey: 'meetingTestDescription',
    keywords: ['online meeting', 'video call', 'meeting setup', 'device compatibility', 'hardware check'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Meeting Setup Test",
      "applicationCategory": "UtilityApplication",
      "description": "Complete device compatibility check for online meetings"
    }
  },
  gaming: {
    titleKey: 'gamingSetup',
    descriptionKey: 'gamingTestDescription',
    keywords: ['gaming setup', 'gaming peripherals', 'gaming test', 'esports', 'gaming hardware'],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Gaming Setup Test",
      "applicationCategory": "GameApplication",
      "description": "Complete gaming setup check for optimal gaming experience"
    }
  }
};

// 默认SEO配置
export const defaultSEOConfig: Partial<SEOConfig> = {
  ogType: 'website',
  twitterCard: 'summary_large_image',
  ogImage: '/logo.png', // 使用项目中的logo图片
};

// 生成页面SEO配置的工具函数
export const generatePageSEO = (
  pageKey: string, 
  t: (key: string) => string,
  baseUrl: string = ''
): SEOConfig => {
  const pageData = pageSEOData[pageKey];
  
  if (!pageData) {
    return {
      title: t('siteName'),
      description: t('siteDescription'),
      ...defaultSEOConfig
    };
  }

  const seoConfig: SEOConfig = {
    title: t(pageData.titleKey),
    description: t(pageData.descriptionKey),
    keywords: pageData.keywords,
    canonical: baseUrl ? `${baseUrl}/${pageKey}` : undefined,
    structuredData: pageData.structuredData,
    ...defaultSEOConfig
  };

  return seoConfig;
}; 