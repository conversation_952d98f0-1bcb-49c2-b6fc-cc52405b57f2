import { GA4Config } from '@/types/analytics';

// GA4 配置
export const ga4Config: GA4Config = {
  measurementId: import.meta.env.VITE_GA4_MEASUREMENT_ID || '',
  enabled: import.meta.env.VITE_GA4_ENABLED === 'true' && 
           import.meta.env.VITE_GA4_MEASUREMENT_ID && 
           import.meta.env.VITE_GA4_MEASUREMENT_ID !== 'GA_MEASUREMENT_ID',
  debug: import.meta.env.VITE_GA4_DEBUG === 'true'
};

// 检查是否在生产环境
export const isProduction = import.meta.env.PROD;

// 检查是否应该启用分析
export const shouldEnableAnalytics = (): boolean => {
  // 在开发环境下，只有明确启用才会加载
  if (!isProduction && !ga4Config.enabled) {
    return false;
  }
  
  // 检查是否有有效的测量 ID
  if (!ga4Config.measurementId || ga4Config.measurementId === 'GA_MEASUREMENT_ID') {
    console.warn('GA4: 无效的测量 ID，分析功能已禁用');
    return false;
  }
  
  return ga4Config.enabled;
};

// 调试日志函数
export const debugLog = (message: string, data?: any): void => {
  if (ga4Config.debug) {
    console.log(`[GA4 Debug] ${message}`, data || '');
  }
};

// 错误日志函数
export const errorLog = (message: string, error?: any): void => {
  console.error(`[GA4 Error] ${message}`, error || '');
};
