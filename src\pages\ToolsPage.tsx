import React from "react";
import { useNavigate } from "react-router-dom";
import { 
  Camera, 
  Mic, 
  Headphones, 
  Keyboard, 
  Mouse, 
  Wifi,
  ArrowRight,
  CheckCircle,
  Zap
} from "lucide-react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { GlassCard } from "@/components/ui/GlassCard";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

export const ToolsPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  
  // 生成SEO配置
  const seoConfig = {
    title: t('deviceTestingTools'),
    description: t('deviceTestingToolsDescription'),
    keywords: [t('testingTools'), t('hardwareTesting'), t('deviceTools'), t('cameraTest'), t('microphoneTest'), t('keyboardTest')],
  };

  // 工具配置
  const tools = [
    {
      id: 'camera',
      icon: Camera,
      title: t('cameraTest'),
      description: t('cameraTestDescription'),
      path: '/tools/camera',
      color: 'from-blue-500 to-cyan-500',
      features: [
        t('resolution'),
        t('frameRate'),
        t('colorBrightness')
      ]
    },
    {
      id: 'microphone',
      icon: Mic,
      title: t('microphoneTest'),
      description: t('microphoneTestDescription'),
      path: '/tools/microphone',
      color: 'from-red-500 to-pink-500',
      features: [
        t('audioQuality'),
        t('noiseLevel'), 
        t('sensitivity')
      ]
    },
    {
      id: 'headphones',
      icon: Headphones,
      title: t('headphonesTest'),
      description: t('headphonesTestDescription'),
      path: '/tools/headphones',
      color: 'from-purple-500 to-indigo-500',
      features: [
        t('stereoBalance'),
        t('volumeLevel'),
        t('audioOutput')
      ]
    },
    {
      id: 'keyboard',
      icon: Keyboard,
      title: t('keyboardTest'),
      description: t('keyboardTestDescription'),
      path: '/tools/keyboard',
      color: 'from-green-500 to-emerald-500',
      features: [
        t('keyResponse'),
        t('keyMapping'),
        t('typingSpeed')
      ]
    },
    {
      id: 'mouse',
      icon: Mouse,
      title: t('mouseTest'),
      description: t('mouseTestDescription'),
      path: '/tools/mouse',
      color: 'from-orange-500 to-yellow-500',
      features: [
        t('clickAccuracy'),
        t('scrollFunction'), 
        t('mouseAccuracy')
      ]
    },
    {
      id: 'network',
      icon: Wifi,
      title: t('networkQualityTest'),
      description: t('networkTestDescription'),
      path: '/tools/network',
      color: 'from-teal-500 to-blue-500',
      features: [
        t('internetSpeed'),
        t('latency'),
        t('connectionStability')
      ]
    }
  ];

  const handleToolClick = (path: string) => {
    const fullPath = language === 'en' ? path : `/${language}${path}`;
    navigate(fullPath);
  };

  return (
    <MainLayout seoConfig={seoConfig}>
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          {t('deviceTestingTools')}
        </h1>
        <p className="text-xl text-white/80 max-w-3xl mx-auto">
          {t('deviceTestingToolsDescription')}
        </p>
      </div>

      {/* 工具网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {tools.map((tool) => {
          const IconComponent = tool.icon;
          
          return (
            <GlassCard
              key={tool.id}
              className="group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl h-full flex flex-col"
              onClick={() => handleToolClick(tool.path)}
            >
              {/* 渐变背景 */}
              <div 
                className={`absolute inset-0 bg-gradient-to-br ${tool.color} opacity-10 rounded-2xl group-hover:opacity-20 transition-opacity duration-300`}
              />
              
              {/* 内容 */}
              <div className="relative flex flex-col h-full p-6 lg:p-8">
                {/* 图标和标题 */}
                <div className="flex items-center mb-6">
                  <div className={`p-4 rounded-xl bg-gradient-to-br ${tool.color} mr-4`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl lg:text-2xl font-semibold text-white group-hover:text-blue-300 transition-colors">
                    {tool.title}
                  </h3>
                </div>

                {/* 描述 */}
                <p className="text-white/70 mb-6 text-base leading-relaxed line-clamp-2">
                  {tool.description}
                </p>

                {/* 功能特性 */}
                <div className="space-y-2 mb-6 flex-1">
                  {tool.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-white/60">
                      <CheckCircle className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                {/* 行动按钮 */}
                <div className="flex items-center justify-between mt-auto">
                  <div className="flex items-center text-sm text-white/50">
                    <Zap className="w-4 h-4 mr-1" />
                    <span>{t('quickTest')}</span>
                  </div>

                  <div className="flex items-center text-blue-400 group-hover:text-blue-300 transition-colors">
                    <span className="text-sm font-medium mr-1">{t('startTest')}</span>
                    <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </GlassCard>
          );
        })}
      </div>

      {/* 底部信息 */}
      <div className="text-center">
        <GlassCard className="p-8">
          <h2 className="text-2xl font-semibold text-white mb-4">
            {t('needHelp')}
          </h2>
          <p className="text-white/70 mb-6">
            {t('testingToolsDescription')}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-400 mb-2">6+</div>
              <div className="text-white/60">{t('testingTools')}</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-400 mb-2">99%</div>
              <div className="text-white/60">{t('accuracy')}</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-400 mb-2">
                {t('free')}
              </div>
              <div className="text-white/60">{t('noRegistration')}</div>
            </div>
          </div>
        </GlassCard>
      </div>
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="tools" />
        <FAQ pageType="tools" />
        <Glossary pageType="tools" />
        <TroubleshootingGuide pageType="tools" />
        <SEOFooter pageType="tools" />
      </div>
    </MainLayout>
  );
}; 