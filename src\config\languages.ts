export type Language = 'en' | 'zh' | 'es' | 'de' | 'ja' | 'ko' | 'fr';

// 语言配置接口
export interface LanguageConfig {
  code: Language;
  name: string;
  nativeName: string;
  flag: string;
  browserPrefixes: string[];
  isRTL?: boolean;
}

// 集中的语言配置 - 添加新语言只需要在这里添加一项
export const LANGUAGE_CONFIGS: Record<Language, LanguageConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    browserPrefixes: ['en']
  },
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    browserPrefixes: ['zh', 'zh-cn', 'zh-tw', 'zh-hk']
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    browserPrefixes: ['es']
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    browserPrefixes: ['de']
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    browserPrefixes: ['ja']
  },
  ko: {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    browserPrefixes: ['ko']
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    browserPrefixes: ['fr']
  }
};

// 支持的语言列表
export const SUPPORTED_LANGUAGES: Language[] = Object.keys(LANGUAGE_CONFIGS) as Language[];

// 默认语言
export const DEFAULT_LANGUAGE: Language = 'en';

// 本地存储键名
export const LANGUAGE_STORAGE_KEY = 'preferred-language';

// 语言检测逻辑
export const detectBrowserLanguage = (): Language => {
  const browserLang = navigator.language.toLowerCase();
  
  // 遍历所有支持的语言，查找匹配的浏览器前缀
  for (const [langCode, config] of Object.entries(LANGUAGE_CONFIGS)) {
    if (config.browserPrefixes.some(prefix => browserLang.startsWith(prefix))) {
      return langCode as Language;
    }
  }
  
  return DEFAULT_LANGUAGE;
};

// 获取用户偏好语言
export const getPreferredLanguage = (): Language => {
  // 首先检查用户保存的偏好
  const savedLang = localStorage.getItem(LANGUAGE_STORAGE_KEY) as Language;
  if (savedLang && SUPPORTED_LANGUAGES.includes(savedLang)) {
    return savedLang;
  }
  
  // 回退到浏览器语言检测
  return detectBrowserLanguage();
};

// 保存语言偏好
export const saveLanguagePreference = (language: Language): void => {
  if (SUPPORTED_LANGUAGES.includes(language)) {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  }
};

// 验证语言代码是否有效
export const isValidLanguage = (lang: string): lang is Language => {
  return SUPPORTED_LANGUAGES.includes(lang as Language);
};

// 获取语言的显示名称
export const getLanguageDisplayName = (lang: Language): string => {
  return LANGUAGE_CONFIGS[lang]?.nativeName || lang;
};

// 获取语言的国旗
export const getLanguageFlag = (lang: Language): string => {
  return LANGUAGE_CONFIGS[lang]?.flag || '🌐';
};

// 为新语言添加提供便利函数
export const addLanguageSupport = (config: LanguageConfig) => {
  console.warn('To add a new language, please update the LANGUAGE_CONFIGS object in src/config/languages.ts');
  console.info('New language config:', config);
}; 