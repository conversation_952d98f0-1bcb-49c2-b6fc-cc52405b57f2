import React, { useState, useEffect, useRef } from "react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { RotateCcw, Mouse, CheckCircle2, Target, MousePointer2 } from "lucide-react";
import { useLanguage } from "@/hooks/useLanguage";

interface MouseTestModuleProps {
  onNext?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack?: () => void;
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
}

interface ClickEvent {
  x: number;
  y: number;
  button: number;
  timestamp: number;
  accuracy?: number;
}

interface TestStats {
  totalClicks: number;
  leftClicks: number;
  rightClicks: number;
  averageAccuracy: number;
  clickSequences: number;
  scrollEvents: number;
}

interface Target {
  id: number;
  x: number;
  y: number;
  size: number;
  hit: boolean;
  targetTime: number;
}

export const MouseTestModule: React.FC<MouseTestModuleProps> = ({ onNext, onBack, onTestResult }) => {
  const { t } = useLanguage();
  const [isActive, setIsActive] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [currentTest, setCurrentTest] = useState<'clicking' | 'accuracy' | 'scroll'>('clicking');
  const [testStats, setTestStats] = useState<TestStats>({
    totalClicks: 0,
    leftClicks: 0,
    rightClicks: 0,
    averageAccuracy: 0,
    clickSequences: 0,
    scrollEvents: 0
  });
  const [clickHistory, setClickHistory] = useState<ClickEvent[]>([]);
  const [targets, setTargets] = useState<Target[]>([]);
  const [currentTargetIndex, setCurrentTargetIndex] = useState(0);
  const [testComplete, setTestComplete] = useState(false);
  
  const testAreaRef = useRef<HTMLDivElement>(null);
  const startTimeRef = useRef<number>(0);

  // 生成随机目标点
  const generateTargets = () => {
    const newTargets: Target[] = [];
    const targetCount = 8;
    const containerWidth = 400;
    const containerHeight = 300;
    const targetSize = 40;
    
    for (let i = 0; i < targetCount; i++) {
      newTargets.push({
        id: i,
        x: Math.random() * (containerWidth - targetSize),
        y: Math.random() * (containerHeight - targetSize),
        size: targetSize,
        hit: false,
        targetTime: 0
      });
    }
    
    setTargets(newTargets);
    setCurrentTargetIndex(0);
  };

  useEffect(() => {
    const handleMouseClick = (event: MouseEvent) => {
      if (!isActive || currentTest !== 'clicking') return;
      
      const rect = testAreaRef.current?.getBoundingClientRect();
      if (!rect) return;
      
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      const clickEvent: ClickEvent = {
        x,
        y,
        button: event.button,
        timestamp: Date.now()
      };
      
      setClickHistory(prev => [...prev.slice(-9), clickEvent]);
      
      setTestStats(prev => ({
        ...prev,
        totalClicks: prev.totalClicks + 1,
        leftClicks: event.button === 0 ? prev.leftClicks + 1 : prev.leftClicks,
        rightClicks: event.button === 2 ? prev.rightClicks + 1 : prev.rightClicks
      }));
    };

    const handleScroll = (event: WheelEvent) => {
      if (!isActive || currentTest !== 'scroll') return;
      
      setTestStats(prev => ({
        ...prev,
        scrollEvents: prev.scrollEvents + 1
      }));
    };

    const handleContextMenu = (event: MouseEvent) => {
      if (isActive) {
        event.preventDefault();
      }
    };

    if (isActive) {
      testAreaRef.current?.addEventListener('click', handleMouseClick);
      testAreaRef.current?.addEventListener('contextmenu', handleContextMenu);
      testAreaRef.current?.addEventListener('wheel', handleScroll);
    }

    return () => {
      testAreaRef.current?.removeEventListener('click', handleMouseClick);
      testAreaRef.current?.removeEventListener('contextmenu', handleContextMenu);
      testAreaRef.current?.removeEventListener('wheel', handleScroll);
    };
  }, [isActive, currentTest]);

  const handleTargetClick = (targetId: number, event: React.MouseEvent) => {
    if (!isActive || currentTest !== 'accuracy') return;
    
    event.stopPropagation();
    
    const target = targets[targetId];
    if (target && !target.hit && targetId === currentTargetIndex) {
      const rect = event.currentTarget.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const clickX = event.clientX;
      const clickY = event.clientY;
      
      const distance = Math.sqrt(
        Math.pow(clickX - centerX, 2) + Math.pow(clickY - centerY, 2)
      );
      
      const accuracy = Math.max(0, 100 - (distance / (target.size / 2)) * 100);
      
      const updatedTargets = [...targets];
      updatedTargets[targetId] = {
        ...target,
        hit: true,
        targetTime: Date.now() - startTimeRef.current
      };
      
      setTargets(updatedTargets);
      setCurrentTargetIndex(prev => prev + 1);
      
      setTestStats(prev => ({
        ...prev,
        averageAccuracy: (prev.averageAccuracy * prev.clickSequences + accuracy) / (prev.clickSequences + 1),
        clickSequences: prev.clickSequences + 1
      }));
      
      if (currentTargetIndex >= targets.length - 1) {
        setTestComplete(true);
      }
    }
  };

  // 自动报告测试结果
  useEffect(() => {
    if (onTestResult && testComplete && testStats.totalClicks > 0) {
      const testPassed = testStats.averageAccuracy >= 0.7 && testStats.totalClicks >= 5; // 70%精度且至少5次点击
      
      let failureReason;
      if (!testPassed) {
        const issues = [];
        if (testStats.averageAccuracy < 0.7) issues.push(t("mouseAccuracyInsufficient"));
        if (testStats.totalClicks < 5) issues.push(t("mouseClicksInsufficient"));
        failureReason = issues.join(", ");
      }

      onTestResult({
        passed: testPassed,
        failureReason: failureReason,
        details: {
          totalClicks: testStats.totalClicks,
          leftClicks: testStats.leftClicks,
          rightClicks: testStats.rightClicks,
          averageAccuracy: testStats.averageAccuracy,
          clickSequences: testStats.clickSequences,
          scrollEvents: testStats.scrollEvents
        }
      });
    }
  }, [onTestResult, testComplete, testStats, t]);

  const startTest = () => {
    setIsActive(true);
    setHasStarted(true);
    setTestComplete(false);
    startTimeRef.current = Date.now();
    
    if (currentTest === 'accuracy') {
      generateTargets();
    }
  };

  const stopTest = () => {
    setIsActive(false);
  };

  const resetTest = () => {
    setIsActive(false);
    setTestStats({
      totalClicks: 0,
      leftClicks: 0,
      rightClicks: 0,
      averageAccuracy: 0,
      clickSequences: 0,
      scrollEvents: 0
    });
    setClickHistory([]);
    setTargets([]);
    setCurrentTargetIndex(0);
    setTestComplete(false);
    setHasStarted(false);
  };

  const nextTestType = () => {
    stopTest();
    if (currentTest === 'clicking') {
      setCurrentTest('accuracy');
    } else if (currentTest === 'accuracy') {
      setCurrentTest('scroll');
    }
  };

  const getTestResult = () => {
    const issues: string[] = [];
    
    // 检查基本点击测试
    if (testStats.totalClicks < 5) {
      issues.push("Not enough clicks performed");
    }
    
    // 检查准确性测试
    if (testStats.clickSequences < 3) {
      issues.push("Accuracy test not completed");
    } else if (testStats.averageAccuracy < 60) {
      issues.push("Low clicking accuracy");
    }
    
    // 检查滚轮测试
    if (testStats.scrollEvents < 3) {
      issues.push("Mouse wheel test not completed");
    }
    
    // 检查左右键平衡
    if (testStats.leftClicks === 0) {
      issues.push("Left click not tested");
    }
    
    if (testStats.rightClicks === 0) {
      issues.push("Right click not tested");
    }

    const passed = issues.length === 0;
    
    return {
      passed,
      details: {
        totalClicks: testStats.totalClicks,
        accuracy: Math.round(testStats.averageAccuracy),
        leftClicks: testStats.leftClicks,
        rightClicks: testStats.rightClicks,
        scrollEvents: testStats.scrollEvents,
        targetsHit: testStats.clickSequences
      },
      failureReason: issues.length > 0 ? issues.join(", ") : undefined
    };
  };

  const handleNext = () => {
    const result = getTestResult();
    onNext(result);
  };

  const getCurrentTestName = () => {
    switch (currentTest) {
      case 'clicking': return '基础点击测试';
      case 'accuracy': return '精确度测试';
      case 'scroll': return '滚轮测试';
      default: return '';
    }
  };

  const getTestInstructions = () => {
    switch (currentTest) {
      case 'clicking':
        return '在测试区域内进行左键和右键点击，测试鼠标按键的响应性';
      case 'accuracy':
        return '依次点击出现的目标，测试鼠标的精确度和反应时间';
      case 'scroll':
        return '在测试区域内上下滚动鼠标滚轮，测试滚轮的流畅性';
      default:
        return '';
    }
  };

  return (
    <GlassCard className="max-w-4xl mx-auto">
      <div className="text-center mb-6">
        <Mouse className="h-16 w-16 mx-auto mb-4 text-blue-400" />
        <h2 className="text-3xl font-bold text-white mb-2">{t("mouseTest")}</h2>
        <p className="text-white/70 text-lg">{t("gamingMouseTestDesc")}</p>
      </div>

      {!hasStarted ? (
        <div className="text-center">
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-6 mb-6">
            <h3 className="text-blue-200 font-semibold mb-3">测试说明</h3>
            <div className="text-left text-white/80 space-y-2">
              <p>• 鼠标测试包含三个部分：基础点击、精确度、滚轮</p>
              <p>• 基础点击：测试左键和右键的响应性</p>
              <p>• 精确度测试：点击目标测试鼠标准确性</p>
              <p>• 滚轮测试：测试鼠标滚轮的流畅性</p>
            </div>
          </div>
          <PrimaryButton onClick={startTest} size="lg">
            开始鼠标测试
          </PrimaryButton>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 当前测试类型 */}
          <div className="text-center">
            <h3 className="text-xl font-semibold text-white mb-2">{getCurrentTestName()}</h3>
            <p className="text-white/70">{getTestInstructions()}</p>
          </div>

          {/* 测试统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-xl font-bold text-white">{testStats.totalClicks}</div>
              <div className="text-white/60 text-sm">总点击数</div>
            </div>
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-xl font-bold text-white">
                {testStats.averageAccuracy > 0 ? `${Math.round(testStats.averageAccuracy)}%` : "-"}
              </div>
              <div className="text-white/60 text-sm">平均精确度</div>
            </div>
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-xl font-bold text-white">{testStats.clickSequences}</div>
              <div className="text-white/60 text-sm">目标命中</div>
            </div>
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-xl font-bold text-white">{testStats.scrollEvents}</div>
              <div className="text-white/60 text-sm">滚轮事件</div>
            </div>
          </div>

          {/* 测试区域 */}
          <div className="bg-white/5 rounded-xl p-6">
            <div 
              ref={testAreaRef}
              className="relative bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border-2 border-dashed border-white/20 mx-auto"
              style={{ width: '400px', height: '300px' }}
            >
              {/* 基础点击测试 */}
              {currentTest === 'clicking' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white/60">
                    <MousePointer2 className="h-12 w-12 mx-auto mb-2" />
                    <p>在此区域内进行左键和右键点击</p>
                    <p className="text-sm mt-2">已点击: {testStats.leftClicks} 左键, {testStats.rightClicks} 右键</p>
                  </div>
                </div>
              )}

              {/* 精确度测试 */}
              {currentTest === 'accuracy' && targets.map((target, index) => (
                <div
                  key={target.id}
                  className={`absolute rounded-full border-2 cursor-pointer transition-all duration-200 ${
                    target.hit
                      ? "bg-green-500/30 border-green-400"
                      : index === currentTargetIndex
                        ? "bg-red-500/30 border-red-400 animate-pulse"
                        : "bg-white/10 border-white/30"
                  }`}
                  style={{
                    left: target.x,
                    top: target.y,
                    width: target.size,
                    height: target.size
                  }}
                  onClick={(e) => handleTargetClick(target.id, e)}
                >
                  <div className="flex items-center justify-center h-full text-white font-bold">
                    {target.hit ? <CheckCircle2 className="h-5 w-5" /> : index + 1}
                  </div>
                </div>
              ))}

              {/* 滚轮测试 */}
              {currentTest === 'scroll' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white/60">
                    <div className="animate-bounce mb-4">
                      <div className="w-8 h-12 border-2 border-white/40 rounded-xl mx-auto relative">
                        <div className="w-1 h-3 bg-white/60 rounded mx-auto mt-2 animate-pulse"></div>
                      </div>
                    </div>
                    <p>在此区域内上下滚动鼠标滚轮</p>
                    <p className="text-sm mt-2">滚动次数: {testStats.scrollEvents}</p>
                  </div>
                </div>
              )}

              {/* 点击历史显示 */}
              {currentTest === 'clicking' && clickHistory.map((click, index) => (
                <div
                  key={index}
                  className={`absolute w-3 h-3 rounded-full pointer-events-none animate-ping ${
                    click.button === 0 ? "bg-blue-400" : "bg-red-400"
                  }`}
                  style={{
                    left: click.x - 6,
                    top: click.y - 6,
                  }}
                />
              ))}
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex justify-center space-x-4">
            {isActive ? (
              <PrimaryButton onClick={stopTest} variant="secondary">
                停止测试
              </PrimaryButton>
            ) : (
              <PrimaryButton onClick={startTest}>
                继续测试
              </PrimaryButton>
            )}
            
            {currentTest !== 'scroll' && (
              <PrimaryButton onClick={nextTestType}>
                下一测试类型
              </PrimaryButton>
            )}
            
            <PrimaryButton onClick={resetTest} variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </PrimaryButton>
          </div>

          {/* 测试进度 */}
          <div className="flex justify-center space-x-4">
            {['clicking', 'accuracy', 'scroll'].map((testType, index) => (
              <div
                key={testType}
                className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                  currentTest === testType
                    ? "bg-blue-500/20 text-blue-300"
                    : "bg-white/10 text-white/60"
                }`}
              >
                <div className={`w-2 h-2 rounded-full ${
                  currentTest === testType ? "bg-blue-400" : "bg-white/40"
                }`} />
                <span>{testType === 'clicking' ? '点击' : testType === 'accuracy' ? '精确度' : '滚轮'}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 保留兼容性的导航按钮 - 仅在使用旧API时显示 */}
      {!onTestResult && (
        <div className="flex justify-between mt-8">
          <PrimaryButton onClick={onBack} variant="outline">
            {t("back")}
          </PrimaryButton>
          <PrimaryButton 
            onClick={handleNext}
            disabled={!hasStarted || testStats.totalClicks < 3}
          >
            {t("next")}
          </PrimaryButton>
        </div>
      )}
    </GlassCard>
  );
}; 