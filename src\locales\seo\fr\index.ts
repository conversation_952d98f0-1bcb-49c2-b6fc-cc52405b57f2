/**
 * Français - Module d'Index des Traductions SEO
 * Exportation unifiée de tout le contenu de traduction SEO français
 */

import type { SEOTranslation } from '../types';
import { frEnhanced } from './enhanced';
import { frFooter } from './footer';
import { frKeywords } from './keywords';
import { frFAQ } from './faq';
import { frGlossary } from './glossary';
import { frTroubleshooting } from './troubleshooting';

export const frTranslation: SEOTranslation = {
  enhanced: frEnhanced,
  seoFooter: frFooter,
  seoKeywords: frKeywords,
  faq: frFAQ,
  glossary: frGlossary,
  troubleshooting: frTroubleshooting
};

// Maintenir la compatibilité descendante
export const frSEO = frTranslation;

// Exporter les modules individuels pour un import sélectif
export { frEnhanced, frFooter, frKeywords };
