import React from 'react';
import { CookieConsentBanner } from './CookieConsentBanner';
import { CookieConsentModal } from './CookieConsentModal';
import { useCookieConsent } from '@/hooks/useCookieConsent';

/**
 * 隐私管理器组件 - 统一管理 Cookie 同意和隐私设置
 */
export const PrivacyManager: React.FC = () => {
  const {
    consent,
    showBanner,
    showModal,
    acceptAll,
    rejectAll,
    customizeConsent,
    showCustomizeModal,
    hideBanner,
    hideModal
  } = useCookieConsent();

  return (
    <>
      {/* Cookie 同意横幅 */}
      <CookieConsentBanner
        isVisible={showBanner}
        onAcceptAll={acceptAll}
        onRejectAll={rejectAll}
        onCustomize={showCustomizeModal}
        onClose={hideBanner}
      />

      {/* Cookie 自定义设置模态框 */}
      <CookieConsentModal
        isOpen={showModal}
        onClose={hideModal}
        onSave={customizeConsent}
        currentConsent={consent}
      />
    </>
  );
};
