<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <!-- 渐变背景 - 使用蓝色主题 -->
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    
    <!-- 边框渐变 -->
    <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-opacity="0.3"/>
    </filter>
    
    <!-- 内发光 -->
    <filter id="innerGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 - 玻璃态效果 -->
  <circle 
    cx="16" 
    cy="16" 
    r="15" 
    fill="url(#glassGradient)" 
    stroke="url(#borderGradient)" 
    stroke-width="1" 
    filter="url(#shadow)"
  />
  
  <!-- Monitor 图标主体 -->
  <g transform="translate(16, 16)" fill="white" fill-opacity="1" filter="url(#innerGlow)">
    <!-- 屏幕 -->
    <rect x="-8" y="-6" width="16" height="10" rx="1" ry="1" stroke="white" stroke-width="1.5" fill="rgba(255,255,255,0.1)"/>
    
    <!-- 屏幕内容 - 小圆点表示像素 -->
    <circle cx="-4" cy="-2" r="0.8" fill="white" fill-opacity="1"/>
    <circle cx="0" cy="-2" r="0.8" fill="white" fill-opacity="1"/>
    <circle cx="4" cy="-2" r="0.8" fill="white" fill-opacity="1"/>
    <circle cx="-2" cy="1" r="0.8" fill="white" fill-opacity="1"/>
    <circle cx="2" cy="1" r="0.8" fill="white" fill-opacity="1"/>
    
    <!-- 支架 -->
    <rect x="-1" y="4" width="2" height="3" rx="0.5" fill="white" fill-opacity="1"/>
    
    <!-- 底座 -->
    <rect x="-4" y="7" width="8" height="1" rx="0.5" fill="white" fill-opacity="1"/>
  </g>
  
  <!-- 高光效果 -->
  <circle 
    cx="16" 
    cy="16" 
    r="15" 
    fill="none" 
    stroke="rgba(96, 165, 250, 0.8)" 
    stroke-width="1" 
    stroke-opacity="0.8"
  />
  
  <!-- 顶部高光 -->
  <ellipse 
    cx="16" 
    cy="8" 
    rx="8" 
    ry="2" 
    fill="rgba(255,255,255,0.4)" 
    fill-opacity="0.6"
  />
</svg> 