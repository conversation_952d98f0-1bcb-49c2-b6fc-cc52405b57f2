import { useState, useEffect, useCallback } from 'react';
import { CookieConsent, ConsentStatus, DEFAULT_COOKIE_CONSENT, PRIVACY_POLICY_VERSION } from '@/types/privacy';
import { setConsent } from '@/lib/analytics';

const CONSENT_STORAGE_KEY = 'cookie-consent';
const CONSENT_EXPIRY_DAYS = 365;

/**
 * Cookie 同意管理 Hook
 */
export const useCookieConsent = () => {
  const [consent, setConsentState] = useState<CookieConsent | null>(null);
  const [status, setStatus] = useState<ConsentStatus>('pending');
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // 从本地存储加载同意设置
  const loadConsent = useCallback(() => {
    try {
      const stored = localStorage.getItem(CONSENT_STORAGE_KEY);
      if (stored) {
        const parsedConsent: CookieConsent = JSON.parse(stored);
        
        // 检查版本是否过期
        if (parsedConsent.version !== PRIVACY_POLICY_VERSION) {
          // 版本过期，重新请求同意
          localStorage.removeItem(CONSENT_STORAGE_KEY);
          setStatus('pending');
          setShowBanner(true);
          return;
        }
        
        // 检查是否过期（365天）
        const expiryTime = parsedConsent.timestamp + (CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
        if (Date.now() > expiryTime) {
          localStorage.removeItem(CONSENT_STORAGE_KEY);
          setStatus('pending');
          setShowBanner(true);
          return;
        }
        
        setConsentState(parsedConsent);
        setStatus(parsedConsent.analytics ? 'accepted' : 'customized');
        
        // 应用 GA4 同意设置
        applyConsentToGA4(parsedConsent);
      } else {
        setStatus('pending');
        setShowBanner(true);
      }
    } catch (error) {
      console.error('加载 Cookie 同意设置失败:', error);
      setStatus('pending');
      setShowBanner(true);
    }
  }, []);

  // 保存同意设置
  const saveConsent = useCallback((newConsent: CookieConsent) => {
    try {
      const consentWithTimestamp = {
        ...newConsent,
        timestamp: Date.now(),
        version: PRIVACY_POLICY_VERSION
      };
      
      localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentWithTimestamp));
      setConsentState(consentWithTimestamp);
      
      // 应用 GA4 同意设置
      applyConsentToGA4(consentWithTimestamp);
      
      // 更新状态
      if (newConsent.analytics && newConsent.marketing && newConsent.preferences) {
        setStatus('accepted');
      } else if (newConsent.analytics || newConsent.marketing || newConsent.preferences) {
        setStatus('customized');
      } else {
        setStatus('rejected');
      }
      
      setShowBanner(false);
      setShowModal(false);
    } catch (error) {
      console.error('保存 Cookie 同意设置失败:', error);
    }
  }, []);

  // 应用同意设置到 GA4
  const applyConsentToGA4 = useCallback((consent: CookieConsent) => {
    setConsent({
      analytics_storage: consent.analytics ? 'granted' : 'denied',
      ad_storage: consent.marketing ? 'granted' : 'denied',
      functionality_storage: consent.preferences ? 'granted' : 'denied',
      personalization_storage: consent.preferences ? 'granted' : 'denied',
      security_storage: 'granted' // 安全相关的始终允许
    });
  }, []);

  // 接受所有 Cookie
  const acceptAll = useCallback(() => {
    const fullConsent: CookieConsent = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
      timestamp: Date.now(),
      version: PRIVACY_POLICY_VERSION
    };
    saveConsent(fullConsent);
  }, [saveConsent]);

  // 拒绝所有非必要 Cookie
  const rejectAll = useCallback(() => {
    const minimalConsent: CookieConsent = {
      ...DEFAULT_COOKIE_CONSENT,
      timestamp: Date.now()
    };
    saveConsent(minimalConsent);
  }, [saveConsent]);

  // 自定义同意设置
  const customizeConsent = useCallback((customConsent: Partial<CookieConsent>) => {
    const newConsent: CookieConsent = {
      necessary: true, // 必要 Cookie 始终启用
      analytics: customConsent.analytics ?? false,
      marketing: customConsent.marketing ?? false,
      preferences: customConsent.preferences ?? false,
      timestamp: Date.now(),
      version: PRIVACY_POLICY_VERSION
    };
    saveConsent(newConsent);
  }, [saveConsent]);

  // 显示自定义模态框
  const showCustomizeModal = useCallback(() => {
    setShowModal(true);
    setShowBanner(false);
  }, []);

  // 隐藏横幅
  const hideBanner = useCallback(() => {
    setShowBanner(false);
  }, []);

  // 隐藏模态框
  const hideModal = useCallback(() => {
    setShowModal(false);
  }, []);

  // 重置同意设置（用于测试或用户请求）
  const resetConsent = useCallback(() => {
    localStorage.removeItem(CONSENT_STORAGE_KEY);
    setConsentState(null);
    setStatus('pending');
    setShowBanner(true);
    setShowModal(false);
  }, []);

  // 检查特定类型的同意
  const hasConsent = useCallback((type: keyof Omit<CookieConsent, 'timestamp' | 'version'>) => {
    return consent?.[type] ?? false;
  }, [consent]);

  // 初始化
  useEffect(() => {
    loadConsent();
  }, [loadConsent]);

  return {
    consent,
    status,
    showBanner,
    showModal,
    acceptAll,
    rejectAll,
    customizeConsent,
    showCustomizeModal,
    hideBanner,
    hideModal,
    resetConsent,
    hasConsent,
    isAnalyticsEnabled: hasConsent('analytics'),
    isMarketingEnabled: hasConsent('marketing'),
    isPreferencesEnabled: hasConsent('preferences')
  };
};
