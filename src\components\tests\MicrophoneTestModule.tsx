import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Play, Square, Circle, Settings, ChevronDown, ChevronUp, Headphones, Volume2, Clock, Pause } from 'lucide-react';
import { GlassCard } from '@/components/ui/GlassCard';
import { PrimaryButton } from '@/components/ui/PrimaryButton';
import { useMicrophone, AudioConstraints } from '@/hooks/useMicrophone';
import { useLanguage } from '@/hooks/useLanguage';

interface MicrophoneTestModuleProps {
  onNext?: () => void;
  onBack?: () => void;
}

// 格式化时间显示（秒转换为 mm:ss 格式）
const formatTime = (seconds: number): string => {
  // 检查输入是否为有效数字
  if (!seconds || isNaN(seconds) || !isFinite(seconds)) {
    return '00:00';
  }
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const MicrophoneTestModule: React.FC<MicrophoneTestModuleProps> = ({ onNext, onBack }) => {
  const { t } = useLanguage();
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>('');
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [audioConstraints, setAudioConstraints] = useState<AudioConstraints>({
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
  });

  const {
    permissionState,
    devices,
    volume,
    technicalDetails,
    isRecording,
    audioUrl,
    frequencyData,
    startTest,
    stopTest,
    startRecording,
    stopRecording,
    playRecording,
    error,
    // 录音计时器和播放进度
    recordingDuration,
    isPlaying,
    playbackProgress,
    playbackDuration,
    stopPlayback,
    // 实时监听功能
    isMonitoring,
    monitorVolume,
    startMonitoring,
    stopMonitoring,
    setMonitorVolume,
    startMonitoringWithDelay, // 新增带延迟的监听函数
    updateMonitoringDelay, // 实时更新音频延迟
  } = useMicrophone({ selectedDeviceId, audioConstraints });

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 实时监听设置
  const [monitorSettings, setMonitorSettings] = useState({
    isExpanded: false,
    delay: 0, // 音频延迟（秒）
    selectedSpeaker: '',
    volume: 0.5,
  });
  const [outputDevices, setOutputDevices] = useState<MediaDeviceInfo[]>([]);

  // 获取输出设备列表
  const getOutputDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
      setOutputDevices(audioOutputs);
      
      if (audioOutputs.length > 0 && !monitorSettings.selectedSpeaker) {
        setMonitorSettings(prev => ({
          ...prev,
          selectedSpeaker: audioOutputs[0].deviceId
        }));
      }
    } catch (err) {
      console.error('Error getting output devices:', err);
    }
  };

  // 简化的监听切换函数
  const toggleMonitoring = () => {
    console.log('[MicrophoneTestModule] toggleMonitoring called. isMonitoring:', isMonitoring);
    if (isMonitoring) {
      // 关闭监听并收起面板
      stopMonitoring();
      setMonitorSettings(prev => ({ ...prev, isExpanded: false }));
    } else {
      // 检查权限
      if (permissionState !== 'granted') {
        const errorMsg = t("micInstructionsStart");
        console.error(`[MicrophoneTestModule] ${errorMsg}`);
        setLocalError(errorMsg);
        return;
      }
      
      // 展开面板并立即开始监听
      setMonitorSettings(prev => ({ ...prev, isExpanded: true }));
      setLocalError(null);
      
      // 设置监听音量并启动监听
      setMonitorVolume(monitorSettings.volume);
      const delayInSeconds = monitorSettings.delay / 1000;
      console.log(`[MicrophoneTestModule] Starting monitoring with audio delay: ${delayInSeconds}s`);
      startMonitoringWithDelay(delayInSeconds);
    }
  };

  // 更新监听设置
  const updateMonitorSettings = (key: keyof typeof monitorSettings, value: any) => {
    setMonitorSettings(prev => ({ ...prev, [key]: value }));
  };

  // 初始化
  useEffect(() => {
    getOutputDevices();
  }, []);

  // Draw user-friendly audio waveform
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !frequencyData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const draw = () => {
      const width = canvas.width;
      const height = canvas.height;
      
      ctx.clearRect(0, 0, width, height);

      // Background with subtle grid
      ctx.fillStyle = 'rgba(255, 255, 255, 0.03)';
      ctx.fillRect(0, 0, width, height);
      
      // Draw center line
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(0, height / 2);
      ctx.lineTo(width, height / 2);
      ctx.stroke();

      // Create a simplified waveform from frequency data
      const numBars = Math.min(32, frequencyData.length / 8); // Reduce complexity
      const barWidth = width / numBars;
      
      ctx.beginPath();
      ctx.moveTo(0, height / 2);

      for (let i = 0; i < numBars; i++) {
        // Average multiple frequency bins for smoother appearance
        let sum = 0;
        const startIndex = Math.floor((i / numBars) * frequencyData.length);
        const endIndex = Math.floor(((i + 1) / numBars) * frequencyData.length);
        
        for (let j = startIndex; j < endIndex; j++) {
          sum += frequencyData[j];
        }
        const average = sum / (endIndex - startIndex);
        
        // Convert to waveform height (centered around middle)
        const waveHeight = (average / 255) * (height * 0.4); // Max 40% of height
        const x = i * barWidth;
        const y = height / 2 + (Math.sin(i * 0.5) * waveHeight * 0.5); // Add some wave-like movement
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      // Style the waveform line
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, 'rgba(59, 130, 246, 0.8)'); // Blue
      gradient.addColorStop(0.5, 'rgba(16, 185, 129, 0.8)'); // Green
      gradient.addColorStop(1, 'rgba(245, 158, 11, 0.8)'); // Amber

      ctx.strokeStyle = gradient;
      ctx.lineWidth = 3;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.stroke();

      // Add glow effect
      ctx.shadowColor = 'rgba(59, 130, 246, 0.5)';
      ctx.shadowBlur = 10;
      ctx.stroke();
      ctx.shadowBlur = 0;

      // Draw activity indicators (dots that pulse with audio)
      const activityLevel = frequencyData.reduce((sum, val) => sum + val, 0) / frequencyData.length;
      if (activityLevel > 10) {
        for (let i = 0; i < 5; i++) {
          const x = (width / 6) * (i + 1);
          const y = height / 2;
          const radius = 2 + (activityLevel / 255) * 3;
          
          ctx.beginPath();
          ctx.arc(x, y, radius, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(16, 185, 129, ${0.3 + (activityLevel / 255) * 0.7})`;
          ctx.fill();
        }
      }
    };

    draw();
  }, [frequencyData]);

  // Get volume color based on dBFS level
  const getVolumeColor = (dbfs: number) => {
    if (dbfs <= -25) return 'bg-blue-500';
    if (dbfs <= -8) return 'bg-green-500';
    return 'bg-red-500';
  };

  // Get volume text color with flash effect for overload
  const getVolumeTextColor = (dbfs: number) => {
    if (dbfs <= -25) return 'text-blue-400';
    if (dbfs <= -8) return 'text-green-400';
    return 'text-red-400 animate-pulse';
  };

  // Get volume status text
  const getVolumeStatus = (dbfs: number) => {
    if (dbfs <= -25) return t('lowLevel');
    if (dbfs <= -8) return t('goodLevel');
    return t('overload');
  };

  // Get volume status color
  const getVolumeStatusColor = (dbfs: number) => {
    if (dbfs <= -25) return 'text-blue-300';
    if (dbfs <= -8) return 'text-green-300';
    return 'text-red-300 animate-pulse';
  };

  // Convert dBFS to percentage for progress bar (0% = -60dB, 100% = 0dB)
  const dbfsToPercent = (dbfs: number) => {
    return Math.max(0, Math.min(100, ((dbfs + 60) / 60) * 100));
  };

  const handleConstraintChange = async (key: keyof AudioConstraints, value: boolean) => {
    // 更新状态
    const newConstraints = { ...audioConstraints, [key]: value };
    setAudioConstraints(newConstraints);
    
    // 如果测试正在进行中，则使用新设置重启测试
    if (permissionState === 'granted') {
      setLocalError(null);
      try {
        // 显示正在应用新设置的提示
        const settingNames = {
          echoCancellation: t('echoCancellation'),
          noiseSuppression: t('noiseSuppression'),
          autoGainControl: t('autoGainControl')
        };
        
        // 可以在这里添加一个临时的加载状态，但为了保持简洁，我们直接重启
        console.log(`[MicrophoneTestModule] ${t('applyingNewSettings')}${settingNames[key]}: ${value ? t('toggleOn') : t('toggleOff')}`);
        
        // 使用新的约束重新开始测试
        await startTest();
      } catch (err) {
        console.error('[MicrophoneTestModule] 应用新设置时出错:', err);
        setLocalError(t('applySettingsError'));
      }
    }
  };

  return (
    <GlassCard className="max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-semibold text-white">{t("microphoneAccess")}</h2>
          
          {devices.length > 0 && permissionState === 'granted' && (
            <select
              value={selectedDeviceId}
              onChange={(e) => setSelectedDeviceId(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-400/50 backdrop-blur-sm"
            >
              {devices.map((device) => (
                <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                  {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                </option>
              ))}
            </select>
          )}
        </div>
        
        {/* 右上角测试控制按钮 */}
        <div className="flex items-center space-x-3">
          {permissionState === 'granted' ? (
            <PrimaryButton onClick={stopTest} variant="outline" size="sm">
              <MicOff className="h-4 w-4" />
              {t("stopTest")}
            </PrimaryButton>
          ) : (
            <PrimaryButton onClick={startTest} size="sm">
              <Mic className="h-4 w-4" />
              {t("startMicrophoneTest")}
            </PrimaryButton>
          )}
        </div>
      </div>

      {(error || localError) && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error || localError}</p>
        </div>
      )}

      {/* Permission Denied State */}
      {permissionState === 'denied' && (
        <div className="text-center py-8">
          <MicOff className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">{t('micPermissionDeniedTitle')}</h3>
          <p className="text-white/70 mb-4">
            {t('micPermissionDeniedMessage')}
          </p>
          <div className="bg-amber-500/20 border border-amber-400/50 rounded-xl p-4 mb-4 text-left">
            <p className="text-amber-200 text-sm">
              <strong>{t('enableMicPermissionInstructions')}</strong>
            </p>
            <ul className="text-amber-200/80 text-sm mt-2 space-y-1">
              <li>{t('enableMicStep1')}</li>
              <li>{t('enableMicStep2')}</li>
              <li>{t('enableMicStep3')}</li>
            </ul>
          </div>
          <PrimaryButton onClick={startTest}>
            {t('retryTest')}
          </PrimaryButton>
        </div>
      )}

      {/* Main Test Interface - 总是显示，无论是否已开始测试 */}
      <div className="space-y-6">
        {/* 当没有权限时，显示提示 */}
        {permissionState !== 'granted' && permissionState !== 'denied' && (
          <div className="bg-blue-500/20 border border-blue-400/50 rounded-xl p-4 mb-6 text-center">
            <p className="text-blue-200 text-sm mb-2">
              <strong>{t("testContent")}</strong>
            </p>
            <ul className="text-blue-200/80 text-sm space-y-1">
              <li>{t("testContentItem1")}</li>
              <li>{t("testContentItem2")}</li>
              <li>{t("testContentItem3")}</li>
              <li>{t("testContentItem4")}</li>
            </ul>
          </div>
        )}
          {/* Audio Visualization Section */}
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("audioVisualization")}
            </label>
            <div className="relative">
              <canvas
                ref={canvasRef}
                width={800}
                height={200}
                className="w-full h-32 rounded-xl border border-white/20 bg-black/20"
              />
              <div className="absolute top-2 left-2 text-xs text-white/60 bg-black/40 px-2 py-1 rounded">
                {t("realTimeWaveform")}
              </div>
              {/* Activity indicator when no audio */}
            {(!frequencyData || frequencyData.reduce((sum, val) => sum + val, 0) / frequencyData.length < 10) && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-white/40 text-center">
                  <div className="text-2xl mb-2">🎤</div>
                  <div className="text-sm">
                    {permissionState === 'granted' ? t("startSpeaking") : t("startTestingButton")}
                  </div>
                </div>
              </div>
            )}
            </div>
            <div className="mt-2 text-xs text-white/50 text-center bg-white/5 p-2 rounded-lg">
              {t("usageTip")}
            </div>
          </div>

          {/* Status & Volume Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Status Indicator */}
            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">
                {t("microphoneStatus")}
              </label>
              <div className="p-3 bg-white/5 rounded-xl border border-white/10">
              <div className="flex items-center space-x-2 mb-2">
                <div className={`h-3 w-3 rounded-full ${
                  permissionState === 'granted' ? 'bg-green-400 animate-pulse' : 'bg-gray-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  permissionState === 'granted' ? 'text-green-400' : 'text-gray-400'
                }`}>
                  {permissionState === 'granted' ? t("micConnected") : t("waitingConnection")}
                </span>
              </div>
              <p className="text-white/60 text-xs">
                {permissionState === 'granted' ? t("speakIntoMic") : t("startTestingButton")}
              </p>
              <div className="text-center mt-2">
                <span className={`text-xs ${
                  permissionState === 'granted' ? 'text-green-300' : 'text-gray-300'
                }`}>
                  {permissionState === 'granted' ? t("deviceWorking") : t("deviceReady")}
                </span>
              </div>
            </div>
            </div>

            {/* Volume Meter */}
            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">
                {t("volumeDetection")}
              </label>
              <div className={`p-3 bg-white/5 rounded-xl border transition-all duration-200 ${
              permissionState === 'granted' && volume > -8 ? 'border-red-400/50 bg-red-500/5 animate-pulse' : 'border-white/10'
            }`}>
                <div className="flex items-center space-x-3 mb-2">
                  <div className="flex-1 bg-white/10 rounded-full h-3 overflow-hidden relative">
                    {/* Background gradient zones */}
                    <div className="absolute inset-0 flex">
                      <div className="bg-blue-500/30 flex-1" style={{ width: '58.33%' }}></div>
                      <div className="bg-green-500/30 flex-1" style={{ width: '28.33%' }}></div>
                      <div className="bg-red-500/30 flex-1" style={{ width: '13.33%' }}></div>
                    </div>
                    {/* Volume indicator */}
                    <div
                    className={`h-full transition-all duration-100 ${
                      permissionState === 'granted' ? getVolumeColor(volume) : 'bg-gray-500'
                    } relative z-10 ${
                      permissionState === 'granted' && volume > -8 ? 'animate-pulse' : ''
                    }`}
                    style={{ width: `${permissionState === 'granted' ? Math.max(1, dbfsToPercent(volume)) : 1}%` }}
                  />
                  </div>
                  <span className={`text-sm font-mono ${
                  permissionState === 'granted' ? getVolumeTextColor(volume) : 'text-gray-400'
                } min-w-[4rem] font-semibold`}>
                  {permissionState === 'granted' ? (volume <= -60 ? '-∞' : Math.round(volume)) : '-∞'} dB
                </span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-300">{t("dbLow")}</span>
                  <span className={`font-medium ${
                  permissionState === 'granted' ? getVolumeStatusColor(volume) : 'text-gray-400'
                } px-2 py-1 rounded-md ${
                  permissionState === 'granted' && volume > -8 ? 'bg-red-500/20 border border-red-400/30' : ''
                }`}>
                  {permissionState === 'granted' ? getVolumeStatus(volume) : t('notDetected')}
                </span>
                  <span className="text-red-300">{t("dbOverload")}</span>
                </div>
                <div className="text-center mt-1">
                <span className={`text-xs ${
                  permissionState === 'granted' ? 'text-green-300' : 'text-gray-400'
                }`}>
                  {permissionState === 'granted' ? t("goodRange") : t('waitingTesting')}
                </span>
              </div>
              </div>
            </div>
          </div>

          {/* 实时监听区域 */}
          <div className="bg-white/5 rounded-xl border border-white/10 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Headphones className="h-5 w-5 text-purple-400" />
                <h3 className="text-white/80 font-medium">{t("realTimeMonitoring")}</h3>
                <div className="flex items-center space-x-2">
                  {isMonitoring ? (
                    <>
                      <div className="h-2 w-2 bg-purple-400 rounded-full animate-pulse"></div>
                      <span className="text-purple-300 text-sm font-medium">{t("monitoring")}</span>
                    </>
                  ) : (
                    <span className="text-white/50 text-sm">{t("closed")}</span>
                  )}
                </div>
              </div>
              
              <button
              onClick={toggleMonitoring}
              disabled={permissionState !== 'granted'}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isMonitoring ? 'bg-purple-500' : 'bg-white/20'
              } ${permissionState !== 'granted' ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isMonitoring ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            </div>

            <p className="text-white/60 text-sm mb-4">
              {t('monitoringDesc')} <span className="text-amber-300">({t('warningHeadphones')})</span>
            </p>

            {/* 监听控制面板 */}
            {monitorSettings.isExpanded && (
              <div className="bg-white/5 rounded-lg border border-purple-400/30 p-4 space-y-3">
                {/* 警告提示 */}
                <div className="bg-amber-500/20 border border-amber-400/50 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Headphones className="h-4 w-4 text-amber-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-amber-200 text-sm font-medium mb-1">
                        {t('warningHeadphones')}
                      </p>
                      <p className="text-amber-200/80 text-sm">
                        {t('preventFeedback')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 音频延迟设置 - 紧凑版 */}
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-blue-400 flex-shrink-0" />
                  <span className="text-white/70 text-sm min-w-[60px]">{t('realTimeAudioDelay')}</span>
                  <input
                    type="range"
                    min="0"
                    max="2000"
                    step="100"
                    value={monitorSettings.delay}
                    onChange={(e) => {
                      const newDelay = parseInt(e.target.value);
                      updateMonitorSettings('delay', newDelay);
                      // 如果正在监听，实时更新延迟
                      if (isMonitoring) {
                        const delayInSeconds = newDelay / 1000;
                        console.log(`[MicrophoneTestModule] Updating audio delay to: ${delayInSeconds}s`);
                        updateMonitoringDelay(delayInSeconds);
                      }
                    }}
                    className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider-thumb"
                  />
                  <span className="text-blue-300 text-sm font-mono min-w-[40px]">
                    {(monitorSettings.delay / 1000).toFixed(1)}{t('seconds')}
                  </span>
                  <span className="text-xs text-white/40">{t('preventSoundOverlap')}</span>
                </div>

                {/* 监听音量设置 - 紧凑版 */}
                <div className="flex items-center space-x-3">
                  <Volume2 className="h-4 w-4 text-purple-400 flex-shrink-0" />
                  <span className="text-white/70 text-sm min-w-[60px]">{t('monitoringVolume')}</span>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={isMonitoring ? monitorVolume : monitorSettings.volume}
                    onChange={(e) => {
                      const newVolume = parseFloat(e.target.value);
                      updateMonitorSettings('volume', newVolume);
                      // 如果正在监听，实时更新音量
                      if (isMonitoring) {
                        setMonitorVolume(newVolume);
                      }
                    }}
                    className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider-thumb"
                  />
                  <span className="text-purple-300 text-sm font-mono min-w-[40px]">
                    {Math.round((isMonitoring ? monitorVolume : monitorSettings.volume) * 100)}%
                  </span>
                  <span className="text-xs text-white/40">{t('notAffectRecording')}</span>
                </div>

                {/* 扬声器选择 */}
                {outputDevices.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <Volume2 className="h-4 w-4 text-green-400 flex-shrink-0" />
                    <span className="text-white/70 text-sm min-w-[60px]">{t('speaker')}</span>
                    <select
                      value={monitorSettings.selectedSpeaker}
                      onChange={(e) => updateMonitorSettings('selectedSpeaker', e.target.value)}
                      className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-1.5 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400/50"
                    >
                      {outputDevices.map((device) => (
                        <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                          {device.label || `Speaker ${device.deviceId.slice(0, 8)}`}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* 监听状态提示 */}
                <div className="flex items-center justify-center pt-2 border-t border-white/10">
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <span className="text-purple-200 text-sm font-medium">{t('monitoringEnabled')}</span>
                    <span className="text-xs text-white/40">{t('adjustAnytime')}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Record & Playback Controls */}
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("recordingTest")}
            </label>
            <div className="bg-white/5 rounded-xl border border-white/10 p-4">
              {/* 录音控制按钮 */}
              <div className="flex items-center space-x-3 mb-4">
                <PrimaryButton
                onClick={startRecording}
                disabled={isRecording || isPlaying || permissionState !== 'granted'}
                size="sm"
              >
                <Circle className="h-4 w-4 fill-current" />
                {t("startRecording")}
              </PrimaryButton>
                
                <PrimaryButton
                  onClick={stopRecording}
                  disabled={!isRecording}
                  variant="secondary"
                  size="sm"
                >
                  <Square className="h-4 w-4" />
                  {t("stopRecording")}
                </PrimaryButton>
                
                <PrimaryButton
                  onClick={isPlaying ? stopPlayback : playRecording}
                  disabled={!audioUrl}
                  variant="outline"
                  size="sm"
                >
                  {isPlaying ? (
                    <>
                      <Pause className="h-4 w-4" />
                      {t('playbackStopped')}
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      {t("playRecording")}
                    </>
                  )}
                </PrimaryButton>

                {/* 录音状态指示器 */}
                {isRecording && (
                  <div className="flex items-center space-x-2 ml-4">
                    <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-red-400 text-sm font-mono">
                      {t('recording')} {formatTime(recordingDuration)}
                    </span>
                  </div>
                )}

                {/* 播放状态指示器 */}
                {isPlaying && (
                  <div className="flex items-center space-x-2 ml-4">
                    <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="text-blue-400 text-sm font-mono">
                      {t('playingInProgress')} {formatTime((playbackProgress || 0) * (playbackDuration || 0))} / {formatTime(playbackDuration || 0)}
                    </span>
                  </div>
                )}
              </div>

              {/* 播放进度条 */}
              {audioUrl && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-xs text-white/60 mb-1">
                    <span>{t('playbackProgress')}</span>
                    <span>{formatTime(playbackDuration || 0)}</span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                    <div
                      className={`h-full transition-all duration-100 rounded-full ${
                        isPlaying ? 'bg-blue-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min((playbackProgress || 0) * 100, 100)}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs text-white/40 mt-1">
                    <span>{formatTime((playbackProgress || 0) * (playbackDuration || 0))}</span>
                    <span className="text-green-300">
                      {(playbackProgress || 0) >= 1 ? t('playbackComplete') : isPlaying ? t('playingInProgress') + '...' : t('paused')}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <div className="text-xs text-white/60 bg-white/5 p-2 rounded-lg">
                💡 <strong>{t('recordingInstructions')}</strong>
              </div>
                {showAdvanced && (
                  <div className="text-xs text-amber-200/80 bg-amber-500/10 border border-amber-400/30 p-2 rounded-lg">
                  🔧 <strong>{t('advancedTest')}</strong>
                </div>
                )}
              </div>
            </div>
          </div>

          {/* Technical Details & Advanced Settings Footer */}
          <div className="bg-white/5 rounded-xl border border-white/10 p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-white/80 font-medium mb-2">{t('microphoneInformation')}</h4>
                {technicalDetails ? (
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-white/60">{t('sampleRate')}</span>
                    <span className="text-white ml-2">{technicalDetails.sampleRate} Hz</span>
                  </div>
                  <div>
                    <span className="text-white/60">{t('channels')}</span>
                    <span className="text-white ml-2">{technicalDetails.channels === 1 ? t('mono') : t('stereo')}</span>
                  </div>
                  <div>
                    <span className="text-white/60">{t('bitDepth')}</span>
                    <span className="text-white ml-2">{technicalDetails.bitDepth} bit</span>
                  </div>
                </div>
              ) : (
                <div className="text-white/60 text-sm">
                  {permissionState !== 'granted' ? t('pleaseTurnOnTest') : t('gettingDeviceInfo')}
                </div>
              )}
              </div>
              
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-white/70 hover:text-white transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span className="text-sm">{t('advancedSettings')}</span>
                {showAdvanced ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* Advanced Audio Settings */}
            {showAdvanced && (
              <div className="border-t border-white/10 pt-4">
                {/* 使用提示区域 */}
                <div className="bg-blue-500/20 border border-blue-400/50 rounded-xl p-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-6 w-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">💡</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-blue-200 font-medium text-sm mb-2">{t('whyAdjustSettings')}</h4>
                      <div className="space-y-2 text-blue-200/90 text-sm">
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-300 font-medium min-w-[80px]">{t('echoCancellation')}：</span>
                          <span>{t('echoWhenUsingSpeaker')}</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-300 font-medium min-w-[80px]">{t('noiseSuppression')}：</span>
                          <span>{t('noisyBackground')}</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-300 font-medium min-w-[80px]">{t('autoGainControl')}：</span>
                          <span>{t('unstableVolume')}</span>
                        </div>
                      </div>
                      <div className="mt-3 p-2 bg-blue-400/20 rounded-lg">
                        <p className="text-blue-100 text-xs">
                          <strong>{t('settingsApplyImmediately')}</strong>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  {/* Echo Cancellation */}
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white/80 text-sm font-medium">{t('echoCancellation')}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          audioConstraints.echoCancellation 
                            ? 'bg-green-500/20 text-green-300 border border-green-400/30' 
                            : 'bg-gray-500/20 text-gray-300 border border-gray-400/30'
                        }`}>
                          {audioConstraints.echoCancellation ? t('toggleOn') : t('toggleOff')}
                        </span>
                      </div>
                      <p className="text-white/60 text-xs">
                        {t('preventEcho')}
                      </p>
                      {!audioConstraints.echoCancellation && (
                        <p className="text-amber-300 text-xs mt-1">
                          {t('useHeadphonesToPreventEcho')}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => handleConstraintChange('echoCancellation', !audioConstraints.echoCancellation)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        audioConstraints.echoCancellation ? 'bg-blue-500' : 'bg-white/20'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          audioConstraints.echoCancellation ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {/* Noise Suppression */}
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white/80 text-sm font-medium">{t('noiseSuppression')}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          audioConstraints.noiseSuppression 
                            ? 'bg-green-500/20 text-green-300 border border-green-400/30' 
                            : 'bg-gray-500/20 text-gray-300 border border-gray-400/30'
                        }`}>
                          {audioConstraints.noiseSuppression ? t('toggleOn') : t('toggleOff')}
                        </span>
                      </div>
                      <p className="text-white/60 text-xs">
                        {t('filterNoise')}
                      </p>
                      {audioConstraints.noiseSuppression && (
                        <p className="text-green-300 text-xs mt-1">
                          {t('improveCallQuality')}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => handleConstraintChange('noiseSuppression', !audioConstraints.noiseSuppression)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        audioConstraints.noiseSuppression ? 'bg-blue-500' : 'bg-white/20'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          audioConstraints.noiseSuppression ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {/* Auto Gain Control */}
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white/80 text-sm font-medium">{t('autoGainControl')}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          audioConstraints.autoGainControl 
                            ? 'bg-green-500/20 text-green-300 border border-green-400/30' 
                            : 'bg-gray-500/20 text-gray-300 border border-gray-400/30'
                        }`}>
                          {audioConstraints.autoGainControl ? t('toggleOn') : t('toggleOff')}
                        </span>
                      </div>
                      <p className="text-white/60 text-xs">
                        {t('autoAdjustVolume')}
                      </p>
                      {!audioConstraints.autoGainControl && (
                        <p className="text-blue-300 text-xs mt-1">
                          {t('hearNaturalVolumeChanges')}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => handleConstraintChange('autoGainControl', !audioConstraints.autoGainControl)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        audioConstraints.autoGainControl ? 'bg-blue-500' : 'bg-white/20'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          audioConstraints.autoGainControl ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* 测试建议区域 */}
                <div className="mt-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-400/30 rounded-xl p-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <span className="text-2xl">🧪</span>
                    </div>
                    <div>
                      <h4 className="text-purple-200 font-medium text-sm mb-2">{t('testSuggestionsTitle')}</h4>
                      <div className="space-y-2 text-purple-200/90 text-xs">
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-300">1.</span>
                          <span>{t('defaultSettingsFirst')}</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-300">2.</span>
                          <span>{t('compareEachSetting')}</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-300">3.</span>
                          <span>{t('noiseSuppressionTip')}</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-300">4.</span>
                          <span>{t('echoCancellationTip')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

        </div>

      {/* Navigation Buttons */}
      {(onBack || onNext) && (
        <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
          {onBack && (
            <PrimaryButton onClick={onBack} variant="outline">
              {t('returnButton')}
            </PrimaryButton>
          )}
          {onNext && (
            <PrimaryButton onClick={onNext}>
              {t("nextSpeakerTest")}
            </PrimaryButton>
          )}
        </div>
      )}
    </GlassCard>
  );
};