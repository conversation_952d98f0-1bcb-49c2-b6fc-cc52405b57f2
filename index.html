<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#1e40af" />
    <meta name="robots" content="index, follow" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon.svg" />
    <meta name="msapplication-TileColor" content="#1e40af" />
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 基础SEO信息 - 这些会被React动态更新 -->
    <title>Setup Check | Camera, Microphone, Speaker, Keyboard, Mouse Test</title>
    <meta name="description" content="Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality. Perfect for online meetings and gaming setups." />
    <meta name="keywords" content="device test, camera test, microphone test, speaker test, keyboard test, mouse test, network test, online meeting, hardware check, gaming setup, setup check" />
    <meta name="author" content="Setup Check" />

    <!-- Open Graph SEO -->
    <meta property="og:title" content="Setup Check" />
    <meta property="og:description" content="Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/设备测试网站Logo-简化版(1).png" />
    <meta property="og:site_name" content="Setup Check" />

    <!-- Twitter Card SEO -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Setup Check" />
    <meta name="twitter:description" content="Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality." />
    <meta name="twitter:image" content="/设备测试网站Logo-简化版(1).png" />
    
    <!-- 多语言支持 -->
    <link rel="alternate" hreflang="en" href="/" />
    <link rel="alternate" hreflang="zh" href="/zh" />
    <link rel="alternate" hreflang="es" href="/es" />
    <link rel="alternate" hreflang="de" href="/de" />
    <link rel="alternate" hreflang="ja" href="/ja" />
    <link rel="alternate" hreflang="ko" href="/ko" />
    <link rel="alternate" hreflang="x-default" href="/" />
    
    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Setup Check",
      "description": "Professional device testing platform for cameras, microphones, speakers, keyboards, mice, and network quality.",
      "applicationCategory": "UtilityApplication",
      "operatingSystem": "Web Browser",
      "url": "/",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Camera Testing",
        "Microphone Testing",
        "Speaker Testing", 
        "Keyboard Testing",
        "Mouse Testing",
        "Network Testing"
      ]
    }
    </script>
    <!-- Google Analytics 4 - 性能优化的预连接 -->
    <link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>
    <link rel="dns-prefetch" href="https://www.google-analytics.com">
    <link rel="dns-prefetch" href="https://stats.g.doubleclick.net">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
