/**
 * SEO翻译模块统一导出
 * 提供向后兼容的接口和工具函数
 */

import type { SEOTranslations, SupportedLanguage } from './types';
import { zhTranslation } from './zh';
import { enTranslation } from './en';
import { esTranslation } from './es';
import { deTranslation } from './de';
import { jaTranslation } from './ja';
import { koTranslation } from './ko';
import { frTranslation } from './fr';

// 重新构建完整的SEO翻译对象，保持向后兼容
export const seoTranslations: SEOTranslations = {
  zh: zhTranslation,
  en: enTranslation,
  es: esTranslation,
  de: deTranslation,
  ja: jaTranslation,
  ko: koTranslation,
  fr: frTranslation
};

/**
 * 获取SEO翻译的辅助函数 - 保持向后兼容
 * @param language 语言代码
 * @param key 翻译键，支持点分隔的嵌套键
 * @returns 翻译值或原键
 */
export const getSEOTranslation = (language: string, key: string): any => {
  const lang = seoTranslations[language] || seoTranslations['en'];
  const keys = key.split('.');
  let value: any = lang;

  for (const k of keys) {
    value = value?.[k];
  }

  return value || key;
};

// 导出类型定义
export * from './types';

// 导出各语言模块
export { zhTranslation } from './zh';
export { enTranslation } from './en';
export { esTranslation } from './es';
export { deTranslation } from './de';
export { jaTranslation } from './ja';
export { koTranslation } from './ko';
export { frTranslation } from './fr';
