/**
 * 한국어 - 기술 용어집
 * 장치 테스트와 관련된 기술 용어 정의 포함
 */

import type { GlossaryTranslation } from '../types';

export const koGlossary: GlossaryTranslation = {
  title: "기술 용어집",
  terms: {
    resolution: {
      title: "해상도",
      description: "1920x1080과 같은 비디오 픽셀 크기, 값이 높을수록 화질이 좋음"
    },
    frameRate: {
      title: "프레임 속도",
      description: "초당 표시되는 이미지 프레임 수, 일반적으로 fps로 표현되며 비디오 부드러움에 영향"
    },
    latency: {
      title: "지연 시간",
      description: "데이터 전송의 시간 지연, 밀리초(ms)로 측정되며 낮을수록 좋음"
    },
    bandwidth: {
      title: "대역폭",
      description: "네트워크 전송 용량, 일반적으로 Mbps로 측정되며 데이터 전송 속도를 결정"
    },
    sampleRate: {
      title: "샘플 속도",
      description: "초당 수집되는 오디오 샘플 수, 일반적인 속도는 44.1kHz, 48kHz"
    },
    bitRate: {
      title: "비트 속도",
      description: "오디오 또는 비디오 데이터 전송 속도, 품질과 파일 크기에 영향"
    },
    dpi: {
      title: "DPI",
      description: "마우스 감도 단위, 인치당 이동 픽셀 수를 나타냄"
    },
    pollingRate: {
      title: "폴링 속도",
      description: "장치가 컴퓨터에 상태를 보고하는 빈도, Hz로 측정되며 높을수록 응답이 빠름"
    },
    fps: {
      title: "프레임 속도 (FPS)",
      description: "초당 표시되는 이미지 프레임 수, 비디오 부드러움과 품질에 영향"
    },
    megapixel: {
      title: "메가픽셀",
      description: "디지털 이미지의 기본 단위, 메가픽셀이 이미지 선명도를 결정"
    },
    exposure: {
      title: "노출",
      description: "카메라의 빛 감도 수준, 이미지 밝기와 선명도에 영향"
    },
    noiseReduction: {
      title: "노이즈 감소",
      description: "오디오에서 노이즈와 배경음을 제거하는 기술"
    },
    sensitivity: {
      title: "감도",
      description: "마이크가 음향 신호를 감지하는 능력"
    },
    frequency: {
      title: "주파수",
      description: "음향 또는 전기 신호의 진동 수, Hz로 측정"
    },
    impedance: {
      title: "임피던스",
      description: "오디오 장치의 전류에 대한 저항, 전력 매칭에 영향"
    },
    soundStage: {
      title: "사운드스테이지",
      description: "오디오의 공간감과 위치, 음질 층을 반영"
    },
    drivers: {
      title: "드라이버",
      description: "헤드폰의 핵심 구성 요소로 전기 신호를 소리로 변환"
    },
    thd: {
      title: "총 고조파 왜곡",
      description: "오디오 신호 왜곡 수준의 측정 지표"
    },
    keyTravel: {
      title: "키 이동",
      description: "키가 휴지 위치에서 작동까지 이동하는 거리"
    },
    actuationForce: {
      title: "작동력",
      description: "키를 작동시키는 데 필요한 최소 압력"
    },
    tactile: {
      title: "촉각",
      description: "키가 트리거될 때의 촉각 피드백"
    },
    linear: {
      title: "선형",
      description: "키 압력이 이동 거리에 비례하는 특성"
    },
    polling: {
      title: "폴링",
      description: "시스템이 주기적으로 장치 상태를 확인하는 메커니즘"
    },
    acceleration: {
      title: "가속도",
      description: "빠른 움직임 시 마우스 응답 특성"
    },
    liftOffDistance: {
      title: "리프트오프 거리",
      description: "마우스가 여전히 감지하는 상태에서 들어올릴 수 있는 최대 높이"
    },
    tracking: {
      title: "추적",
      description: "마우스 센서가 움직임을 감지하는 능력"
    },
    jitter: {
      title: "지터",
      description: "네트워크 지연 시간 변동 정도, 안정성에 영향"
    },
    packetLoss: {
      title: "패킷 손실",
      description: "네트워크 전송 중 손실되는 데이터 패킷의 비율"
    },
    throughput: {
      title: "처리량",
      description: "네트워크 데이터 전송의 실제 속도"
    },
    codec: {
      title: "코덱",
      description: "오디오/비디오 데이터를 압축 및 압축 해제하는 알고리즘"
    },
    compression: {
      title: "압축",
      description: "대역폭 절약을 위해 데이터 크기를 줄이는 기술"
    },
    inputLag: {
      title: "입력 지연",
      description: "입력 작업에서 시스템 응답까지의 시간 차이"
    }
  }
};
