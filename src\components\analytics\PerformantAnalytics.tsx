import { useEffect, useRef } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useCookieConsent } from '@/hooks/useCookieConsent';

/**
 * 性能优化的分析组件
 * 只在用户同意且页面加载完成后初始化 GA4
 */
export const PerformantAnalytics: React.FC = () => {
  const { isAnalyticsEnabled } = useCookieConsent();
  const analytics = useAnalytics();
  const hasInitialized = useRef(false);

  useEffect(() => {
    // 只有在用户同意分析 Cookie 且尚未初始化时才加载
    if (!isAnalyticsEnabled || hasInitialized.current) {
      return;
    }

    // 等待页面完全加载后再初始化分析
    const initializeWhenReady = () => {
      if (document.readyState === 'complete') {
        // 使用 requestIdleCallback 在浏览器空闲时初始化
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            if (!hasInitialized.current) {
              hasInitialized.current = true;
              // analytics hook 会自动初始化 GA4
            }
          }, { timeout: 3000 });
        } else {
          // 降级到 setTimeout
          setTimeout(() => {
            if (!hasInitialized.current) {
              hasInitialized.current = true;
              // analytics hook 会自动初始化 GA4
            }
          }, 1000);
        }
      } else {
        // 如果页面还没加载完成，等待 load 事件
        window.addEventListener('load', initializeWhenReady, { once: true });
      }
    };

    initializeWhenReady();

    // 清理函数
    return () => {
      window.removeEventListener('load', initializeWhenReady);
    };
  }, [isAnalyticsEnabled, analytics]);

  // 这个组件不渲染任何内容
  return null;
};
