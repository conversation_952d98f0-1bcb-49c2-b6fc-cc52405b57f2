---
alwaysApply: true
description: 项目概览
---

# 项目概览

这是一个使用 Vite 构建的 React + TypeScript 单页应用。

## 技术栈

- **框架**: React 18
- **语言**: TypeScript
- **构建工具**: Vite
- **路由**: React Router v6
- **UI**: shadcn/ui (基于 Radix UI 和 Tailwind CSS)
- **表单**: React Hook Form with Zod
- **服务端状态管理**: Tanstack Query (React Query)
- **样式**: Tailwind CSS

## 关键文件和目录

-   **`src/main.tsx`**: 应用主入口，渲染 `App` 根组件。
-   **`src/App.tsx`**: 根组件，负责设置路由。
-   **`src/pages/`**: 存放应用的各个页面。
-   **`src/components/`**: 存放可复用的 React 组件。
    -   `ui/`: shadcn/ui 风格的基础 UI 组件。
    -   `tests/`: 用于各项设备检测的特定业务组件。
    -   `layouts/`: 布局组件。
-   **`src/hooks/`**: 存放自定义 React Hooks。
-   **`src/lib/utils.ts`**: 存放工具函数，例如 `cn` 用于合并 Tailwind class。
-   **`package.json`**: 定义项目依赖和脚本。

-   **`src/lib/utils.ts`**: 存放工具函数，例如 `cn` 用于合并 Tailwind class。
-   **`package.json`**: 定义项目依赖和脚本。
