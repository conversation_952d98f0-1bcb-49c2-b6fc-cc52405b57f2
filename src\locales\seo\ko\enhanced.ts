/**
 * 한국어 - 향상된 SEO 번역
 * 사용 가이드, 기술 사양, 모범 사례, 업계 표준 포함
 */

import type { EnhancedTranslation } from '../types';

export const koEnhanced: EnhancedTranslation = {
  usageGuide: {
    title: "포괄적인 사용 가이드",
    tips: {
      title: "전문가 팁"
    },
    camera: {
      steps: [
        "'허용'을 클릭하여 브라우저가 카메라 장치에 액세스할 수 있도록 허용",
        "카메라 초기화를 기다리고 장치 상태 표시등이 활성화되어 있는지 확인",
        "미리보기 피드를 관찰하여 이미지 선명도, 색상 정확도, 노출을 확인",
        "밝은 조명과 저조도를 포함한 다양한 조명 조건에서 카메라 성능 테스트",
        "해상도, 프레임 속도, 전체 비디오 품질 평가를 포함한 테스트 결과 기록"
      ],
      tip: "자연광이 잘 드는 밝은 조명 조건에서 테스트하는 것을 권장합니다. 테스트 결과에 영향을 줄 수 있는 역광이나 너무 어두운 환경은 피하세요."
    },
    microphone: {
      steps: [
        "브라우저 마이크 권한을 허용하고 시스템 오디오 설정을 확인",
        "오디오 왜곡을 피하기 위해 적절한 녹음 볼륨으로 조정",
        "다양한 볼륨과 거리에서 10-15초 테스트 녹음 수행",
        "녹음을 재생하여 노이즈, 에코 또는 지연 문제에 대한 오디오 품질 확인"
      ],
      tip: "최상의 테스트 결과를 위해 조용한 환경에서 마이크를 사용하고 장치로부터 적절한 거리를 유지하세요."
    },
    headphones: {
      steps: [
        "헤드폰이 오디오 출력 장치에 올바르게 연결되어 있는지 확인",
        "시스템 볼륨을 편안한 청취 수준으로 조정",
        "좌우 채널 테스트 오디오를 재생하여 스테레오 밸런스 확인",
        "다양한 주파수의 오디오를 테스트하여 저음, 중음, 고음 성능 평가",
        "장시간 사용 편안함 테스트 수행"
      ],
      tip: "테스트 중 볼륨이 너무 높지 않도록 하세요. 장시간 고음량 청취는 청력을 손상시킬 수 있습니다."
    },
    keyboard: {
      steps: [
        "키보드가 올바르게 연결되고 시스템에서 인식되는지 확인",
        "모든 키를 순차적으로 눌러 응답과 피드백 확인",
        "Ctrl, Alt, Shift 및 기타 수정자 키와 같은 조합 키 기능 테스트",
        "연속 타이핑 테스트를 수행하여 키 바운스와 응답 속도 확인",
        "F1-F12 및 멀티미디어 제어 키와 같은 특수 기능 키 테스트"
      ],
      tip: "키가 끈적거리거나 더블 클릭되는지 관찰하세요. 이는 타이핑 효율성과 게임 경험에 영향을 줄 수 있습니다."
    },
    mouse: {
      steps: [
        "마우스 연결 상태와 드라이버 설치 확인",
        "좌클릭 및 우클릭 기능과 응답 속도 테스트",
        "마우스 휠의 부드러움과 정확도 확인",
        "다양한 표면에서 마우스 움직임의 정확도 테스트",
        "게이밍 마우스의 경우 추가 버튼과 DPI 조정 기능 테스트"
      ],
      tip: "마우스 패드를 사용하면 더 나은 추적 정확도를 제공할 수 있습니다. 게이밍 사용자는 고속 움직임 시 추적 안정성을 테스트해야 합니다."
    },
    network: {
      steps: [
        "네트워크 대역폭을 많이 소비하는 다른 애플리케이션 종료",
        "안정적인 네트워크 환경에 연결하고 모바일 핫스팟 사용 피하기",
        "테스트가 완료될 때까지 기다리고 테스트 중 다른 네트워크 활동 수행하지 않기",
        "다운로드 속도, 업로드 속도, 지연 시간 데이터 기록",
        "여러 번 테스트를 수행하고 평균을 내어 결과의 정확성 보장"
      ],
      tip: "네트워크 테스트 결과는 서버 부하 및 네트워크 혼잡과 같은 요인에 영향을 받을 수 있습니다. 더 정확한 네트워크 성능 평가를 위해 다른 시간에 여러 번 테스트를 수행하는 것이 좋습니다."
    },
    meeting: {
      steps: [
        "권장 순서대로 모든 장치 테스트를 순차적으로 완료",
        "각 장치의 테스트 결과와 기존 잠재적 문제 기록",
        "카메라와 마이크의 협업 작업 효과에 특별히 주의",
        "시뮬레이션된 회의 환경에서 전체 성능 테스트",
        "회의 전 참조를 위해 테스트 보고서 저장"
      ],
      tip: "중요한 회의 24시간 전에 완전한 장치 감지를 수행하여 발견된 문제를 처리할 충분한 시간을 확보하는 것이 좋습니다."
    },
    gaming: {
      steps: [
        "게이밍 관련 모든 하드웨어 장치를 순차적으로 테스트",
        "입력 장치의 응답 시간과 정확도에 집중",
        "오디오 장치의 게이밍 사운드 이펙트 성능과 음성 통신 품질 테스트",
        "네트워크 지연 시간이 경쟁 게이밍 요구 사항을 충족하는지 확인",
        "모든 테스트 데이터를 기록하고 장치 성능 프로필 설정"
      ],
      tip: "e스포츠 플레이어는 정기적인 장치 감지를 수행하여 장치 상태가 최적 수준에 있는지 확인해야 합니다. 게이밍 성능에서 키보드와 마우스 응답 시간의 중요성에 주목하세요."
    },
    tools: {
      steps: [
        "포괄적인 장치 테스트 도구 컬렉션 탐색",
        "테스트하려는 장치에 대한 특정 도구 선택",
        "각 도구의 상세한 테스트 지침 따르기",
        "다양한 장치와 구성 간 결과 비교",
        "하드웨어 최적화를 위한 전문 보고서 사용"
      ],
      tip: "각 도구는 특정 장치 유형을 위해 설계되었습니다. 포괄적인 테스트를 위해 모든 하드웨어 구성 요소가 최적으로 작동하는지 확인하기 위해 여러 도구 사용을 고려하세요."
    },
    home: {
      steps: [
        "필요에 적합한 테스트 시나리오 또는 개별 장치 테스트 선택",
        "시스템이 권장하는 순서로 장치 감지 수행",
        "각 테스트의 결과와 최적화 제안을 주의 깊게 읽기",
        "향후 참조를 위해 테스트 보고서 저장 또는 인쇄",
        "최적의 장치 성능을 유지하기 위해 정기적인 테스트 수행"
      ],
      tip: "다양한 사용 시나리오는 장치에 대해 다른 요구 사항을 가집니다. 실제 필요에 따라 가장 적합한 테스트 계획을 선택하세요."
    }
  },
  technicalSpecs: {
    title: "기술 사양 및 요구 사항",
    systemRequirements: "시스템 요구 사항",
    parameters: "기술 매개변수",
    compatibility: "호환성 정보",
    camera: {
      systemRequirements: [
        "Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+",
        "Chrome 88+, Firefox 85+, Safari 14+, Edge 88+",
        "USB 2.0 이상 사양을 지원하는 카메라 장치",
        "비디오 처리를 위한 최소 512MB 사용 가능한 메모리",
        "WebRTC 기술을 지원하는 최신 브라우저"
      ],
      parameters: {
        "지원 해상도": "480p-4K",
        "프레임 속도 범위": "15-60 FPS",
        "비디오 인코딩": "H.264, VP8, VP9",
        "최소 대역폭": "1 Mbps",
        "지연 시간": "< 100ms"
      },
      compatibilityNote: "대부분의 USB 카메라, 노트북 내장 카메라, 전문 카메라 장비와 호환됩니다. 다중 카메라 환경에서 자동 인식 및 전환을 지원합니다."
    },
    microphone: {
      systemRequirements: [
        "WebRTC Audio API를 지원하는 브라우저",
        "올바르게 작동하는 오디오 드라이버",
        "표준 오디오 인터페이스를 지원하는 마이크 장치",
        "시스템 오디오 서비스의 정상 작동",
        "실시간 오디오 처리를 위한 충분한 CPU 리소스"
      ],
      parameters: {
        "샘플 속도": "8kHz-48kHz",
        "비트 깊이": "16비트, 24비트",
        "채널": "모노/스테레오",
        "주파수 응답": "20Hz-20kHz",
        "신호 대 잡음비": "> 60dB"
      },
      compatibilityNote: "USB 마이크, 3.5mm 인터페이스 마이크, 무선 마이크 등의 유형을 지원합니다. 다양한 장치 오디오 특성에 자동으로 적응합니다."
    },
    headphones: {
      systemRequirements: [
        "올바르게 작동하는 오디오 출력 장치",
        "다중 채널 오디오를 지원하는 오디오 드라이버",
        "브라우저 오디오 권한 부여됨",
        "작동하는 오디오 코덱"
      ],
      parameters: {
        "주파수 응답": "20Hz-20kHz",
        "임피던스 범위": "16Ω-600Ω",
        "채널 분리": "> 40dB",
        "총 고조파 왜곡": "< 0.1%",
        "최대 SPL": "100dB SPL"
      },
      compatibilityNote: "유선 헤드폰, 무선 헤드폰, 스피커 및 모든 오디오 출력 장치와 호환됩니다. 스테레오, 5.1, 7.1 서라운드 사운드 테스트를 지원합니다."
    },
    keyboard: {
      systemRequirements: [
        "HID 키보드 장치를 지원하는 운영 체제",
        "올바르게 설치된 키보드 드라이버",
        "브라우저 키보드 이벤트 API 지원",
        "적절한 USB/PS2 인터페이스 전원 공급"
      ],
      parameters: {
        "응답 시간": "< 1ms",
        "키 수명": "5천만 클릭",
        "동시 키": "6키 롤오버",
        "폴링 속도": "1000Hz",
        "연결": "유선/무선"
      },
      compatibilityNote: "87키, 104키, 108키 사양을 포함한 모든 표준 키보드 레이아웃을 지원합니다. 기계식, 멤브레인, 정전용량식 키보드와 호환됩니다."
    },
    mouse: {
      systemRequirements: [
        "마우스 HID 프로토콜을 지원하는 시스템",
        "올바르게 작동하는 마우스 드라이버",
        "브라우저 마우스 이벤트 API 지원",
        "적절한 마우스 사용 표면"
      ],
      parameters: {
        "DPI 범위": "400-16000",
        "폴링 속도": "125-1000Hz",
        "가속도": "40G",
        "최대 속도": "400 IPS",
        "클릭 수명": "2천만 클릭"
      },
      compatibilityNote: "광학 마우스, 레이저 마우스, 무선 마우스 및 모든 유형을 지원합니다. 다양한 브랜드의 전문 게이밍 및 사무용 마우스와 호환됩니다."
    },
    network: {
      systemRequirements: [
        "안정적인 인터넷 연결",
        "브라우저 네트워크 API 지원",
        "네트워크 테스트를 허용하는 방화벽",
        "정상적인 DNS 해결 서비스"
      ],
      parameters: {
        "테스트 대역폭": "1Mbps-1Gbps",
        "지연 시간 감지": "1-1000ms",
        "지터 측정": "< 5ms",
        "패킷 손실 감지": "< 0.1%",
        "테스트 지속 시간": "10-30초"
      },
      compatibilityNote: "브로드밴드, 광섬유, 모바일 네트워크를 포함한 모든 유형의 네트워크 연결에 적용 가능합니다. 정확한 네트워크 성능 평가를 제공합니다."
    }
  },
  bestPractices: {
    title: "모범 사례 가이드",
    recommended: "권장 사례",
    avoid: "피해야 할 것들",
    optimization: "성능 최적화 팁",
    camera: {
      dos: [
        "자연광 또는 부드러운 인공 조명 사용",
        "카메라 렌즈를 깨끗하게 유지",
        "단순하고 깨끗한 배경 선택",
        "얼굴이 프레임 중앙에 위치하도록 확인",
        "카메라 드라이버를 정기적으로 업데이트"
      ],
      donts: [
        "강한 역광이나 측면 조명 피하기",
        "조명이 부족한 환경에서 사용하지 않기",
        "이미지 안정성에 영향을 주는 빈번한 움직임 피하기",
        "다른 프로그램이 동시에 카메라에 액세스하지 않도록 하기",
        "카메라 렌즈를 만지거나 가리지 않기"
      ],
      optimizationTip: "중요한 화상 회의의 경우 외부 카메라와 전문 조명 장비 사용을 고려하여 비디오 품질과 전문적인 외관을 크게 향상시키세요."
    },
    microphone: {
      dos: [
        "조용한 녹음 환경 선택",
        "적절한 마이크 거리 유지",
        "파열음을 줄이기 위해 팝 필터 사용",
        "더 나은 오디오 품질을 위해 노이즈 감소 활성화",
        "오디오 설정을 정기적으로 확인"
      ],
      donts: [
        "시끄러운 환경에서 녹음하지 않기",
        "마이크에 너무 가깝거나 너무 멀리 위치하지 않기",
        "여러 오디오 장치를 동시에 사용하지 않기",
        "에코 및 피드백 문제를 무시하지 않기",
        "녹음 중 소음을 만들지 않기"
      ],
      optimizationTip: "전문 사용자는 스튜디오 품질의 오디오를 위해 콘덴서 마이크와 오디오 인터페이스, 음향 처리된 녹음 환경의 조합을 고려해야 합니다."
    },
    headphones: {
      dos: [
        "적절한 볼륨 레벨 선택",
        "헤드폰 장비를 정기적으로 청소",
        "테스트용 고품질 오디오 파일 사용",
        "장시간 착용 편안함에 주의",
        "용도에 따라 적절한 헤드폰 유형 선택"
      ],
      donts: [
        "장시간 고음량 사용 피하기",
        "헤드폰 착용 편안함을 무시하지 않기",
        "시끄러운 환경에서 과도한 볼륨 증가 피하기",
        "손상된 오디오 인터페이스 사용하지 않기",
        "개인용 헤드폰 장치 공유 피하기"
      ],
      optimizationTip: "게이머는 저지연 유선 헤드폰을 선택해야 하며, 음악 애호가는 헤드폰 앰프와 함께 고임피던스 모니터 헤드폰을 선택할 수 있습니다."
    },
    keyboard: {
      dos: [
        "키보드를 깨끗하고 건조하게 유지",
        "키 기능을 정기적으로 확인",
        "적절한 타이핑 자세 사용",
        "필요에 따라 적절한 키보드 유형 선택",
        "키보드 드라이버와 펌웨어를 적시에 업데이트"
      ],
      donts: [
        "키보드 위에서 음식 섭취 피하기",
        "키를 강하게 치지 않기",
        "키보드에 액체 유출 피하기",
        "장시간 나쁜 타이핑 자세 유지하지 않기",
        "복잡한 키보드 구조 분해 피하기"
      ],
      optimizationTip: "프로그래머는 타이핑 경험 향상을 위해 기계식 키보드를 고려해야 하며, 게이머는 매크로 기능과 RGB 백라이트가 있는 키보드를 선택할 수 있습니다."
    },
    mouse: {
      dos: [
        "고품질 마우스 패드 사용",
        "센서 영역을 깨끗하게 유지",
        "적절한 DPI 설정 조정",
        "마우스 피트를 정기적으로 청소",
        "손 크기에 적합한 마우스 선택"
      ],
      donts: [
        "고르지 않은 표면에서 마우스 사용 피하기",
        "마우스 청소 및 유지보수를 소홀히 하지 않기",
        "DPI 설정이 너무 높거나 낮지 않도록 하기",
        "장시간 긴장된 그립 자세 유지하지 않기",
        "마우스를 떨어뜨리거나 무거운 압력을 가하지 않기"
      ],
      optimizationTip: "e스포츠 플레이어는 더 정밀한 제어 경험을 위해 가벼운 게이밍 마우스와 대형 컨트롤 타입 마우스 패드를 선택해야 합니다."
    },
    network: {
      dos: [
        "최고의 안정성을 위해 유선 연결 사용",
        "캐시를 지우기 위해 라우터를 정기적으로 재시작",
        "혼잡이 적은 WiFi 채널 선택",
        "불필요한 백그라운드 프로그램 종료",
        "네트워크 성능을 정기적으로 테스트"
      ],
      donts: [
        "네트워크 피크 시간 중 중요한 작업 피하기",
        "여러 고트래픽 작업을 동시에 수행하지 않기",
        "오래된 네트워크 장비 사용 피하기",
        "네트워크 보안 설정을 무시하지 않기",
        "약한 신호 지역에서 WiFi 사용 피하기"
      ],
      optimizationTip: "전문 사용자는 성능과 안정성을 크게 향상시키기 위해 엔터프라이즈급 라우터와 네트워크 장비로 기가비트 네트워크로 업그레이드를 고려해야 합니다."
    },
    meeting: {
      dos: [
        "중요한 회의 24시간 전에 포괄적인 장치 테스트 실시",
        "충분한 대역폭으로 안정적인 네트워크 연결 확보",
        "조용하고 밝은 회의 환경 선택",
        "주요 장치 고장에 대비한 백업 장치 준비",
        "실제 회의 플랫폼과의 호환성 테스트"
      ],
      donts: [
        "회의 전 마지막 순간 장치 테스트 피하기",
        "불안정한 네트워크로 중요한 회의 진행하지 않기",
        "테스트되지 않은 새 장치 사용 피하기",
        "오디오 에코 및 비디오 지연 문제 무시하지 않기",
        "시끄럽거나 조명이 나쁜 환경에서 회의 피하기"
      ],
      optimizationTip: "기업 사용자는 일관된 회의 품질과 전문성을 보장하기 위해 정기적인 장치 유지보수와 성능 테스트를 통한 표준화된 회의실 장비 구성을 확립해야 합니다."
    },
    gaming: {
      dos: [
        "최저 지연을 위해 유선 연결 사용",
        "게이밍 주변기기의 정기적인 청소 및 유지보수",
        "입력 지연을 줄이기 위해 시스템 설정 최적화",
        "정밀도 향상을 위해 전문 게이밍 마우스 패드 사용",
        "드라이버와 펌웨어를 정기적으로 업데이트"
      ],
      donts: [
        "경쟁 게임에서 무선 장치 사용 피하기",
        "주변기기 응답 시간 설정을 무시하지 않기",
        "시스템 리소스 부족으로 게임하지 않기",
        "품질이 나쁘거나 손상된 장비 사용하지 않기",
        "근육 기억에 영향을 주는 빈번한 장비 변경 피하기"
      ],
      optimizationTip: "전문 e스포츠 선수는 경쟁 안정성을 보장하기 위해 개인 장비 프로필을 확립하고, 최적 설정 매개변수를 기록하며, 정기적인 장치 성능 벤치마크를 실시해야 합니다."
    },
    tools: {
      dos: [
        "사용될 환경에서 장치 테스트",
        "포괄적인 결과를 위해 권장 테스트 순서 따르기",
        "성능 추적을 위해 테스트 보고서 저장 및 비교",
        "완전한 하드웨어 평가를 위해 여러 도구 사용",
        "성능 변화를 모니터링하기 위해 정기적으로 장치 테스트"
      ],
      donts: [
        "장치 액세스 권한 요청을 건너뛰지 않기",
        "간섭이 있는 환경에서 테스트 피하기",
        "경고 메시지나 오류 표시기 무시하지 않기",
        "적절한 설정 없이 테스트를 서두르지 않기",
        "오래된 브라우저 버전으로 테스트하지 않기"
      ],
      optimizationTip: "전문적 사용을 위해 장치 성능을 정기적으로 모니터링하고 문제 해결 및 최적화 목적을 위한 상세한 기록을 유지하는 테스트 일정을 만드세요."
    },
    home: {
      dos: [
        "실제 사용될 환경에서 장치 테스트",
        "정확한 장치 감지를 위해 필요한 모든 권한 부여",
        "포괄적인 테스트를 시작하기 전에 안정적인 인터넷 연결 확보",
        "최적의 결과를 위해 권장 테스트 순서 따르기",
        "시간 경과에 따른 장치 성능 추적을 위해 테스트 보고서 저장 및 비교",
        "최상의 호환성을 위해 장치 드라이버를 정기적으로 업데이트"
      ],
      donts: [
        "리소스 충돌을 피하기 위해 여러 장치를 동시에 테스트하지 않기",
        "전자기 간섭이 있는 환경에서 테스트 피하기",
        "브라우저 권한 요청이나 보안 경고 건너뛰지 않기",
        "최신 기능을 지원하지 않을 수 있는 오래된 브라우저 사용 피하기",
        "장치 드라이버 업데이트에 대한 시스템 알림 무시하지 않기",
        "시스템 업데이트나 무거운 백그라운드 프로세스 중 테스트 피하기"
      ],
      optimizationTip: "최적의 테스트 경험을 위해 불필요한 애플리케이션을 닫고, 시스템이 최소 요구사항을 충족하는지 확인하며, 장치를 개별적으로 테스트하세요. 정기적인 테스트는 장치 성능을 유지하고 문제를 조기에 식별하는 데 도움이 됩니다."
    }
  },
  industryStandards: {
    title: "업계 표준 및 인증",
    compliance: "표준 준수",
    camera: {
      list: [
        {
          name: "USB Video Class (UVC)",
          description: "플러그 앤 플레이 카메라 장치 호환성을 보장하는 범용 직렬 버스 비디오 클래스 표준",
          requirement: "UVC 1.1/1.5 표준 지원"
        },
        {
          name: "WebRTC 비디오 표준",
          description: "크로스 플랫폼 비디오 통화 호환성을 보장하는 실시간 통신 웹 비디오 전송 프로토콜",
          requirement: "H.264/VP8/VP9 인코딩 지원"
        },
        {
          name: "ISO/IEC 23001-8",
          description: "멀티미디어 시스템 및 장비 코딩 매개변수 표준",
          requirement: "국제 코딩 표준 준수"
        }
      ],
      complianceNote: "당사의 카메라 테스트 도구는 국제 표준을 엄격히 준수하여 테스트 결과의 정확성과 신뢰성을 보장합니다. 주류 비디오 인코딩 형식과 전송 프로토콜을 지원합니다."
    },
    microphone: {
      list: [
        {
          name: "IEC 61938",
          description: "멀티미디어 장비 오디오 측정 방법의 국제 표준",
          requirement: "전문 오디오 테스트 표준 준수"
        },
        {
          name: "WebRTC 오디오 처리",
          description: "에코 제거 및 노이즈 감소 알고리즘을 포함한 실시간 오디오 처리 및 전송 표준",
          requirement: "Opus/G.722/G.711 인코딩 지원"
        },
        {
          name: "USB Audio Class",
          description: "오디오 장치 호환성을 보장하는 USB 오디오 장치 클래스 표준",
          requirement: "UAC 1.0/2.0 표준 지원"
        }
      ],
      complianceNote: "오디오 테스트는 전문 오디오 분석 알고리즘을 사용하여 방송급 오디오 품질 표준을 준수하며, 전문 오디오 애플리케이션에 신뢰할 수 있는 테스트 결과를 제공합니다."
    },
    network: {
      list: [
        {
          name: "RFC 6349",
          description: "TCP 처리량 테스트 방법론 표준",
          requirement: "표준화된 네트워크 성능 테스트"
        },
        {
          name: "ITU-T Y.1540",
          description: "IP 네트워크 성능 매개변수 정의 표준",
          requirement: "국제전기통신연합 성능 표준"
        },
        {
          name: "IETF RFC 2544",
          description: "네트워크 상호 연결 장치 벤치마크 테스트 방법론",
          requirement: "네트워크 장비 테스트 표준"
        }
      ],
      complianceNote: "네트워크 테스트는 국제 통신 표준을 따르며, 정확한 대역폭, 지연 시간, 지터 측정을 제공하여 엔터프라이즈급 네트워크 평가 요구사항을 준수합니다."
    },
    keyboard: {
      list: [
        {
          name: "USB HID 표준",
          description: "범용 키보드 장치 호환성을 보장하는 휴먼 인터페이스 장치 표준",
          requirement: "USB HID 1.11 표준 준수"
        },
        {
          name: "ISO/IEC 9995",
          description: "키 배치와 기능을 정의하는 국제 키보드 레이아웃 표준",
          requirement: "국제 표준 키보드 레이아웃 지원"
        },
        {
          name: "FCC 인증",
          description: "연방통신위원회 전자기 호환성 인증",
          requirement: "EMC 전자기 호환성 테스트 통과"
        }
      ],
      complianceNote: "키보드 테스트 도구는 국제적으로 준수하는 모든 키보드 장치를 지원하여 테스트 결과의 정확성과 장치 호환성을 보장합니다."
    },
    mouse: {
      list: [
        {
          name: "USB HID 표준",
          description: "플러그 앤 플레이 호환성을 보장하는 마우스 장치 휴먼 인터페이스 표준",
          requirement: "USB HID 1.11 표준 준수"
        },
        {
          name: "ISO 9241-9",
          description: "인간-컴퓨터 상호작용 포인팅 장치 요구사항 표준",
          requirement: "인체공학적 설계 표준 준수"
        },
        {
          name: "게이밍 인증",
          description: "전문 게이밍 장치 성능 인증 표준",
          requirement: "e스포츠급 성능 요구사항 충족"
        }
      ],
      complianceNote: "마우스 테스트는 정밀도, 응답 시간, 인체공학을 포함한 여러 차원을 다루며, 전문 게이밍 및 사무용 애플리케이션의 엄격한 요구사항을 충족합니다."
    },
    meeting: {
      list: [
        {
          name: "WebRTC 표준",
          description: "크로스 플랫폼 오디오/비디오 호환성을 보장하는 웹 실시간 통신 표준",
          requirement: "WebRTC 1.0 사양 지원"
        },
        {
          name: "ITU-T H.264",
          description: "비디오 회의 시스템에서 널리 사용되는 국제 비디오 인코딩 표준",
          requirement: "H.264/AVC 인코딩 표준 지원"
        },
        {
          name: "SIP 프로토콜",
          description: "세션 개시 프로토콜, 엔터프라이즈급 통신 시스템 표준",
          requirement: "SIP/RTP 프로토콜 스택과 호환"
        },
        {
          name: "GDPR 준수",
          description: "사용자 개인정보 데이터를 보호하는 유럽연합 일반 데이터 보호 규정",
          requirement: "데이터 보호 규제 요구사항 준수"
        }
      ],
      complianceNote: "회의 장치 테스트는 국제 통신 표준과 개인정보 보호 규정을 엄격히 준수하여 엔터프라이즈급 애플리케이션의 보안과 신뢰성을 보장합니다."
    },
    gaming: {
      list: [
        {
          name: "USB 3.0 표준",
          description: "게이밍 주변기기의 저지연 응답을 보장하는 고속 데이터 전송 표준",
          requirement: "USB 3.0/3.1 사양 지원"
        },
        {
          name: "DirectInput API",
          description: "Microsoft 게이밍 입력 장치 인터페이스 표준",
          requirement: "DirectInput 8.0과 호환"
        },
        {
          name: "e스포츠 인증",
          description: "전자 스포츠 장비 성능 인증 표준",
          requirement: "전문 e스포츠 요구사항 충족"
        },
        {
          name: "저지연 오디오",
          description: "전문 오디오 저지연 전송 표준",
          requirement: "ASIO/WDM 드라이버 지원"
        }
      ],
      complianceNote: "게이밍 장치 테스트는 e스포츠와 전문 게이밍 요구사항을 대상으로 하며, 가장 엄격한 성능 표준을 사용하여 경쟁급 장치 성능을 보장합니다."
    },
    headphones: {
      list: [
        {
          name: "IEC 60268-7",
          description: "헤드폰 전기음향 성능 측정 표준",
          requirement: "국제전기기술위원회 표준 준수"
        },
        {
          name: "THX 인증",
          description: "전문 오디오 품질 인증 표준",
          requirement: "THX 오디오 인증 통과"
        },
        {
          name: "하이레즈 오디오",
          description: "고품질 오디오 재생을 지원하는 고해상도 오디오 표준",
          requirement: "24bit/96kHz 이상 지원"
        }
      ],
      complianceNote: "헤드폰 테스트는 전문 오디오 분석 기술을 사용하여 방송급 오디오 품질 표준을 준수하며, 전문 오디오 애플리케이션에 정확한 평가를 제공합니다."
    },
    tools: {
      list: [
        {
          name: "WebRTC 표준",
          description: "크로스 플랫폼 장치 호환성을 보장하는 웹 실시간 통신 표준",
          requirement: "WebRTC 1.0 사양 지원"
        },
        {
          name: "W3C 웹 표준",
          description: "웹 접근성과 호환성을 위한 World Wide Web 컨소시엄 표준",
          requirement: "WCAG 2.1 접근성 가이드라인 준수"
        },
        {
          name: "ISO/IEC 27001",
          description: "정보 보안 관리 시스템 국제 표준",
          requirement: "데이터 보안 모범 사례 준수"
        },
        {
          name: "GDPR 준수",
          description: "사용자 개인정보를 위한 유럽연합 일반 데이터 보호 규정",
          requirement: "사용자 데이터 보호 및 개인정보 보장"
        }
      ],
      complianceNote: "당사의 테스트 도구는 국제 웹 표준, 보안 프로토콜, 접근성 가이드라인을 준수하여 전 세계 사용자에게 신뢰할 수 있고 안전한 테스트 경험을 보장합니다."
    },
    home: {
      list: [
        {
          name: "WebRTC 표준",
          description: "크로스 플랫폼 장치 호환성을 보장하는 웹 실시간 통신 표준",
          requirement: "WebRTC 1.0 사양 지원"
        },
        {
          name: "W3C 웹 표준",
          description: "웹 접근성과 호환성을 위한 World Wide Web 컨소시엄 표준",
          requirement: "WCAG 2.1 접근성 가이드라인 준수"
        },
        {
          name: "ISO/IEC 27001",
          description: "정보 보안 관리 시스템 국제 표준",
          requirement: "데이터 보안 모범 사례 준수"
        },
        {
          name: "GDPR 준수",
          description: "사용자 개인정보를 위한 유럽연합 일반 데이터 보호 규정",
          requirement: "사용자 데이터 보호 및 개인정보 보장"
        },
        {
          name: "HTML5 표준",
          description: "장치 액세스와 멀티미디어를 위한 현대 웹 기술 표준",
          requirement: "HTML5 Media Capture API 지원"
        },
        {
          name: "크로스 플랫폼 호환성",
          description: "범용 호환성을 보장하는 멀티 플랫폼 장치 테스트 표준",
          requirement: "Windows, macOS, Linux, iOS, Android에서 작동"
        }
      ],
      complianceNote: "당사의 포괄적인 장치 테스트 플랫폼은 국제 웹 표준, 보안 프로토콜, 접근성 가이드라인을 준수하여 전 세계 사용자에게 신뢰할 수 있고 안전한 테스트 경험을 보장합니다."
    }
  }
};
