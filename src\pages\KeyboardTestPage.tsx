import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { KeyboardTest } from "@/components/tests/KeyboardTest";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { SEOFooter } from "@/components/seo/SEOFooter";

export const KeyboardTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('keyboard', t, window.location.origin);

  return (
    <MainLayout seoConfig={seoConfig}>
      <KeyboardTest />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="keyboard" />
        <FAQ pageType="keyboard" />
        <Glossary pageType="keyboard" />
        <TroubleshootingGuide pageType="keyboard" />
        <SEOFooter pageType="keyboard" />
      </div>
    </MainLayout>
  );
};