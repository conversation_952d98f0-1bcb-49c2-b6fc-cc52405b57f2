import React from "react";
import { Globe, ChevronDown } from "lucide-react";
import { useLanguage, useLanguageNavigation } from "../../hooks/useLanguage";
import { LANGUAGE_CONFIGS, SUPPORTED_LANGUAGES, Language } from "@/config/languages";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

export const LanguageSwitcher: React.FC = () => {
  const { language } = useLanguage();
  const { setLanguage } = useLanguageNavigation();

  const handleLanguageChange = (newLang: Language) => {
    setLanguage(newLang);
  };

  const currentLanguageConfig = LANGUAGE_CONFIGS[language];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-all duration-200 border border-white/20">
          <Globe className="h-4 w-4" />
          <span className="text-sm font-medium">{currentLanguageConfig.flag}</span>
          <ChevronDown className="h-3 w-3" />
        </button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-48 p-0 bg-white/5 backdrop-blur-xl border border-white/20 shadow-2xl"
        align="end"
      >
        <div className="bg-black/20 backdrop-blur-xl rounded-lg">
          {SUPPORTED_LANGUAGES.map((langCode) => {
            const config = LANGUAGE_CONFIGS[langCode];
            return (
              <button
                key={langCode}
                onClick={() => handleLanguageChange(langCode)}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/20 transition-all duration-200 first:rounded-t-lg last:rounded-b-lg ${
                  language === langCode ? 'bg-white/20 text-white' : 'text-white/80'
                }`}
              >
                <span className="text-lg">{config.flag}</span>
                <span className="text-sm font-medium">{config.nativeName}</span>
              </button>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
};