/**
 * 中文 - 故障排查翻译
 * 包含故障排查指南和解决方案
 */

import type { TroubleshootingTranslation } from '../types';

export const zhTroubleshooting: TroubleshootingTranslation = {
  title: "故障排查指南",
  commonIssues: "常见问题",
  stepByStep: "解决步骤",
  // 不同页面的故障排查
  camera: {
    // 基础问题
    issue1: "摄像头黑屏或无法启动",
    solution1: "检查浏览器权限设置，重新授权摄像头访问权限，重启浏览器后重试。",
    issue2: "画面卡顿或延迟",
    solution2: "关闭其他占用摄像头的程序，检查系统资源使用情况，降低视频质量设置。",
    issue3: "画面模糊不清",
    solution3: "清洁摄像头镜头，调整环境光线，检查焦距设置。",

    // 扩展问题
    issue4: "摄像头在特定应用中无法工作",
    solution4: "检查应用程序权限设置，确认摄像头未被其他程序独占使用，尝试以管理员身份运行应用程序。",
    issue5: "摄像头分辨率或帧率异常",
    solution5: "在设备管理器中更新摄像头驱动，检查摄像头规格是否支持当前设置，调整应用程序中的视频设置。",
    issue6: "摄像头颜色失真或过曝",
    solution6: "调整环境光线，检查摄像头自动曝光设置，在摄像头软件中手动调整白平衡和曝光参数。",
    issue7: "USB摄像头频繁断开连接",
    solution7: "更换USB线缆，尝试不同的USB端口，检查USB端口供电是否充足，禁用USB选择性暂停设置。",
    issue8: "摄像头驱动程序冲突",
    solution8: "卸载冲突的驱动程序，从制造商官网下载最新驱动，使用设备管理器回滚到稳定版本驱动。",
    issue9: "摄像头在视频会议中音画不同步",
    solution9: "检查网络连接稳定性，关闭不必要的后台程序，调整视频编码设置，重启路由器改善网络质量。",
    issue10: "摄像头隐私指示灯异常",
    solution10: "检查是否有恶意软件，运行系统安全扫描，检查后台运行的程序，确认摄像头权限设置正确。",
    issue11: "多摄像头设备切换问题",
    solution11: "在系统设置中指定默认摄像头，检查应用程序的摄像头选择设置，确保驱动程序支持多设备管理。",
    issue12: "摄像头在低光环境下噪点严重",
    solution12: "增加环境照明，调整摄像头ISO设置，使用降噪软件，考虑升级到低光性能更好的摄像头。",

    // 分步解决方案
    step1: "确认摄像头硬件连接正常",
    step2: "检查浏览器摄像头权限设置",
    step3: "重启浏览器并重新授权",
    step4: "测试其他应用程序的摄像头功能",
    step5: "如问题持续，尝试更新摄像头驱动程序",
    step6: "检查Windows隐私设置中的摄像头权限",
    step7: "运行Windows疑难解答程序",
    step8: "检查设备管理器中的摄像头状态",
    step9: "尝试在安全模式下测试摄像头",
    step10: "考虑重置摄像头设置到默认值",

    // 跨平台操作步骤
    windowsSteps: [
      "打开设置 > 隐私 > 摄像头，确保允许应用访问摄像头",
      "在设备管理器中检查摄像头驱动状态",
      "运行 Windows 更新检查驱动程序更新",
      "使用相机应用测试基本功能",
      "检查防病毒软件是否阻止摄像头访问"
    ],
    macosSteps: [
      "打开系统偏好设置 > 安全性与隐私 > 摄像头",
      "确保相关应用已获得摄像头权限",
      "重置SMC（系统管理控制器）",
      "检查活动监视器中是否有程序占用摄像头",
      "尝试创建新用户账户测试摄像头功能"
    ],
    linuxSteps: [
      "使用 lsusb 命令检查摄像头是否被识别",
      "检查 /dev/video* 设备文件是否存在",
      "安装 v4l-utils 工具包进行摄像头调试",
      "使用 cheese 或 guvcview 测试摄像头功能",
      "检查用户是否在 video 用户组中"
    ],

    // 预防性维护
    maintenance: [
      "定期清洁摄像头镜头，避免灰尘积累",
      "保持摄像头驱动程序更新到最新版本",
      "避免在高温环境下长时间使用摄像头",
      "定期检查USB连接线是否有损坏",
      "建立摄像头设置备份，便于快速恢复"
    ]
  },
  microphone: {
    // 基础问题
    issue1: "麦克风无声音或音量过低",
    solution1: "检查系统音量设置和麦克风权限，确保设备未被静音，调整输入音量。",
    issue2: "音质差或有杂音",
    solution2: "选择安静环境，调整麦克风位置，开启降噪功能，检查设备连接。",
    issue3: "有回音或啸叫",
    solution3: "使用耳机代替扬声器，降低音量，开启回音消除功能。",

    // 扩展问题
    issue4: "麦克风延迟严重",
    solution4: "检查音频驱动程序，降低音频缓冲区大小，关闭音频增强功能，使用专业音频接口。",
    issue5: "麦克风只在一侧有声音",
    solution5: "检查麦克风连接线，确认音频设置为单声道或立体声，测试不同的音频端口。",
    issue6: "麦克风音量自动调节异常",
    solution6: "禁用Windows自动增益控制，关闭应用程序的自动音量调节，手动设置固定音量级别。",
    issue7: "USB麦克风无法识别",
    solution7: "更换USB端口，检查USB驱动程序，确认麦克风兼容性，尝试在其他设备上测试。",
    issue8: "麦克风在特定频率有啸叫",
    solution8: "调整麦克风与扬声器的距离和角度，使用均衡器降低问题频率，检查房间声学环境。",
    issue9: "蓝牙麦克风连接不稳定",
    solution9: "检查蓝牙驱动程序，清除蓝牙缓存，重新配对设备，确保设备在有效范围内。",
    issue10: "麦克风录音有电流声",
    solution10: "检查电源接地，更换音频线缆，使用音频隔离器，检查周围电磁干扰源。",
    issue11: "多麦克风设备冲突",
    solution11: "在系统设置中指定默认麦克风，禁用不使用的音频设备，检查应用程序音频设置。",
    issue12: "麦克风在游戏中无法工作",
    solution12: "检查游戏音频设置，确认游戏有麦克风权限，关闭独占模式，更新游戏和音频驱动。",

    // 分步解决方案
    step1: "检查麦克风硬件连接",
    step2: "验证浏览器麦克风权限",
    step3: "调整系统音频设置",
    step4: "测试不同环境下的录音效果",
    step5: "尝试其他录音软件验证问题",
    step6: "检查音频驱动程序状态",
    step7: "测试麦克风在安全模式下的工作状态",
    step8: "检查音频服务是否正常运行",
    step9: "尝试使用不同的音频格式设置",
    step10: "考虑重置音频设置到默认值",

    // 跨平台操作步骤
    windowsSteps: [
      "右键点击音量图标，选择录音设备",
      "确保麦克风已启用且设为默认设备",
      "检查麦克风属性中的级别和增强设置",
      "运行录音机应用测试麦克风功能",
      "检查Windows隐私设置中的麦克风权限"
    ],
    macosSteps: [
      "打开系统偏好设置 > 声音 > 输入",
      "选择正确的麦克风设备",
      "调整输入音量并测试音量级别",
      "检查安全性与隐私中的麦克风权限",
      "使用QuickTime Player测试录音功能"
    ],
    linuxSteps: [
      "使用 arecord -l 命令列出录音设备",
      "检查 alsamixer 中的麦克风音量设置",
      "使用 pavucontrol 调整PulseAudio设置",
      "测试 arecord 命令录音功能",
      "检查用户是否在 audio 用户组中"
    ],

    // 预防性维护
    maintenance: [
      "定期清洁麦克风防风罩和外壳",
      "避免在潮湿环境中使用麦克风",
      "定期检查音频线缆连接状态",
      "保持音频驱动程序更新",
      "建立音频设置配置文件备份"
    ]
  },
  headphones: {
    // 基础问题
    issue1: "耳机无声音或音量过小",
    solution1: "检查音频输出设备选择，调整系统音量和应用音量，确认音频驱动正常工作。",
    issue2: "左右声道不平衡或单侧无声",
    solution2: "检查音频平衡设置，测试耳机连接，更换音频接口，可能是耳机硬件问题。",
    issue3: "音质差或有杂音",
    solution3: "清洁耳机接口，检查音频文件质量，调整音效设置，避免电磁干扰。",

    // 扩展问题
    issue4: "蓝牙耳机连接不稳定",
    solution4: "检查蓝牙驱动程序，清除蓝牙设备缓存，重新配对耳机，确保设备在有效连接范围内。",
    issue5: "耳机音频延迟严重",
    solution5: "使用有线连接，检查音频编解码器设置，关闭音频增强功能，更新蓝牙驱动程序。",
    issue6: "耳机麦克风无法工作",
    solution6: "检查耳机是否支持麦克风功能，确认音频设备设置正确，测试麦克风权限设置。",
    issue7: "耳机在特定应用中无声音",
    solution7: "检查应用程序音频设置，确认应用有音频权限，重启应用程序，检查音频独占模式。",
    issue8: "耳机音量控制失效",
    solution8: "检查耳机驱动程序，确认音量控制功能支持，重新安装音频驱动，检查系统音量混合器。",
    issue9: "耳机产生电流声或底噪",
    solution9: "检查音频接口清洁度，更换音频线缆，调整音频增益设置，检查电源干扰。",
    issue10: "多个音频设备冲突",
    solution10: "在系统设置中指定默认音频设备，禁用不使用的音频设备，检查应用程序音频设置。",
    issue11: "耳机驱动程序冲突",
    solution11: "卸载冲突的音频驱动，从制造商官网下载最新驱动，使用设备管理器回滚驱动版本。",
    issue12: "耳机在游戏中音效异常",
    solution12: "检查游戏音频设置，确认3D音效支持，调整音频采样率，更新游戏和音频驱动。",

    // 分步解决方案
    step1: "确认耳机硬件连接正常",
    step2: "检查系统音频输出设置",
    step3: "测试不同音频文件和音量级别",
    step4: "验证其他音频设备工作状态",
    step5: "更新音频驱动程序",
    step6: "检查音频格式兼容性",
    step7: "测试耳机在其他设备上的工作状态",
    step8: "检查音频服务运行状态",
    step9: "尝试重置音频设置",
    step10: "考虑使用专业音频软件测试",

    // 跨平台操作步骤
    windowsSteps: [
      "右键点击音量图标，选择播放设备",
      "确保耳机已设为默认播放设备",
      "检查耳机属性中的音频格式设置",
      "测试Windows系统声音",
      "检查音频增强功能是否影响播放"
    ],
    macosSteps: [
      "打开系统偏好设置 > 声音 > 输出",
      "选择正确的耳机设备",
      "调整输出音量和平衡设置",
      "检查音频MIDI设置中的格式配置",
      "使用音乐应用测试音频播放"
    ],
    linuxSteps: [
      "使用 aplay -l 命令列出播放设备",
      "检查 alsamixer 中的音量设置",
      "使用 pavucontrol 配置PulseAudio",
      "测试 speaker-test 命令",
      "检查音频组权限设置"
    ],

    // 预防性维护
    maintenance: [
      "定期清洁耳机接口和线缆",
      "避免过度拉扯耳机线缆",
      "保持音频驱动程序更新",
      "定期检查耳机垫和头带磨损情况",
      "建立个人音频设置配置备份"
    ]
  },
  keyboard: {
    // 基础问题
    issue1: "按键无响应或响应延迟",
    solution1: "检查键盘连接，重新安装驱动程序，清理键盘内部，检查系统资源占用。",
    issue2: "按键重复输入或粘滞",
    solution2: "调整键盘重复速度设置，清洁按键开关，检查键盘老化情况，可能需要更换。",
    issue3: "特殊功能键不工作",
    solution3: "安装键盘专用软件，检查快捷键设置，确认功能键驱动正常，更新固件。",

    // 扩展问题
    issue4: "无线键盘连接不稳定",
    solution4: "更换电池，检查无线接收器连接，重新配对键盘，确保无线信号无干扰。",
    issue5: "键盘背光不工作",
    solution5: "检查背光快捷键设置，确认键盘支持背光功能，更新键盘驱动和管理软件。",
    issue6: "机械键盘按键手感异常",
    solution6: "检查键帽是否松动，清洁轴体内部，检查弹簧和触点状态，考虑更换轴体。",
    issue7: "键盘在游戏中按键冲突",
    solution7: "检查键盘是否支持全键无冲，调整游戏按键设置，使用游戏模式，更新键盘固件。",
    issue8: "键盘输入法切换异常",
    solution8: "检查系统输入法设置，重置键盘语言配置，清除输入法缓存，重新安装输入法。",
    issue9: "键盘宏功能失效",
    solution9: "检查键盘管理软件设置，确认宏配置正确，更新键盘驱动程序，重新录制宏。",
    issue10: "USB键盘频繁断开",
    solution10: "更换USB线缆，尝试不同USB端口，检查USB驱动程序，禁用USB节能模式。",
    issue11: "键盘按键映射错误",
    solution11: "检查键盘布局设置，重置键盘映射配置，确认区域和语言设置正确。",
    issue12: "键盘在BIOS中无法使用",
    solution12: "检查BIOS中的USB支持设置，尝试使用PS/2接口，更新主板BIOS版本。",

    // 分步解决方案
    step1: "验证键盘物理连接状态",
    step2: "检查键盘驱动程序安装",
    step3: "测试基础按键和组合键功能",
    step4: "清洁键盘并检查机械状态",
    step5: "在其他设备上测试键盘功能",
    step6: "检查键盘管理软件设置",
    step7: "测试键盘在安全模式下的工作状态",
    step8: "验证USB端口和线缆状态",
    step9: "检查系统键盘设置和布局",
    step10: "考虑重置键盘到出厂设置",

    // 跨平台操作步骤
    windowsSteps: [
      "打开设备管理器检查键盘驱动状态",
      "在控制面板中调整键盘重复速度",
      "检查Windows键盘语言和布局设置",
      "使用记事本测试所有按键功能",
      "检查键盘制造商提供的管理软件"
    ],
    macosSteps: [
      "打开系统偏好设置 > 键盘",
      "调整按键重复和延迟设置",
      "检查输入源和键盘布局",
      "使用键盘查看器测试按键",
      "检查辅助功能中的键盘设置"
    ],
    linuxSteps: [
      "使用 lsusb 命令检查键盘识别状态",
      "检查 /proc/bus/input/devices 中的键盘信息",
      "使用 xev 命令测试按键事件",
      "配置 xmodmap 进行按键映射",
      "检查键盘布局配置文件"
    ],

    // 预防性维护
    maintenance: [
      "定期清洁键盘表面和按键间隙",
      "避免在键盘上饮食造成污染",
      "保持键盘驱动程序和固件更新",
      "定期检查无线键盘电池电量",
      "建立键盘设置和宏配置备份"
    ]
  },
  mouse: {
    // 基础问题
    issue1: "鼠标指针移动不精确或跳跃",
    solution1: "清洁鼠标传感器，更换鼠标垫，调整DPI和加速度设置，检查表面适配性。",
    issue2: "鼠标按键失灵或双击问题",
    solution2: "调整双击速度，清理按键开关，检查微动开关寿命，可能需要维修或更换。",
    issue3: "滚轮滚动异常或无响应",
    solution3: "清洁滚轮机械结构，检查编码器状态，调整滚动设置，避免强力使用。",

    // 扩展问题
    issue4: "无线鼠标连接不稳定",
    solution4: "更换电池，检查无线接收器位置，重新配对鼠标，避免无线信号干扰。",
    issue5: "鼠标DPI切换功能失效",
    solution5: "检查鼠标驱动程序，确认DPI按键功能，更新鼠标管理软件，重置DPI设置。",
    issue6: "游戏鼠标宏功能异常",
    solution6: "检查鼠标管理软件设置，确认宏配置正确，更新驱动程序，重新录制宏命令。",
    issue7: "鼠标在特定游戏中失灵",
    solution7: "检查游戏鼠标设置，确认游戏支持鼠标型号，调整游戏输入设置，更新游戏版本。",
    issue8: "鼠标光标漂移或自动移动",
    solution8: "清洁鼠标传感器，检查鼠标垫表面，调整传感器灵敏度，检查是否有反射光干扰。",
    issue9: "鼠标侧键无法自定义",
    solution9: "安装鼠标专用软件，检查按键映射设置，确认鼠标支持可编程按键，更新固件。",
    issue10: "鼠标滚轮水平滚动失效",
    solution10: "检查鼠标是否支持水平滚动，确认驱动程序正确安装，调整滚轮设置，清洁滚轮机构。",
    issue11: "鼠标在高DPI下抖动",
    solution11: "降低DPI设置，更换高质量鼠标垫，检查传感器类型适配性，调整表面材质。",
    issue12: "鼠标电池耗电过快",
    solution12: "检查鼠标节能设置，降低轮询率，关闭不必要的RGB灯效，更换高质量电池。",

    // 分步解决方案
    step1: "检查鼠标硬件连接和供电",
    step2: "清洁传感器和使用表面",
    step3: "调整鼠标设置和DPI配置",
    step4: "测试不同表面的跟踪性能",
    step5: "验证其他设备的兼容性",
    step6: "检查鼠标驱动程序和管理软件",
    step7: "测试鼠标在不同应用中的表现",
    step8: "检查USB端口和无线接收器状态",
    step9: "验证鼠标固件版本",
    step10: "考虑重置鼠标到出厂设置",

    // 跨平台操作步骤
    windowsSteps: [
      "打开鼠标设置调整指针速度和精度",
      "在设备管理器中检查鼠标驱动状态",
      "使用鼠标制造商提供的管理软件",
      "测试鼠标在不同应用程序中的功能",
      "检查Windows指针精度增强设置"
    ],
    macosSteps: [
      "打开系统偏好设置 > 鼠标",
      "调整跟踪速度和滚动方向",
      "检查辅助功能中的鼠标设置",
      "使用活动监视器检查鼠标相关进程",
      "测试鼠标手势和多点触控功能"
    ],
    linuxSteps: [
      "使用 xinput 命令列出和配置鼠标设备",
      "检查 /proc/bus/input/devices 中的鼠标信息",
      "使用 xev 命令测试鼠标事件",
      "配置 xorg.conf 文件调整鼠标设置",
      "检查鼠标在不同桌面环境下的表现"
    ],

    // 预防性维护
    maintenance: [
      "定期清洁鼠标传感器和外壳",
      "使用合适的鼠标垫保护传感器",
      "避免在反光或透明表面使用鼠标",
      "保持鼠标驱动程序和固件更新",
      "定期检查无线鼠标电池电量"
    ]
  },
  network: {
    // 基础问题
    issue1: "网络延迟过高",
    solution1: "使用有线连接，关闭后台下载，联系网络服务商检查线路质量，优化路由器设置。",
    issue2: "下载速度慢",
    solution2: "重启路由器，检查其他设备占用，选择合适的测试服务器，升级网络套餐。",
    issue3: "网络不稳定频繁断开",
    solution3: "检查网线连接，更新网络驱动，避免网络高峰期使用，更换网络设备。",

    // 扩展问题
    issue4: "WiFi信号弱或连接不稳定",
    solution4: "移动到路由器附近，检查WiFi频段设置，更新无线网卡驱动，考虑使用WiFi扩展器。",
    issue5: "DNS解析失败或缓慢",
    solution5: "更换DNS服务器（如8.8.8.8），清除DNS缓存，检查路由器DNS设置，重置网络配置。",
    issue6: "特定网站或服务无法访问",
    solution6: "检查防火墙设置，尝试使用VPN，清除浏览器缓存，检查ISP是否有限制。",
    issue7: "上传速度远低于下载速度",
    solution7: "检查网络套餐上传限制，优化路由器QoS设置，关闭其他上传任务，联系ISP确认线路状态。",
    issue8: "网络游戏延迟和丢包严重",
    solution8: "使用有线连接，关闭后台更新，选择就近游戏服务器，优化路由器游戏模式设置。",
    issue9: "多设备连接时网速下降明显",
    solution9: "升级路由器带宽，配置QoS优先级，限制部分设备带宽，考虑使用Mesh网络系统。",
    issue10: "网络适配器频繁断开重连",
    solution10: "更新网络驱动程序，检查电源管理设置，更换网线或无线网卡，检查硬件兼容性。",
    issue11: "IPv6连接问题",
    solution11: "检查ISP是否支持IPv6，配置路由器IPv6设置，更新网络驱动，测试IPv6连通性。",
    issue12: "网络安全软件影响连接速度",
    solution12: "调整防火墙设置，配置安全软件白名单，临时禁用实时保护测试，优化安全软件配置。",

    // 分步解决方案
    step1: "检查网络硬件连接状态",
    step2: "重启网络设备清除缓存",
    step3: "测试有线和无线连接性能",
    step4: "检查网络设置和DNS配置",
    step5: "联系ISP进行线路检测",
    step6: "检查网络驱动程序状态",
    step7: "测试不同时间段的网络性能",
    step8: "检查路由器固件版本",
    step9: "验证网络安全设置",
    step10: "考虑升级网络硬件设备",

    // 跨平台操作步骤
    windowsSteps: [
      "运行网络疑难解答程序",
      "使用命令提示符执行 ipconfig /flushdns",
      "检查网络适配器设置和驱动程序",
      "使用 ping 和 tracert 命令测试连接",
      "检查Windows防火墙和网络配置"
    ],
    macosSteps: [
      "打开网络偏好设置检查连接状态",
      "使用网络诊断工具分析问题",
      "重置网络设置（删除并重新添加网络）",
      "使用终端执行 sudo dscacheutil -flushcache",
      "检查系统网络代理设置"
    ],
    linuxSteps: [
      "使用 ifconfig 或 ip 命令检查网络接口",
      "检查 /etc/resolv.conf 中的DNS设置",
      "使用 ping、traceroute、nslookup 测试连接",
      "检查 NetworkManager 或 systemd-networkd 配置",
      "查看系统日志中的网络相关错误"
    ],

    // 预防性维护
    maintenance: [
      "定期重启路由器和调制解调器",
      "保持网络设备固件更新",
      "定期清理网络设备散热口",
      "监控网络使用情况和性能指标",
      "建立网络配置备份和恢复计划"
    ]
  },
  meeting: {
    issue1: "视频或音频质量差",
    solution1: "确保网络稳定，关闭其他占用带宽的应用，调整视频质量设置，使用有线连接代替WiFi。",
    issue2: "与会者无法听到或看到您",
    solution2: "检查麦克风和摄像头权限设置，确认设备未被静音或禁用，尝试重新加入会议，更新会议软件。",
    issue3: "会议期间连接不稳定或断开",
    solution3: "使用有线网络连接，关闭不必要的应用程序，降低视频质量，检查网络带宽是否满足要求。",
    step1: "会议前测试所有设备功能",
    step2: "确保网络连接稳定可靠",
    step3: "调整摄像头位置和环境光线",
    step4: "检查音频设置和麦克风音量",
    step5: "准备备用设备以应对紧急情况"
  },
  gaming: {
    issue1: "游戏中出现延迟或卡顿",
    solution1: "使用有线网络连接，关闭后台程序，更新显卡驱动，优化游戏图形设置，检查系统温度。",
    issue2: "外设响应不灵敏或不准确",
    solution2: "更新设备驱动程序，调整DPI和轮询率设置，清洁传感器，检查USB接口是否正常工作。",
    issue3: "游戏音频定位不准确",
    solution3: "使用支持空间音频的耳机，更新音频驱动，调整游戏内音频设置，禁用不必要的音频效果。",
    step1: "确保所有游戏外设正常工作",
    step2: "优化网络连接减少延迟",
    step3: "调整外设设置匹配游戏需求",
    step4: "更新所有设备驱动和固件",
    step5: "监控系统性能避免过热问题"
  },
  tools: {
    issue1: "测试工具无法加载或启动",
    solution1: "刷新页面，清除浏览器缓存，确保启用JavaScript，尝试不同的浏览器。",
    issue2: "测试工具无法检测到设备",
    solution2: "检查设备连接，授予必要权限，确保设备未被其他应用程序使用。",
    issue3: "测试结果似乎不准确",
    solution3: "确保适当的测试环境，检查设备校准，验证没有其他程序干扰。",
    step1: "验证浏览器兼容性并在必要时更新",
    step2: "授予所有必需的设备权限",
    step3: "关闭可能干扰的其他应用程序",
    step4: "确保稳定的网络连接以进行实时测试",
    step5: "仔细遵循测试说明以获得准确结果"
  }
};
