/**
 * 한국어 - 메인 SEO 번역 내보내기
 * 모든 한국어 번역을 통합된 객체로 결합
 */

import { koEnhanced } from './enhanced';
import { koFooter } from './footer';
import { koKeywords } from './keywords';
import { koFAQ } from './faq';
import { koGlossary } from './glossary';
import { koTroubleshooting } from './troubleshooting';
import type { SEOTranslation } from '../types';

export const koTranslation: SEOTranslation = {
  enhanced: koEnhanced,
  seoFooter: koFooter,
  seoKeywords: koKeywords,
  faq: koFAQ,
  glossary: koGlossary,
  troubleshooting: koTroubleshooting
};

// 직접 사용을 위한 개별 모듈 내보내기
export {
  koEnhanced,
  koFooter,
  koKeywords,
  koFAQ,
  koGlossary,
  koTroubleshooting
};
