import React, { createContext, useContext, ReactNode, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { 
  Language, 
  getPreferredLanguage, 
  saveLanguagePreference,
  SUPPORTED_LANGUAGES,
  isValidLanguage 
} from '@/config/languages';
import { loadTranslation, getCachedTranslation, TranslationKeys } from '@/locales';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, replacements?: Record<string, string | number>) => string;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
  language: Language;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children, language }) => {
  const [translations, setTranslations] = useState<TranslationKeys | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载翻译文件
  useEffect(() => {
    const loadLanguageTranslations = async () => {
      setIsLoading(true);
      try {
        // 首先尝试从缓存获取
        const cached = getCachedTranslation(language);
        if (cached) {
          setTranslations(cached);
          setIsLoading(false);
          return;
        }

        // 如果缓存中没有，则动态加载
        const loadedTranslations = await loadTranslation(language);
        setTranslations(loadedTranslations);
      } catch (error) {
        console.error('Failed to load translations:', error);
        // 回退到英语
        try {
          const fallbackTranslations = await loadTranslation('en');
          setTranslations(fallbackTranslations);
        } catch (fallbackError) {
          console.error('Failed to load fallback translations:', fallbackError);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguageTranslations();
  }, [language]);

  const t = (key: string, replacements?: Record<string, string | number>): string => {
    if (!translations) {
      return key; // 如果翻译还没加载完成，返回key
    }

    const keys = key.split('.');
    let value: any = translations;
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    if (typeof value === 'string' && replacements) {
      Object.entries(replacements).forEach(([rKey, rValue]) => {
        value = value.replace(`{${rKey}}`, String(rValue));
      });
    }

    return value || key;
  };

  // 这个setLanguage函数是一个占位符
  // 实际的语言切换将由useLanguageNavigation hook处理
  const setLanguage = () => {
    console.warn(translations?.setLanguageWarning || 'setLanguage called from LanguageProvider. Use useLanguageNavigation hook instead.');
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// 用于语言导航的独立hook（只能在Router上下文内使用）
export const useLanguageNavigation = () => {
  const navigate = useNavigate();
  const { language, t } = useLanguage();

  const setLanguage = (newLang: Language) => {
    if (!isValidLanguage(newLang)) {
      console.error(`${t('invalidLanguageCode')}: ${newLang}`);
      return;
    }

    if (newLang !== language) {
      // 保存用户的语言偏好
      saveLanguagePreference(newLang);
      
      const currentPath = window.location.pathname;
      const newPath = currentPath.replace(`/${language}`, `/${newLang}`);
      navigate(newPath);
    }
  };

  return { setLanguage };
};

// 导出配置相关的函数和类型，以便其他组件使用
export type { Language } from '@/config/languages';
export { 
  getPreferredLanguage,
  SUPPORTED_LANGUAGES,
  isValidLanguage 
} from '@/config/languages';

export type { TranslationKeys } from '@/locales';