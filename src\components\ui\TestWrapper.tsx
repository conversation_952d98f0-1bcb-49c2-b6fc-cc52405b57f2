import React, { useState } from "react";

interface TestResult {
  passed: boolean;
  details?: any;
  failureReason?: string;
}

interface TestWrapperProps {
  children: React.ReactNode;
  onTestComplete?: (result: TestResult) => void;
  defaultResult?: TestResult;
}

export const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  onTestComplete,
  defaultResult
}) => {
  const [testResult, setTestResult] = useState<TestResult | undefined>(defaultResult);

  // 提供给子组件的回调函数
  const handleTestResult = (result: TestResult) => {
    setTestResult(result);
    onTestComplete?.(result);
  };

  // 通过 React.cloneElement 将回调函数传递给子组件
  const enhancedChildren = React.isValidElement(children)
    ? React.cloneElement(children, {
        onTestResult: handleTestResult,
        testResult: testResult
      } as any)
    : children;

  return <>{enhancedChildren}</>;
}; 