import React from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { CameraTestModule } from "@/components/tests/CameraTestModule";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { SEOFooter } from "@/components/seo/SEOFooter";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";

export const CameraTestPage: React.FC = () => {
  const { t } = useLanguage();
  const seoConfig = generatePageSEO('camera', t, window.location.origin);

  return (
    <MainLayout fullWidth seoConfig={seoConfig}>
      <CameraTestModule />
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="camera" />
        <FAQ pageType="camera" />
        <Glossary pageType="camera" />
        <TroubleshootingGuide pageType="camera" />
        <SEOFooter pageType="camera" />
      </div>
    </MainLayout>
  );
};