# Technical Stack

## Core Technologies
- **Framework**: React 18
- **Language**: TypeScript
- **Build System**: Vite
- **Styling**: Tailwind CSS with shadcn/ui components
- **Routing**: React Router DOM (v6)
- **State Management**: React Query (TanStack Query)
- **Form Handling**: React Hook Form with Zod validation

## UI Component Library
- **Component System**: shadcn/ui (Radix UI primitives)
- **Icons**: Lucide React
- **Animations**: Tailwind CSS Animate
- **Toast Notifications**: Sonner
- **Date Handling**: date-fns and React Day Picker
- **Charts**: Recharts
- **Carousel**: Embla Carousel

## Project Configuration
- **Path Aliases**: `@/` maps to `./src/`
- **TypeScript**: Strict mode with some exceptions (see tsconfig.json)
- **ESLint**: ESLint v9 with React plugins
- **PostCSS**: For Tailwind processing
- **SWC**: Fast compilation via Vite plugin

## Development Tools
- **Component Tagging**: lovable-tagger (development mode only)
- **Tailwind Plugins**: Typography plugin for rich text

## Common Commands

### Development
```bash
# Start development server
npm run dev

# Run linting
npm run lint
```

### Building
```bash
# Production build
npm run build

# Development build
npm run build:dev

# Preview production build
npm run preview
```

## Environment Setup
- **Server Port**: 8080
- **Host**: `::` (IPv6 support)
- **Node Version**: Compatible with Node.js v18+

## Deployment
- Deployed via Lovable platform
- Custom domain support available through project settings
- Static site hosting