import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getPreferredLanguage } from '@/config/languages';

const RootRedirect = () => {
    const navigate = useNavigate();
    useEffect(() => {
        const userLang = getPreferredLanguage();
        navigate(`/${userLang}`, { replace: true });
    }, [navigate]);

    return null; // Or a loading spinner
};

export default RootRedirect; 