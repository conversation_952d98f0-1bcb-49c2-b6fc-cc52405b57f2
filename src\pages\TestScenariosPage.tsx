import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Video,
  Headphones,
  ArrowRight,
  CheckCircle,
  Users,
  Clock,
  Target,
  Radio,
  Search
} from "lucide-react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { GlassCard } from "@/components/ui/GlassCard";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";

export const TestScenariosPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  
  // 生成SEO配置
  const seoConfig = generatePageSEO('test', t, window.location.origin);

  // 测试场景配置
  const scenarios = [
    {
      id: "meeting",
      title: t("onlineMeeting"),
      description: t("onlineMeetingDesc"),
      icon: Video,
      color: 'from-blue-500 to-cyan-500',
      features: [
        t("networkQualityTest"),
        t("cameraTest"),
        t("microphoneTest"),
        t("headphonesTest")
      ],
      duration: t("duration5to8"),
      difficulty: t("difficultyBeginner"),
      primary: true,
      usage: t("usageMeeting")
    },
    {
      id: "gaming",
      title: t("gamingSetup"),
      description: t("gamingSetupDesc"),
      icon: Headphones,
      color: 'from-purple-500 to-pink-500',
      features: [
        t("networkQualityTest"),
        t("keyboardTest"),
        t("mouseTest"),
        t("headphonesTest"),
        t("microphoneTest")
      ],
      duration: t("duration8to12"),
      difficulty: t("difficultyAdvanced"),
      primary: false,
      usage: t("usageGaming")
    },
    {
      id: "streaming",
      title: t("streamingScenario"),
      description: t("streamingScenarioDesc"),
      icon: Radio,
      color: 'from-pink-500 to-rose-500',
      features: [
        t("networkQualityTest"),
        t("cameraTest"),
        t("microphoneTest"),
        t("headphonesTest")
      ],
      duration: t("duration6to10"),
      difficulty: t("difficultyIntermediate"),
      primary: false,
      usage: t("usageStreaming")
    },
    {
      id: "diagnostic",
      title: t("diagnosticScenario"),
      description: t("diagnosticScenarioDesc"),
      icon: Search,
      color: 'from-orange-500 to-red-500',
      features: [
        t("networkQualityTest"),
        t("cameraTest"),
        t("microphoneTest"),
        t("keyboardTest"),
        t("mouseTest"),
        t("headphonesTest")
      ],
      duration: t("duration10to15"),
      difficulty: t("difficultyComprehensive"),
      primary: false,
      usage: t("usageDiagnostic")
    }
  ];

  const handleScenarioSelect = (scenarioId: string) => {
    const path = `/test/${scenarioId}`;
    const fullPath = language === 'en' ? path : `/${language}${path}`;
    navigate(fullPath);
  };

  return (
    <MainLayout seoConfig={seoConfig}>
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          {t('selectScenario')}
        </h1>
        <p className="text-xl text-white/80 max-w-3xl mx-auto">
          {t("selectScenarioDesc")}
        </p>
      </div>

      {/* 场景网格 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {scenarios.map((scenario) => {
          const IconComponent = scenario.icon;
          
          return (
            <GlassCard
              key={scenario.id}
              className="group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              onClick={() => handleScenarioSelect(scenario.id)}
            >
              {/* 渐变背景 */}
              <div 
                className={`absolute inset-0 bg-gradient-to-br ${scenario.color} opacity-10 rounded-2xl group-hover:opacity-20 transition-opacity duration-300`}
              />
              
              {/* 推荐标签 */}
              {scenario.primary && (
                <div className="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
                  {t("recommended")}
                </div>
              )}
              
              {/* 内容 */}
              <div className="relative flex flex-col h-full p-6 lg:p-8">
                {/* 图标和标题 */}
                <div className="flex items-center mb-6">
                  <div className={`p-4 rounded-xl bg-gradient-to-br ${scenario.color} mr-4`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl lg:text-2xl font-semibold text-white group-hover:text-blue-300 transition-colors">
                      {scenario.title}
                    </h3>
                    <p className="text-white/60 text-sm mt-1">{scenario.usage}</p>
                  </div>
                </div>

                {/* 描述 */}
                <p className="text-white/70 mb-6 text-base leading-relaxed">
                  {scenario.description}
                </p>

                {/* 测试项目 */}
                <div className="mb-6 flex-1">
                  <h4 className="text-white font-medium mb-3 flex items-center">
                    <Target className="w-4 h-4 mr-2" />
                    {t("includedTests")}
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {scenario.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-white/60">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 元信息 */}
                <div className="flex items-center justify-between mb-6 text-sm">
                  <div className="flex items-center text-white/50">
                    <Clock className="w-4 h-4 mr-1" />
                    <span>{scenario.duration}</span>
                  </div>
                  <div className="flex items-center text-white/50">
                    <Users className="w-4 h-4 mr-1" />
                    <span>{scenario.difficulty}</span>
                  </div>
                </div>

                {/* 行动按钮 */}
                <div className="flex items-center justify-between mt-auto">
                  <div className="text-white/50 text-sm">
                    {t("completeHardwareCheck")}
                  </div>

                  <div className="flex items-center text-blue-400 group-hover:text-blue-300 transition-colors">
                    <span className="text-sm font-medium mr-1">{t('startTest')}</span>
                    <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </GlassCard>
          );
        })}
      </div>

      {/* 快速访问工具 */}
      <div className="text-center">
        <GlassCard className="p-8">
          <h2 className="text-2xl font-semibold text-white mb-4">
            {t("needIndividualTesting")}
          </h2>
          <p className="text-white/70 mb-6 max-w-2xl mx-auto">
            {t("individualTestingDesc")}
          </p>
          <button
            onClick={() => {
              const path = '/tools';
              const fullPath = language === 'en' ? path : `/${language}${path}`;
              navigate(fullPath);
            }}
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-xl hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
          >
            {t("browseIndividualTools")}
            <ArrowRight className="w-4 h-4 ml-2" />
          </button>
        </GlassCard>
      </div>
    </MainLayout>
  );
}; 