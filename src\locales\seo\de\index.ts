/**
 * De<PERSON>ch - <PERSON>upt-SEO-Übersetzungsexport
 * Kombiniert alle deutschen Übersetzungen in einem einheitlichen Objekt
 */

import { deEnhanced } from './enhanced';
import { deFooter } from './footer';
import { deKeywords } from './keywords';
import { deFAQ } from './faq';
import { deGlossary } from './glossary';
import { deTroubleshooting } from './troubleshooting';
import type { SEOTranslation } from '../types';

export const deTranslation: SEOTranslation = {
  enhanced: deEnhanced,
  seoFooter: deFooter,
  seoKeywords: deKeywords,
  faq: deFAQ,
  glossary: deGlossary,
  troubleshooting: deTroubleshooting
};

// Exportiere individuelle Module für direkten Gebrauch
export {
  deEnhanced,
  deFooter,
  deKeywords,
  deFAQ,
  deGlossary,
  deTroubleshooting
};
