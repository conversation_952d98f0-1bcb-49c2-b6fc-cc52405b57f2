import React, { useState } from 'react';
import { TestStepController } from '@/components/ui/TestStepController';
import { useWorkflowManager } from '@/hooks/useWorkflowManager';
import { getScenarioConfig } from '@/config/testScenarios';
import { 
  NavigationAction, 
  TestStatus, 
  EnhancedTestResult 
} from '@/types/testWorkflow';
import { useLanguage } from '@/hooks/useLanguage';
import { MainLayout } from '@/components/layouts/MainLayout';
import { GlassCard } from '@/components/ui/GlassCard';
import { PrimaryButton } from '@/components/ui/PrimaryButton';

export const WorkflowDemoPage: React.FC = () => {
  const { t } = useLanguage();
  const [selectedScenario, setSelectedScenario] = useState('meeting');
  
  // 获取场景配置
  const scenarioConfig = getScenarioConfig(selectedScenario);
  
  // 使用工作流程管理器
  const workflowManager = useWorkflowManager({
    steps: scenarioConfig.steps
  });

  const currentStep = workflowManager.getCurrentStep();
  const navigationControl = workflowManager.getNavigationControl();
  const currentStepKey = currentStep?.config.key || 'unknown';

  // 模拟测试结果
  const simulateTestResult = (passed: boolean) => {
    const result: EnhancedTestResult = {
      status: passed ? TestStatus.COMPLETED : TestStatus.FAILED,
      passed,
      details: {
        simulatedTest: true,
        timestamp: new Date().toISOString()
      },
      failureReason: passed ? undefined : 'Simulated test failure for demonstration',
      timestamp: new Date()
    };

    workflowManager.updateStepResult(workflowManager.state.currentStepIndex, result);
  };

  // 处理导航操作
  const handleAction = (action: NavigationAction, result?: EnhancedTestResult) => {
    workflowManager.executeAction(action, result);
  };

  // 重置工作流程
  const resetWorkflow = () => {
    // 重置到第一步
    while (workflowManager.state.currentStepIndex > 0) {
      workflowManager.executeAction(NavigationAction.BACK);
    }
    // 清除所有结果
    workflowManager.state.steps.forEach((_, index) => {
      workflowManager.resetStep(index);
    });
  };

  const renderTestContent = () => {
    if (currentStepKey === 'summary') {
      return (
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-bold text-white">测试完成！</h3>
          <div className="bg-white/5 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-white mb-4">测试结果摘要</h4>
            <div className="space-y-2">
              {workflowManager.state.steps.slice(0, -1).map((step, index) => (
                <div key={step.config.key} className="flex justify-between items-center">
                  <span className="text-white/80">{t(step.config.title)}</span>
                  <span className={`font-medium ${
                    step.result?.passed ? 'text-green-400' : 
                    step.result?.status === TestStatus.SKIPPED ? 'text-yellow-400' : 
                    'text-red-400'
                  }`}>
                    {step.result?.status === TestStatus.COMPLETED && step.result.passed ? '✅ 通过' :
                     step.result?.status === TestStatus.SKIPPED ? '⏭️ 跳过' :
                     step.result?.status === TestStatus.FAILED ? '❌ 失败' :
                     '⏳ 未测试'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-bold text-white mb-2">
            {t(currentStep?.config.title || 'unknown')} 演示
          </h3>
          <p className="text-white/70">
            这是一个演示页面，用于测试新的工作流程导航逻辑
          </p>
        </div>

        <div className="bg-white/5 rounded-xl p-6">
          <h4 className="text-lg font-semibold text-white mb-4">当前步骤配置</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white/60">允许失败后继续：</span>
              <span className="text-white ml-2">
                {currentStep?.config.allowProceedOnFailure ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div>
              <span className="text-white/60">可以跳过：</span>
              <span className="text-white ml-2">
                {currentStep?.config.canSkip ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div>
              <span className="text-white/60">需要用户确认：</span>
              <span className="text-white ml-2">
                {currentStep?.config.requiresUserConfirmation ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div>
              <span className="text-white/60">最大重试次数：</span>
              <span className="text-white ml-2">
                {currentStep?.config.maxRetries || 3}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white/5 rounded-xl p-6">
          <h4 className="text-lg font-semibold text-white mb-4">模拟测试操作</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <PrimaryButton
              onClick={() => simulateTestResult(true)}
              className="w-full"
            >
              ✅ 模拟测试通过
            </PrimaryButton>
            <PrimaryButton
              onClick={() => simulateTestResult(false)}
              variant="secondary"
              className="w-full"
            >
              ❌ 模拟测试失败
            </PrimaryButton>
            <PrimaryButton
              onClick={() => handleAction(NavigationAction.SKIP)}
              variant="outline"
              className="w-full border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10"
            >
              ⏭️ 跳过此步骤
            </PrimaryButton>
          </div>
          <div className="mt-4 text-sm text-white/60">
            💡 提示：您可以通过上方按钮模拟不同的测试结果，或一键跳过当前测试步骤
          </div>
        </div>

        {currentStep?.result && (
          <div className="bg-white/5 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-white mb-4">当前测试结果</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-white/60">状态：</span>
                <span className="text-white ml-2">{currentStep.result.status}</span>
              </div>
              <div>
                <span className="text-white/60">是否通过：</span>
                <span className="text-white ml-2">
                  {currentStep.result.passed ? '✅ 是' : '❌ 否'}
                </span>
              </div>
              {currentStep.result.failureReason && (
                <div>
                  <span className="text-white/60">失败原因：</span>
                  <span className="text-red-300 ml-2">{currentStep.result.failureReason}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <MainLayout>
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            工作流程导航演示
          </h1>
          <p className="text-xl text-white/80">
            测试新的步骤导航逻辑和用户体验改进
          </p>
        </div>

        {/* 场景选择 */}
        <GlassCard className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">选择测试场景</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['meeting', 'gaming', 'streaming', 'diagnostic'].map((scenario) => (
              <PrimaryButton
                key={scenario}
                onClick={() => {
                  setSelectedScenario(scenario);
                  resetWorkflow();
                }}
                variant={selectedScenario === scenario ? 'default' : 'outline'}
                className="w-full"
              >
                {scenario === 'meeting' ? '会议' :
                 scenario === 'gaming' ? '游戏' :
                 scenario === 'streaming' ? '直播' : '诊断'}
              </PrimaryButton>
            ))}
          </div>
        </GlassCard>

        {/* 工作流程状态 */}
        <GlassCard className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">工作流程状态</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-white/60">当前步骤：</span>
              <span className="text-white ml-2">
                {workflowManager.state.currentStepIndex + 1} / {workflowManager.state.steps.length}
              </span>
            </div>
            <div>
              <span className="text-white/60">是否完成：</span>
              <span className="text-white ml-2">
                {workflowManager.state.isComplete ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div>
              <span className="text-white/60">可以前进：</span>
              <span className="text-white ml-2">
                {navigationControl.canGoNext ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div>
              <span className="text-white/60">可以后退：</span>
              <span className="text-white ml-2">
                {navigationControl.canGoBack ? '✅ 是' : '❌ 否'}
              </span>
            </div>
          </div>
        </GlassCard>

        {/* 主要测试区域 */}
        <TestStepController
          onAction={handleAction}
          navigationControl={navigationControl}
          testResult={currentStep?.result}
          stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
          stepDescription={currentStep?.config.description ? t(currentStep.config.description) : undefined}
          showProgress={true}
          currentStep={workflowManager.state.currentStepIndex + 1}
          totalSteps={workflowManager.state.steps.length}
        >
          {renderTestContent()}
        </TestStepController>

        {/* 重置按钮 */}
        <div className="text-center mt-8">
          <PrimaryButton onClick={resetWorkflow} variant="outline">
            重置演示
          </PrimaryButton>
        </div>
      </div>
    </MainLayout>
  );
};
