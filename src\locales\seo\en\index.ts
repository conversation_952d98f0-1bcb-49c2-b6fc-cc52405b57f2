/**
 * English - SEO Translation Module Index
 * Unified export of all English SEO translation content
 */

import type { SEOTranslation } from '../types';
import { enEnhanced } from './enhanced';
import { enFooter } from './footer';
import { enKeywords } from './keywords';
import { enFAQ } from './faq';
import { enGlossary } from './glossary';
import { enTroubleshooting } from './troubleshooting';



export const enTranslation: SEOTranslation = {
  enhanced: enEnhanced,
  seoFooter: enFooter,
  seoKeywords: enKeywords,
  faq: enFAQ,
  glossary: enGlossary,
  troubleshooting: enTroubleshooting
};

// 保持向后兼容的导出
export const enSEO = enTranslation;

// 单独导出各模块，便于按需导入
export { enEnhanced, enFooter, enKeywords };
