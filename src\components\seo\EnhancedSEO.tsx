import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { GlassCard } from "@/components/ui/GlassCard";
import { 
  BookOpen, 
  Settings, 
  TrendingUp, 
  Shield, 
  Zap, 
  CheckCircle2,
  AlertTriangle,
  Info,
  Lightbulb,
  Target,
  BarChart3,
  Cpu
} from "lucide-react";
import { getSEOTranslation } from "@/locales/seo";

interface EnhancedSEOProps {
  pageType: 'home' | 'tools' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';
}

export const EnhancedSEO: React.FC<EnhancedSEOProps> = ({ pageType }) => {
  const { language } = useLanguage();

  // 使用指南组件
  const UsageGuide: React.FC = () => {
    const steps = getSEOTranslation(language, `enhanced.usageGuide.${pageType}.steps`) || [];
    
    return (
      <GlassCard className="p-6 mt-8">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-blue-400" />
          {getSEOTranslation(language, 'enhanced.usageGuide.title')}
        </h3>
        
        <div className="space-y-4">
          {steps.map((step: string, index: number) => (
            <div key={index} className="flex gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center text-blue-300 text-sm font-medium">
                {index + 1}
              </div>
              <div className="flex-1">
                <p className="text-white/90 text-sm leading-relaxed">
                  {step}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* 提示信息 */}
        <div className="mt-6 p-4 bg-blue-500/10 border border-blue-400/30 rounded-lg">
          <div className="flex items-start gap-3">
            <Lightbulb className="h-5 w-5 text-yellow-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-white font-medium mb-1">
                {getSEOTranslation(language, 'enhanced.usageGuide.tips.title')}
              </h4>
              <p className="text-white/70 text-sm">
                {getSEOTranslation(language, `enhanced.usageGuide.${pageType}.tip`)}
              </p>
            </div>
          </div>
        </div>
      </GlassCard>
    );
  };

  // 技术规范组件
  const TechnicalSpecs: React.FC = () => {
    const specs = getSEOTranslation(language, `enhanced.technicalSpecs.${pageType}`) || {};
    
    return (
      <GlassCard className="p-6 mt-8">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
          <Cpu className="h-5 w-5 text-green-400" />
          {getSEOTranslation(language, 'enhanced.technicalSpecs.title')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 系统要求 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center gap-2">
              <Settings className="h-4 w-4 text-blue-400" />
              {getSEOTranslation(language, 'enhanced.technicalSpecs.systemRequirements')}
            </h4>
            <div className="space-y-2">
              {(specs.systemRequirements || []).map((req: string, index: number) => (
                <div key={index} className="flex items-center gap-2 text-sm text-white/70">
                  <CheckCircle2 className="h-3 w-3 text-green-400 flex-shrink-0" />
                  <span>{req}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 技术参数 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-purple-400" />
              {getSEOTranslation(language, 'enhanced.technicalSpecs.parameters')}
            </h4>
            <div className="space-y-2">
              {Object.entries(specs.parameters || {}).map(([key, value], index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-white/70">{key}:</span>
                  <span className="text-white font-medium">{value as string}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 兼容性信息 */}
        <div className="mt-6 p-4 bg-green-500/10 border border-green-400/30 rounded-lg">
          <h4 className="text-white font-medium mb-2 flex items-center gap-2">
            <Shield className="h-4 w-4 text-green-400" />
            {getSEOTranslation(language, 'enhanced.technicalSpecs.compatibility')}
          </h4>
          <p className="text-white/70 text-sm">
            {specs.compatibilityNote}
          </p>
        </div>
      </GlassCard>
    );
  };

  // 最佳实践组件
  const BestPractices: React.FC = () => {
    const practices = getSEOTranslation(language, `enhanced.bestPractices.${pageType}`) || {};
    
    return (
      <GlassCard className="p-6 mt-8">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
          <Target className="h-5 w-5 text-orange-400" />
          {getSEOTranslation(language, 'enhanced.bestPractices.title')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 推荐做法 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-400" />
              {getSEOTranslation(language, 'enhanced.bestPractices.recommended')}
            </h4>
            <div className="space-y-3">
              {(practices.dos || []).map((item: string, index: number) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                  <span className="text-white/70 text-sm">{item}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 避免事项 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              {getSEOTranslation(language, 'enhanced.bestPractices.avoid')}
            </h4>
            <div className="space-y-3">
              {(practices.donts || []).map((item: string, index: number) => (
                <div key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-400 flex-shrink-0 mt-0.5" />
                  <span className="text-white/70 text-sm">{item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 性能优化提示 */}
        <div className="mt-6 p-4 bg-orange-500/10 border border-orange-400/30 rounded-lg">
          <h4 className="text-white font-medium mb-2 flex items-center gap-2">
            <Zap className="h-4 w-4 text-orange-400" />
            {getSEOTranslation(language, 'enhanced.bestPractices.optimization')}
          </h4>
          <p className="text-white/70 text-sm">
            {practices.optimizationTip}
          </p>
        </div>
      </GlassCard>
    );
  };

  // 行业标准组件
  const IndustryStandards: React.FC = () => {
    const standards = getSEOTranslation(language, `enhanced.industryStandards.${pageType}`) || {};
    
    return (
      <GlassCard className="p-6 mt-8">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-cyan-400" />
          {getSEOTranslation(language, 'enhanced.industryStandards.title')}
        </h3>
        
        <div className="space-y-4">
          {(standards.list || []).map((standard: any, index: number) => (
            <div key={index} className="border-l-2 border-cyan-400/50 pl-4">
              <h4 className="text-white font-medium mb-1">{standard.name}</h4>
              <p className="text-white/70 text-sm mb-2">{standard.description}</p>
              {standard.requirement && (
                <div className="flex items-center gap-2 text-xs">
                  <Info className="h-3 w-3 text-cyan-400" />
                  <span className="text-cyan-300">{standard.requirement}</span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 合规性说明 */}
        <div className="mt-6 p-4 bg-cyan-500/10 border border-cyan-400/30 rounded-lg">
          <h4 className="text-white font-medium mb-2">
            {getSEOTranslation(language, 'enhanced.industryStandards.compliance')}
          </h4>
          <p className="text-white/70 text-sm">
            {standards.complianceNote}
          </p>
        </div>
      </GlassCard>
    );
  };

  return (
    <div className="space-y-0">
      <UsageGuide />
      <TechnicalSpecs />
      <BestPractices />
      <IndustryStandards />
    </div>
  );
}; 