import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { GlassCard } from "@/components/ui/GlassCard";
import { getSEOTranslation } from "@/locales/seo";

interface SEOFooterProps {
  pageType: 'home' | 'tools' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';
}

export const SEOFooter: React.FC<SEOFooterProps> = ({ pageType }) => {
  const { t, language } = useLanguage();

  return (
    <div className="mt-16 space-y-8">
      {/* Technical Info */}
      <GlassCard className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          {getSEOTranslation(language, 'seoFooter.technicalInfo')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="text-white font-medium mb-2">{getSEOTranslation(language, 'seoFooter.supportedBrowsers')}</h4>
            <p className="text-white/70">{getSEOTranslation(language, 'seoFooter.browserCompatibility')}</p>
          </div>
          <div>
            <h4 className="text-white font-medium mb-2">{getSEOTranslation(language, 'seoFooter.systemRequirements')}</h4>
            <p className="text-white/70">{getSEOTranslation(language, 'seoFooter.requirements')}</p>
          </div>
        </div>
      </GlassCard>

      {/* About Section */}
      <GlassCard className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          {getSEOTranslation(language, 'seoFooter.aboutTitle')}
        </h3>
        <p className="text-white/70 text-sm leading-relaxed">
          {getSEOTranslation(language, `seoFooter.about.${pageType}`)}
        </p>
      </GlassCard>

      {/* Contact Information */}
      <GlassCard className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          {t('contactTitle')}
        </h3>
        <p className="text-white/70 text-sm leading-relaxed mb-4">
          {t('contactDescription')}
        </p>

        <div className="space-y-3">
          {/* Email Contact */}
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <p className="text-white/60 text-xs">{t('userFeedback')}</p>
              <a
                href={`mailto:<EMAIL>?subject=${encodeURIComponent(`Setup Check - ${t('userFeedback')}`)}&body=${encodeURIComponent(t('contactEmailTemplate'))}`}
                className="text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Feedback Types */}
          <div className="pt-2 border-t border-white/10">
            <p className="text-white/60 text-xs mb-2">{t('feedbackTypes')}:</p>
            <div className="flex flex-wrap gap-2">
              <span className="px-2 py-1 bg-white/10 rounded text-xs text-white/70">{t('bugReport')}</span>
              <span className="px-2 py-1 bg-white/10 rounded text-xs text-white/70">{t('featureRequest')}</span>
              <span className="px-2 py-1 bg-white/10 rounded text-xs text-white/70">{t('technicalSupport')}</span>
              <span className="px-2 py-1 bg-white/10 rounded text-xs text-white/70">{t('generalInquiry')}</span>
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  );
};