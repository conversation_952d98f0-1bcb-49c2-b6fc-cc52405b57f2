import React, { useState, useEffect, useRef } from "react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { Rota<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ircle, <PERSON><PERSON>ointer2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>evron<PERSON>p, ChevronDown, Circle } from "lucide-react";

import { useLanguage } from "@/hooks/useLanguage";

interface MouseEvent {
  type: string;
  button?: number;
  timestamp: number;
  x?: number;
  y?: number;
  deltaY?: number;
}

interface ClickSequence {
  button: number;
  downTime: number;
  upTime?: number;
  isComplete: boolean;
}

interface DoubleClickIssue {
  timestamp: number;
  button: number;
  interval: number;
  severity: 'minor' | 'moderate' | 'severe';
  description: string;
}

export const MouseTest: React.FC = () => {
  const { t } = useLanguage();
  const [mouseEvents, setMouseEvents] = useState<MouseEvent[]>([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [pressedButtons, setPressedButtons] = useState<Set<number>>(new Set());
  
  // 双击检测相关状态
  const [clickSequences, setClickSequences] = useState<ClickSequence[]>([]);
  const [doubleClickIssues, setDoubleClickIssues] = useState<DoubleClickIssue[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [testDuration, setTestDuration] = useState(0);
  
  const testAreaRef = useRef<HTMLDivElement>(null);
  const monitoringStartRef = useRef<number>(0);

  // 正常双击时间窗口（ms）
  const NORMAL_DOUBLE_CLICK_WINDOW = 500;
  const SUSPICIOUS_INTERVAL_THRESHOLD = 50; // 小于50ms的间隔被认为是可疑的

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isMonitoring) {
      interval = setInterval(() => {
        setTestDuration(Date.now() - monitoringStartRef.current);
      }, 100);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMonitoring]);

  useEffect(() => {
    const testArea = testAreaRef.current;
    if (!testArea) return;

    const handleMouseMove = (e: globalThis.MouseEvent) => {
      const rect = testArea.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    };

    const handleMouseDown = (e: globalThis.MouseEvent) => {
      e.preventDefault();
      setPressedButtons(prev => new Set(prev).add(e.button));
      
      const timestamp = Date.now();
      const newEvent: MouseEvent = {
        type: "mousedown",
        button: e.button,
        timestamp,
        x: e.clientX - testArea.getBoundingClientRect().left,
        y: e.clientY - testArea.getBoundingClientRect().top
      };
      
      setMouseEvents(prev => [newEvent, ...prev.slice(0, 19)]);

      // 双击检测逻辑
      if (isMonitoring) {
        // 检查是否有未完成的同按钮序列
        const existingSequence = clickSequences.find(seq => 
          seq.button === e.button && !seq.isComplete
        );

        if (existingSequence && existingSequence.upTime) {
          // 计算与上次抬起的时间间隔
          const interval = timestamp - existingSequence.upTime;
          
          if (interval < NORMAL_DOUBLE_CLICK_WINDOW) {
            let severity: 'minor' | 'moderate' | 'severe' = 'minor';
            let description = '';

            if (interval < 20) {
              severity = 'severe';
              description = t('severeHardwareFailureShortInterval');
            } else if (interval < SUSPICIOUS_INTERVAL_THRESHOLD) {
              severity = 'moderate';
              description = t('possibleHardwareFailureUnexpectedDoubleClick');
            } else {
              severity = 'minor';
              description = t('potentialIssueFastDoubleClick');
            }

            const issue: DoubleClickIssue = {
              timestamp,
              button: e.button,
              interval,
              severity,
              description
            };

            setDoubleClickIssues(prev => [issue, ...prev.slice(0, 9)]);
          }

          // 标记旧序列为完成
          setClickSequences(prev => 
            prev.map(seq => 
              seq.button === e.button && !seq.isComplete 
                ? { ...seq, isComplete: true }
                : seq
            )
          );
        }

        // 创建新的点击序列
        const newSequence: ClickSequence = {
          button: e.button,
          downTime: timestamp,
          isComplete: false
        };

        setClickSequences(prev => [newSequence, ...prev.slice(0, 19)]);
      }
    };

    const handleMouseUp = (e: globalThis.MouseEvent) => {
      setPressedButtons(prev => {
        const newSet = new Set(prev);
        newSet.delete(e.button);
        return newSet;
      });
      
      const timestamp = Date.now();
      const newEvent: MouseEvent = {
        type: "mouseup",
        button: e.button,
        timestamp,
        x: e.clientX - testArea.getBoundingClientRect().left,
        y: e.clientY - testArea.getBoundingClientRect().top
      };
      
      setMouseEvents(prev => [newEvent, ...prev.slice(0, 19)]);

      // 更新点击序列
      if (isMonitoring) {
        setClickSequences(prev => 
          prev.map(seq => 
            seq.button === e.button && !seq.upTime 
              ? { ...seq, upTime: timestamp }
              : seq
          )
        );
      }
    };

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      
      const newEvent: MouseEvent = {
        type: "wheel",
        timestamp: Date.now(),
        deltaY: e.deltaY,
        x: e.clientX - testArea.getBoundingClientRect().left,
        y: e.clientY - testArea.getBoundingClientRect().top
      };
      
      setMouseEvents(prev => [newEvent, ...prev.slice(0, 19)]);
    };

    const handleContextMenu = (e: globalThis.MouseEvent) => {
      e.preventDefault();
    };

    testArea.addEventListener("mousemove", handleMouseMove);
    testArea.addEventListener("mousedown", handleMouseDown);
    testArea.addEventListener("mouseup", handleMouseUp);
    testArea.addEventListener("wheel", handleWheel);
    testArea.addEventListener("contextmenu", handleContextMenu);

    return () => {
      testArea.removeEventListener("mousemove", handleMouseMove);
      testArea.removeEventListener("mousedown", handleMouseDown);
      testArea.removeEventListener("mouseup", handleMouseUp);
      testArea.removeEventListener("wheel", handleWheel);
      testArea.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [isMonitoring, clickSequences]);

  const getButtonName = (button: number): string => {
    switch (button) {
      case 0: return t("leftButton");
      case 1: return t("wheelButton");
      case 2: return t("rightButton");
      case 3: return t("sideButton1");
      case 4: return t("sideButton2");
      default: return `Button ${button}`;
    }
  };

  const getEventIcon = (event: MouseEvent) => {
    const baseIconClasses = "relative w-8 h-8 p-1.5 rounded-xl backdrop-blur-md border transition-all duration-300 overflow-hidden group cursor-pointer";
    
    if (event.type === "mousedown" || event.type === "mouseup") {
      const isDown = event.type === "mousedown";
      
      switch (event.button) {
        case 0: // 左键
          return (
            <div className={`${baseIconClasses} bg-blue-500/25 border-blue-400/50 text-blue-200 ${isDown ? 'shadow-xl shadow-blue-500/40 scale-110' : 'hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105'}`}>
              {/* 扫光效果 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              {/* 内部高光 */}
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-300/60 to-transparent" />
              <MousePointer2 className="w-full h-full relative z-10" />
            </div>
          );
        case 1: // 滚轮键
          return (
            <div className={`${baseIconClasses} bg-yellow-500/25 border-yellow-400/50 text-yellow-200 ${isDown ? 'shadow-xl shadow-yellow-500/40 scale-110' : 'hover:shadow-lg hover:shadow-yellow-500/30 hover:scale-105'}`}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-yellow-300/60 to-transparent" />
              <Circle className="w-full h-full relative z-10" />
            </div>
          );
        case 2: // 右键
          return (
            <div className={`${baseIconClasses} bg-green-500/25 border-green-400/50 text-green-200 ${isDown ? 'shadow-xl shadow-green-500/40 scale-110' : 'hover:shadow-lg hover:shadow-green-500/30 hover:scale-105'}`}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-green-300/60 to-transparent" />
              <MousePointer className="w-full h-full relative z-10" />
            </div>
          );
        default: // 其他按钮
          return (
            <div className={`${baseIconClasses} bg-purple-500/25 border-purple-400/50 text-purple-200 ${isDown ? 'shadow-xl shadow-purple-500/40 scale-110' : 'hover:shadow-lg hover:shadow-purple-500/30 hover:scale-105'}`}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-purple-300/60 to-transparent" />
              <Mouse className="w-full h-full relative z-10" />
            </div>
          );
      }
    } else if (event.type === "wheel") {
      const isScrollDown = event.deltaY! > 0;
      return (
        <div className={`${baseIconClasses} bg-orange-500/25 border-orange-400/50 text-orange-200 shadow-xl shadow-orange-500/40 animate-pulse hover:scale-105`}>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-orange-300/60 to-transparent" />
          {isScrollDown ? (
            <ChevronDown className="w-full h-full relative z-10" />
          ) : (
            <ChevronUp className="w-full h-full relative z-10" />
          )}
        </div>
      );
    }
    
    // 默认图标
    return (
      <div className={`${baseIconClasses} bg-gray-500/25 border-gray-400/50 text-gray-200 hover:shadow-lg hover:shadow-gray-500/30 hover:scale-105`}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent" />
        <Mouse className="w-full h-full relative z-10" />
      </div>
    );
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'severe': return 'border-red-400/50 bg-red-500/10 text-red-200';
      case 'moderate': return 'border-yellow-400/50 bg-yellow-500/10 text-yellow-200';
      case 'minor': return 'border-blue-400/50 bg-blue-500/10 text-blue-200';
      default: return 'border-gray-400/50 bg-gray-500/10 text-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'severe': return <AlertTriangle className="h-4 w-4 text-red-400" />;
      case 'moderate': return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'minor': return <AlertTriangle className="h-4 w-4 text-blue-400" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const toggleMonitoring = () => {
    if (!isMonitoring) {
      setIsMonitoring(true);
      monitoringStartRef.current = Date.now();
      setTestDuration(0);
    } else {
      setIsMonitoring(false);
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const getHealthStatus = () => {
    const severeIssues = doubleClickIssues.filter(issue => issue.severity === 'severe').length;
    const moderateIssues = doubleClickIssues.filter(issue => issue.severity === 'moderate').length;
    
    if (severeIssues > 0) {
      return { status: 'severe', message: t('detectedSevereHardwareFailure'), color: 'text-red-400' };
    } else if (moderateIssues > 2) {
      return { status: 'moderate', message: t('mouseMayHaveIssues'), color: 'text-yellow-400' };
    } else if (doubleClickIssues.length > 0) {
      return { status: 'minor', message: t('sporadicIssuesContinueMonitoring'), color: 'text-blue-400' };
    } else {
      return { status: 'good', message: t('mouseWorkingNormally'), color: 'text-green-400' };
    }
  };

  const reset = () => {
    setMouseEvents([]);
    setPressedButtons(new Set());
    setClickSequences([]);
    setDoubleClickIssues([]);
    setIsMonitoring(false);
    setTestDuration(0);
  };

  return (
    <div className="min-h-screen w-full">
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        <GlassCard className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t("mouseTestTitle2")}</h1>
              <p className="text-white/70">
                {t("mouseTestDesc2")}
              </p>
            </div>
            <div className="flex space-x-3">
              <PrimaryButton 
                onClick={toggleMonitoring} 
                variant={isMonitoring ? "secondary" : "primary"}
                size="sm"
              >
                {isMonitoring ? t("stopMonitoring") : t("startDoubleClickDetection")}
              </PrimaryButton>
              <PrimaryButton onClick={reset} variant="outline" size="sm">
                <RotateCcw className="h-4 w-4" />
                {t("resetTest")}
              </PrimaryButton>
            </div>
          </div>

          {/* 双击检测状态栏 */}
          {isMonitoring && (
            <div className="mb-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="text-blue-200 font-medium">{t("doubleClickDetectionInProgress")}</span>
                  </div>
                  <div className="text-blue-200/80">
                    {t("duration")} {formatDuration(testDuration)}
                  </div>
                </div>
                <div className="text-right">
                  <div className={`font-medium ${getHealthStatus().color}`}>
                    {getHealthStatus().message}
                  </div>
                  <div className="text-white/60 text-sm">
                    {t("detected")} {doubleClickIssues.length} {t("issues")}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Mouse Test Area */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">{t("testArea")}</h3>
              <div
                ref={testAreaRef}
                className="relative bg-white/5 border-2 border-dashed border-white/30 rounded-xl h-80 overflow-hidden cursor-crosshair"
                style={{ userSelect: "none" }}
              >
                {/* Mouse Position Indicator */}
                <div
                  className="absolute w-4 h-4 pointer-events-none transition-all duration-75"
                  style={{
                    left: mousePosition.x - 8,
                    top: mousePosition.y - 8,
                    transform: pressedButtons.size > 0 ? "scale(1.5)" : "scale(1)",
                  }}
                >
                  <div className={`w-full h-full rounded-full border-2 ${
                    pressedButtons.size > 0 
                      ? "bg-red-400 border-red-300" 
                      : "bg-blue-400 border-blue-300"
                  }`} />
                </div>

                {/* Instructions */}
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="text-center text-white/50">
                    <Mouse className="h-12 w-12 mx-auto mb-4" />
                    <p className="text-lg font-medium mb-2">
                      {isMonitoring ? t("performSingleClickTest") : t("moveClickScroll")}
                    </p>
                    <p className="text-sm">
                      {isMonitoring ? t("tryNormalSingleClickOperations") : t("tryMouseButtons")}
                    </p>
                    {isMonitoring && (
                      <p className="text-xs text-yellow-300 mt-2">
                        {t("systemWillAutoDetectDoubleClickIssues")}
                      </p>
                    )}
                  </div>
                </div>

                {/* Current Position Display */}
                <div className="absolute top-4 left-4 bg-white/10 rounded-lg px-3 py-2 text-sm text-white">
                  {t("position")} {Math.round(mousePosition.x)}, {Math.round(mousePosition.y)}
                </div>
              </div>
            </div>

            {/* 双击问题检测结果或事件历史 */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">
                {isMonitoring ? t("doubleClickIssueDetection") : t("eventHistory")}
              </h3>
              <div className="bg-white/5 rounded-xl p-4 h-80 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/20 hover:scrollbar-thumb-white/30">
                {isMonitoring ? (
                  // 双击问题检测结果
                  doubleClickIssues.length === 0 ? (
                    <div className="text-center text-white/50 mt-8">
                      <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-400" />
                      <p>{t("noDoubleClickIssuesDetected")}</p>
                      <p className="text-sm mt-2">{t("continueWithSingleClickTesting")}</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {doubleClickIssues.map((issue, index) => (
                        <div
                          key={`issue-${issue.timestamp}-${index}`}
                          className={`rounded-lg p-3 border ${getSeverityColor(issue.severity)}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3">
                              {getSeverityIcon(issue.severity)}
                              <div>
                                <div className="font-medium text-sm">
                                  {getButtonName(issue.button)} - {issue.description}
                                </div>
                                <div className="text-xs opacity-80 mt-1">
                                  {t("interval")} {issue.interval}ms ({t("normal")} &gt;{SUSPICIOUS_INTERVAL_THRESHOLD}ms)
                                </div>
                              </div>
                            </div>
                            <span className="text-xs opacity-60">
                              {new Date(issue.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )
                ) : (
                  // 常规事件历史
                  mouseEvents.length === 0 ? (
                    <div className="text-center text-white/50 mt-8">
                      <p>{t("noMouseEvents")}</p>
                      <p className="text-sm mt-2">{t("startMovingClicking")}</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {mouseEvents.map((event, index) => (
                        <div
                          key={`${event.type}-${event.timestamp}-${index}`}
                          className="flex items-center justify-between text-sm bg-white/5 rounded-lg p-2"
                        >
                                                  <div className="flex items-center space-x-3">
                          {getEventIcon(event)}
                            <div>
                              <div className="text-white font-medium">
                                {event.type === "wheel" 
                                  ? `${event.deltaY! > 0 ? t("scrollDown") : t("scrollUp")}`
                                  : `${event.type} ${event.button !== undefined ? getButtonName(event.button) : ""}`
                                }
                              </div>
                              {event.x !== undefined && event.y !== undefined && (
                                <div className="text-white/60 text-xs">
                                  at ({Math.round(event.x)}, {Math.round(event.y)})
                                </div>
                              )}
                            </div>
                          </div>
                          <span className="text-white/40 text-xs">
                            {new Date(event.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  )
                )}
              </div>
            </div>
          </div>

          {/* Button Status - 跨越整个底部 */}
          <div className="mt-6">
            <h4 className="text-white font-medium mb-3 text-center">{t("buttonStatus")}</h4>
            <div className="flex w-full gap-2">
              {[
                { button: 0, name: t("leftButton"), gradient: "from-blue-500/30 via-blue-400/20 to-blue-600/30", glow: "shadow-blue-500/50" },
                { button: 1, name: t("wheelButton"), gradient: "from-yellow-500/30 via-yellow-400/20 to-amber-600/30", glow: "shadow-yellow-500/50" },
                { button: 2, name: t("rightButton"), gradient: "from-green-500/30 via-green-400/20 to-emerald-600/30", glow: "shadow-green-500/50" },
                { button: 3, name: t("sideButton1"), gradient: "from-purple-500/30 via-purple-400/20 to-violet-600/30", glow: "shadow-purple-500/50" },
                { button: 4, name: t("sideButton2"), gradient: "from-orange-500/30 via-orange-400/20 to-red-600/30", glow: "shadow-orange-500/50" }
              ].map(({ button, name, gradient, glow }) => (
                <div
                  key={button}
                  className={`flex-1 relative overflow-hidden rounded-2xl transition-all duration-500 transform group ${
                    pressedButtons.has(button)
                      ? `bg-gradient-to-br ${gradient} border border-white/60 shadow-2xl ${glow} scale-105 backdrop-blur-md`
                      : "bg-white/5 border border-white/20 hover:bg-white/10 hover:border-white/30 hover:scale-102 backdrop-blur-sm"
                  }`}
                >
                  {/* 发光效果背景 */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 transition-opacity duration-500 ${
                    pressedButtons.has(button) ? "opacity-100" : "group-hover:opacity-30"
                  }`} />
                  
                  {/* 扫光效果 */}
                  <div className={`absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -translate-x-full transition-transform duration-700 ${
                    pressedButtons.has(button) ? "translate-x-full" : "group-hover:translate-x-full"
                  }`} />
                  
                  {/* 按钮内容 */}
                  <div className="relative z-10 p-4 text-center">
                    {/* 状态指示灯 */}
                    <div className="flex justify-center mb-2">
                      <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        pressedButtons.has(button)
                          ? "bg-white shadow-lg shadow-white/50 animate-pulse"
                          : "bg-white/30 group-hover:bg-white/50"
                      }`} />
                    </div>
                    
                    {/* 按钮名称 */}
                    <div className={`font-semibold text-sm mb-1 transition-colors duration-300 ${
                      pressedButtons.has(button) ? "text-white" : "text-white/70 group-hover:text-white"
                    }`}>
                      {name}
                    </div>
                    
                    {/* 状态文本 */}
                    <div className={`text-xs transition-colors duration-300 ${
                      pressedButtons.has(button) 
                        ? "text-white font-medium" 
                        : "text-white/50 group-hover:text-white/70"
                    }`}>
                      {pressedButtons.has(button) ? t("pressed") : t("released")}
                    </div>
                  </div>

                  {/* 边缘高光 */}
                  <div className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent transition-opacity duration-300 ${
                    pressedButtons.has(button) ? "opacity-100" : "opacity-0 group-hover:opacity-60"
                  }`} />
                </div>
              ))}
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <h4 className="text-blue-200 font-medium mb-2">
              {isMonitoring ? t("doubleClickDetectionInstructions") : t("testingTips")}
            </h4>
            {isMonitoring ? (
              <ul className="text-blue-200/80 text-sm space-y-1">
                <li>• {t("doubleClickTip1")}</li>
                <li>• {t("doubleClickTip2")}</li>
                <li>• {t("doubleClickTip3")}</li>
                <li>• {t("doubleClickTip4")}</li>
                <li>• {t("doubleClickTip5")}</li>
              </ul>
            ) : (
              <ul className="text-blue-200/80 text-sm space-y-1">
                <li>{t("mouseTip1")}</li>
                <li>{t("mouseTip2")}</li>
                <li>{t("mouseTip3")}</li>
                <li>{t("mouseTip4")}</li>
                <li>{t("mouseTip5")}</li>
                <li>• {t("doubleClickTip6")}</li>
              </ul>
            )}
          </div>
        </GlassCard>
      </main>
    </div>
  );
};