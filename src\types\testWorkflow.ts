/**
 * 测试工作流程相关的类型定义
 */

// 测试状态枚举
export enum TestStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

// 增强的测试结果接口
export interface EnhancedTestResult {
  status: TestStatus;
  passed: boolean;
  details?: any;
  failureReason?: string;
  timestamp?: Date;
  // 控制选项
  allowProceedOnFailure?: boolean;  // 是否允许失败后继续
  canRetry?: boolean;               // 是否可以重试
  requiresUserConfirmation?: boolean; // 是否需要用户确认
  autoAdvance?: boolean;            // 是否自动进入下一步
}

// 测试步骤配置
export interface TestStepConfig {
  key: string;
  title: string;
  description?: string;
  number: number;
  // 导航控制
  allowProceedOnFailure?: boolean;
  requiresUserConfirmation?: boolean;
  canSkip?: boolean;
  autoAdvance?: boolean;
  // 重试配置
  maxRetries?: number;
  retryDelay?: number;
}

// 测试步骤状态
export interface TestStepState {
  config: TestStepConfig;
  result?: EnhancedTestResult;
  retryCount: number;
  isActive: boolean;
}

// 工作流程状态
export interface WorkflowState {
  currentStepIndex: number;
  steps: TestStepState[];
  isComplete: boolean;
  overallResult?: {
    passed: boolean;
    completedSteps: number;
    failedSteps: number;
    skippedSteps: number;
  };
}

// 导航操作类型
export enum NavigationAction {
  NEXT = 'next',
  BACK = 'back',
  RETRY = 'retry',
  SKIP = 'skip',
  COMPLETE = 'complete'
}

// 导航控制接口
export interface NavigationControl {
  canGoNext: boolean;
  canGoBack: boolean;
  canRetry: boolean;
  canSkip: boolean;
  nextButtonText?: string;
  backButtonText?: string;
  showRetryButton?: boolean;
  showSkipButton?: boolean;
}

// 测试组件通用接口
export interface TestComponentProps {
  onTestResult?: (result: EnhancedTestResult) => void;
  onStatusChange?: (status: TestStatus) => void;
  allowRetry?: boolean;
  config?: TestStepConfig;
}

// 步骤类型定义
export type MeetingTestStep = "network" | "microphone" | "camera" | "headphones" | "summary";
export type GamingTestStep = "network" | "microphone" | "keyboard" | "mouse" | "headphones" | "summary";
export type StreamingTestStep = "network" | "microphone" | "camera" | "headphones" | "summary";
export type DiagnosticTestStep = "network" | "microphone" | "camera" | "keyboard" | "mouse" | "headphones" | "summary";
export type TestStep = MeetingTestStep | GamingTestStep | StreamingTestStep | DiagnosticTestStep;

// 场景配置
export interface ScenarioConfig {
  id: string;
  name: string;
  steps: TestStepConfig[];
}

// 工作流程管理器接口
export interface WorkflowManager {
  state: WorkflowState;
  getCurrentStep: () => TestStepState | undefined;
  getNavigationControl: () => NavigationControl;
  executeAction: (action: NavigationAction, result?: EnhancedTestResult) => boolean;
  updateStepResult: (stepIndex: number, result: EnhancedTestResult) => void;
  resetStep: (stepIndex: number) => void;
  canProceedToNext: () => boolean;
}
