---
alwaysApply: false
---
# 状态管理

项目中的状态管理分为两类：客户端状态和服务器状态。

## 服务器状态 (Tanstack Query)

对于与服务器交互的数据（例如，API 请求），项目使用 `@tanstack/react-query` (即 React Query v5)。

- **Hooks**: 使用 `useQuery` 来获取数据，`useMutation` 来创建/更新/删除数据。
- **Devtools**: React Query Devtools 可能已在开发模式下启用，以方便调试。
- **配置**: 全局配置（如 `queryClient`）通常在应用的根组件（如 `src/App.tsx` 或 `src/main.tsx`）中设置。

## 客户端状态

对于纯粹的客户端状态，项目没有引入大型的全局状态管理库（如 Redux 或 Zustand）。

- **组件本地状态**: 使用 React 内置的 `useState` 和 `useReducer` hooks。
- **跨组件状态**: 使用 `useContext` hook 来共享可以被组件树深处的组件访问的状态。一个例子是 [`src/hooks/useLanguage.tsx`](mdc:src/hooks/useLanguage.tsx) 中管理国际化的 `LanguageProvider`。
