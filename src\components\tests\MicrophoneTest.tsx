import React, { useEffect, useRef } from "react";
import { <PERSON><PERSON>, MicOff, Volume2 } from "lucide-react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useMicrophone } from "@/hooks/useMicrophone";
import { useLanguage } from "@/hooks/useLanguage";

interface MicrophoneTestProps {
  onNext: () => void;
  onBack: () => void;
}

export const MicrophoneTest: React.FC<MicrophoneTestProps> = ({ onNext, onBack }) => {
  const { t } = useLanguage();
  const {
    isRecording,
    audioLevel,
    devices,
    selectedDevice,
    error,
    startRecording,
    stopRecording,
    setSelectedDevice,
    hasPermission,
  } = useMicrophone();

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Draw audio waveform
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !isRecording) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw background
      ctx.fillStyle = "rgba(255, 255, 255, 0.1)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw level indicator
      const barWidth = canvas.width * audioLevel;
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "#10B981");
      gradient.addColorStop(0.7, "#F59E0B");
      gradient.addColorStop(1, "#EF4444");

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, barWidth, canvas.height);

      requestAnimationFrame(draw);
    };

    draw();
  }, [isRecording, audioLevel]);

  return (
    <GlassCard className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          {isRecording ? (
            <Mic className="h-12 w-12 text-green-400" />
          ) : (
            <MicOff className="h-12 w-12 text-white/60" />
          )}
        </div>
        <h2 className="text-2xl font-semibold text-white mb-2">{t("micTestTitle2")}</h2>
        <p className="text-white/70">
          {t("micTestDesc2")}
        </p>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {devices.length > 0 && (
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              {t("selectMicrophone")}:
            </label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50"
            >
              {devices.map((device) => (
                <option key={device.deviceId} value={device.deviceId} className="bg-gray-800">
                  {device.label}
                </option>
              ))}
            </select>
          </div>
        )}

        <div>
          <label className="block text-white/80 text-sm font-medium mb-2">
            {t("audioLevel")}:
          </label>
          <canvas
            ref={canvasRef}
            width={400}
            height={60}
            className="w-full rounded-xl border border-white/20"
          />
          <p className="text-white/60 text-sm mt-2">
            {isRecording ? t("micInstructions") : t("micInstructionsStart")}
          </p>
        </div>

        <div className="flex justify-center">
          {!isRecording ? (
            <PrimaryButton onClick={startRecording} size="lg">
              <Volume2 className="h-5 w-5" />
              {t("startMicTest")}
            </PrimaryButton>
          ) : (
            <PrimaryButton onClick={stopRecording} variant="secondary" size="lg">
              <MicOff className="h-5 w-5" />
              {t("stopTest")}
            </PrimaryButton>
          )}
        </div>
      </div>

      <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
        <PrimaryButton onClick={onBack} variant="outline">
          {t("back")}
        </PrimaryButton>
        <PrimaryButton onClick={onNext}>
          {t("nextSpeakerTest")}
        </PrimaryButton>
      </div>
    </GlassCard>
  );
};