import { TranslationKeys } from './types';

export const zh: TranslationKeys = {
  // 基础导航
  home: "首页",
  tools: "工具",
  meetingTest: "会议检测",
  keyboardTest: "键盘测试",
  mouseTest: "鼠标测试",
  headphonesTest: "耳机测试",
  siteName: "Setup Check",
  siteSubtitle: "硬件检查",
  selectScenario: "选择测试场景",
  onlineMeeting: "在线会议检查",
  onlineMeetingDesc: "测试您的麦克风、扬声器和摄像头以进行视频通话",
  startTest: "开始测试",
  microphoneTest: "麦克风测试",
  speakerTest: "扬声器测试",
  cameraTest: "摄像头测试",
  next: "下一步",
  back: "上一步",
  finish: "完成",
  
  // ToolsPage 相关
  deviceTestingTools: "设备测试工具",
  deviceTestingToolsDescription: "全面的硬件测试工具集合，确保您的设备在任何场景下都能完美工作。",
  testingTools: "测试工具",
  hardwareTesting: "硬件测试",
  deviceTools: "设备工具",
  audioQuality: "音频质量",
  noiseLevel: "噪音水平",
  sensitivity: "灵敏度",
  stereoBalance: "立体声平衡",
  volumeLevel: "音量水平",
  audioOutput: "音频输出",
  keyResponse: "按键响应",
  keyMapping: "按键映射",
  typingSpeed: "打字速度",
  clickAccuracy: "点击精度",
  scrollFunction: "滚动功能",
  internetSpeed: "网络速度",
  connectionStability: "连接稳定性",
  quickTest: "快速测试",
  needHelp: "需要帮助？",
  testingToolsDescription: "我们的测试工具设计简单且准确。每个工具都提供详细的反馈，帮助您优化硬件设置。",
  noRegistration: "无需注册",
  accuracy: "准确度",
  free: "免费",
  
  // 麦克风测试
  micTestTitle: "测试您的麦克风",
  micTestDesc: "对着麦克风说话，您应该看到音频电平响应。",
  selectMicrophone: "选择麦克风",
  microphoneWorking: "麦克风工作正常！",
  microphoneNotDetected: "未检测到麦克风",
  
  // 扬声器测试
  speakerTestTitle: "测试您的扬声器",
  speakerTestDesc: "点击下面的按钮播放测试声音。",
  playTestSound: "播放测试声音",
  canYouHear: "您能听到测试声音吗？",
  
  // 摄像头测试
  cameraTestTitle: "测试您的摄像头",
  cameraTestDesc: "您的摄像头画面应该出现在下方。",
  selectCamera: "选择摄像头",
  cameraWorking: "摄像头工作正常！",
  cameraNotDetected: "未检测到摄像头",
  cameraTestModule: "摄像头测试模块",
  smartScorecard: "智能评分卡",
  startTestToSeeScore: "开始测试以查看评分",
  resolution: "分辨率",
  frameRate: "帧率",
  colorBrightness: "色彩与亮度",
  overallRating: "综合评价",
  takePhoto: "拍照",
  mirror: "镜像",
  fullscreen: "全屏",
  downloadPhoto: "下载照片",
  deletePhoto: "删除照片",
  photoPreview: "拍照预览",
  cameraCapture: "摄像头拍摄",
  deviceName: "设备",
  realTimeInfo: "实时信息",
  
  // 键盘测试
  keyboardTestTitle: "键盘测试",
  keyboardTestDesc: "按键盘上的任意键进行测试",
  pressAnyKey: "按任意键开始测试...",
  keyPressed: "按下的键：",
  
  // 鼠标测试
  mouseTestTitle: "鼠标测试",
  mouseTestDesc: "测试您的鼠标按键和滚轮",
  leftClick: "左键",
  rightClick: "右键",
  middleClick: "中键/滚轮",
  scrollUp: "向上滚动",
  scrollDown: "向下滚动",
  clickButtons: "点击上面的按钮或使用您的鼠标",
  
  // 测试结果
  testComplete: "测试完成！",
  allTestsPassed: "所有测试均成功完成",
  copyReport: "复制报告到剪贴板",

  
  // 页面内容
  gamingSetup: "游戏设置检查",
  gamingSetupDesc: "测试您的外设以获得最佳游戏体验",
  audioTest: "音频测试",
  recommended: "推荐",
  comingSoon: "即将推出",
  individualDeviceTests: "单独设备测试",
  stepOf: "第 {current} 步，共 {total} 步",
  invalidScenario: "无效场景",
  runTestsAgain: "重新运行测试",
  
  // 测试组件
  cameraTestTitle2: "摄像头测试",
  cameraTestDesc2: "测试您的摄像头，确保他人在会议中能清楚看到您。",
  selectCamera2: "选择摄像头",
  cameraPreview: "摄像头预览",
  cameraPlaceholder: "摄像头预览将出现在这里",
  stopCamera: "停止摄像头",
  cameraWorking2: "✅ 摄像头工作正常！",
  cameraWorkingNormally: "摄像头正常工作",
  cameraStartedSuccessfully: "摄像头已成功启动",
  analyzingVideoQuality: "正在分析视频质量",
  cameraWorkingDesc: "您的摄像头工作正常。请确保您面前光线充足且在画面中居中。",
  cameraTips: "💡 摄像头小贴士：",
  cameraTip1: "• 确保您面前有充足的光线",
  cameraTip2: "• 将摄像头放置在眼部水平",
  cameraTip3: "• 清洁摄像头镜头以获得更清晰的图像",
  cameraTip4: "• 检查网络连接是否稳定",
  backSpeakerTest: "返回：扬声器测试",
  finishTesting: "完成测试",
  
  keyboardTestTitle2: "键盘测试",
  keyboardTestDesc2: "按任意键进行测试。按下时键盘会亮起。",
  resetTest: "重置测试",
  backToHome: "返回首页",
  recentKeyPresses: "最近按键",
  space: "空格",
  testingTips: "💡 测试小贴士：",
  keyboardTip1: "• 按不同类型的键",
  keyboardTip2: "• 测试特殊键如 Shift、Ctrl、Alt",
  keyboardTip3: "• 检查所有按键是否正常响应",
  keyboardTip4: "• 尝试输入一些文字以确保操作流畅",
  
  micTestTitle2: "麦克风测试",
  micTestDesc2: "测试您的麦克风，确保他人在会议中能清楚听到您的声音。",
  audioLevel: "音频电平",
  micInstructions: "对着麦克风说话以查看音频电平",
  micInstructionsStart: "点击\"开始测试\"开始",
  startMicTest: "开始麦克风测试",
  stopTest: "停止测试",
  
  // 鼠标测试
  mouseTestTitle2: "鼠标测试",
  mouseTestDesc2: "在下面的测试区域移动鼠标并点击按钮。",
  testArea: "测试区域",
  moveClickScroll: "移动、点击和滚动",
  tryMouseButtons: "尝试左键、右键和中键",
  position: "位置：",
  buttonStatus: "按键状态",
  leftButton: "左键",
  wheelButton: "滚轮",
  middleButton: "中键", 
  rightButton: "右键",
  sideButton1: "侧键1",
  sideButton2: "侧键2",
  pressed: "按下",
  released: "释放",
  eventHistory: "事件历史",
  noMouseEvents: "尚无鼠标事件",
  startMovingClicking: "开始移动和点击以查看事件",
  scrollDown2: "向下滚动",
  scrollUp2: "向上滚动",
  mouseTip1: "• 测试所有鼠标按钮（左键、右键、中键）",
  mouseTip2: "• 检查滚轮双向滚动",
  mouseTip3: "• 验证鼠标移动跟踪",
  mouseTip4: "• 测试点击拖拽功能",
  mouseTip5: "• 确保光标移动流畅",
  
  // 扬声器测试
  speakerTestTitle2: "扬声器测试",
  speakerTestDesc2: "测试您的扬声器或耳机，确保您能在会议中听到音频。",
  volume: "音量：",
  testAudioPlayback: "测试音频播放",
  testAudioDesc: "点击下面的按钮播放测试音调。您应该能从扬声器或耳机中听到清晰的声音。",
  playingTestSound: "播放测试声音中...",
  playTestSound2: "播放测试声音",
  playingTone: "🔊 播放测试音调 (440 Hz)",
  troubleshootingTips: "💡 故障排除小贴士：",
  speakerTip1: "• 检查扬声器/耳机是否已连接",
  speakerTip2: "• 如需要请调整系统音量",
  speakerTip3: "• 尝试不同的音频输出设备",
  speakerTip4: "• 确保音频驱动程序是最新的",
  backMicrophone: "返回：麦克风",
  
  // 耳机与扬声器测试
  headphonesTestTitle: "在线耳机与扬声器测试",
  headphonesTestDesc: "全面的音频测试，获得最佳听音体验",
  outputDeviceSelector: "选择输出设备",
  noOutputDevices: "未找到音频输出设备",
  leftRightChannelTest: "左右声道测试",
  leftRightChannelDesc: "快速诊断最常见的声道反接或单边无声问题",
  playLeft: "▶ 播放左声道",
  playRight: "▶ 播放右声道",
  playingLeft: "播放左声道中...",
  playingRight: "播放右声道中...",
  frequencyResponseDesc: "了解您耳机的低音和高音表现力",
  startSweep: "开始扫描",
  sweepInProgress: "扫描进行中",
  frequencyTestTip: "请留意在哪个频率范围声音开始变弱或消失",
  dynamicRangeTest: "动态范围测试",
  dynamicRangeDesc: "考验耳机对细节的还原能力",
  dynamicRangeTestTip: "您能同时听清安静的背景细节和响亮的主体声音吗？",
  stereoImagingTest: "立体声场与定位测试",
  stereoImagingDesc: "体验身临其境的3D音效",
  play3dAudio: "播放3D音效",
  audioPlaying3d: "3D音效播放中",
  stereoImagingTestTip: "聆听声音在您的头部周围3D空间中移动",
  stopAllAudio: "停止所有音频",
  testInProgress: "测试进行中",
  testNotStarted: "测试未开始",
  testFailed: "测试失败",
  testSkipped: "测试已跳过",
  step: "步骤",
  of: "共",
  skip: "跳过此步骤",
  confirmSkipTest: "确认跳过测试",
  skipTestWarning: "跳过此测试将无法获得该设备的完整状态评估。您确定要跳过吗？",
  confirmSkip: "确认跳过",
  cancel: "取消",
  canSkipThisTest: "可以跳过此测试",
  stopPlaying: "停止播放",
  audioError: "音频错误",
  stopSweep: "停止扫描",
  stopTestButton: "停止测试",
  startTestButton: "开始测试",
  testInstructions: "🎵 测试说明",
  testInstructionItem1: "• 扫描过程中请留意哪些频率范围声音变弱或消失",
  testInstructionItem2: "• 优质耳机应在整个频率范围内保持均衡表现",
  testInstructionItem3: "• 低音过强可能掩盖中音，高音过亮可能刺耳",
  testInstructionItem4: "• 建议在安静环境下进行测试以获得准确结果",
  testInstructionItem5: "• 实时频率显示和图谱帮助您直观了解当前测试频率",
  dynamicRangeTestInProgress: "动态范围测试中",
  quietSound: "安静",
  loudSound: "响亮",
  bassDetail: "低音细节",
  backgroundEffects: "背景音效",
  midrangeLayer: "中音层次",
  voiceDialogue: "人声对话",
  trebleImpact: "高音冲击",
  explosionEffects: "爆炸音效",
  frontDirection: "前",
  backDirection: "后",
  leftDirection: "左",
  rightDirection: "右",
  leftChannel: "左声道",
  rightChannel: "右声道",
  center: "中央",
  ultraLowBass: "超低音",
  lowBass: "低音",
  midLowBass: "中低音",
  midrange: "中音",
  midTreble: "中高音",
  treble: "高音",
  ultraTreble: "超高音",
  bassDrumBass: "低音鼓、贝斯",
  voiceBase: "人声基频",
  voiceClarity: "人声清晰度",
  detailAiriness: "细节、空气感",
  testProgress: "测试进度",
  leftChannelTest: "左声道测试",
  rightChannelTest: "右声道测试",
  frequencyTest: "频率测试",
  dynamicRangeTest: "动态范围测试",
  stereoTest: "立体声测试",
  basicTestRequired: "请先完成左右声道基本测试",
  basicTestCompleted: "基本测试已完成",
  
  // 总结和报告
  testResultsSummary: "✅ 测试结果摘要",
  microphoneTestResult: "麦克风测试：",
  speakerTestResult: "扬声器测试：",
  cameraTestResult: "摄像头测试：",
  testPassed: "通过",
  deviceTestReport: "设备测试报告",
  generated: "生成时间：",
  scenario: "场景：",
  browserInfo: "浏览器信息：",
  testsCompleted: "已完成测试：",
  allHardwareTestsComplete: "所有硬件测试均成功完成。",
  deviceReadyForMeetings: "您的设备已准备好进行在线会议！",
  copyFailed: "复制失败",
  copyFailedDesc: "无法复制到剪贴板。请重试。",
  
  // UI 组件
  close: "关闭",
  previousButton: "上一页",
  nextButton: "下一页",
  more: "更多",
  previousSlide: "上一张幻灯片",
  nextSlide: "下一张幻灯片",
  morePages: "更多页面",
  toggleSidebar: "切换侧边栏",
  
  // 404 页面
  pageNotFound: "404",
  oopsPageNotFound: "哎呀！页面未找到",
  returnToHome: "返回首页",
  
  // 麦克风模块特定
  microphoneAccess: "麦克风访问",
  initializingMicrophone: "正在初始化麦克风...",
  micAccessDenied: "麦克风访问被拒绝",
  micAccessDeniedDesc: "需要麦克风权限才能进行测试。请在浏览器设置中允许访问麦克风。",
  howToEnableMic: "💡 如何开启麦克风权限：",
  micPermissionStep1: "• 点击地址栏左侧的锁定图标",
  micPermissionStep2: "• 选择\"允许\"麦克风权限",
  micPermissionStep3: "• 刷新页面重新尝试",
  micPermissionStep4: "• 或在浏览器设置中手动开启麦克风权限",
  retry: "重新尝试",
  readyToTest: "准备测试您的麦克风",
  readyToTestDesc: "我们将测试您的麦克风音质、音量级别和实时响应性能，确保在线会议时您的声音清晰传达。",
  testContent: "🎯 测试内容：",
  testContentItem1: "• 麦克风设备检测和权限获取",
  testContentItem2: "• 实时音频捕获和音量监测",
  testContentItem3: "• 音频质量和清晰度评估",
  testContentItem4: "• 环境噪音检测和建议",
  testContentItem5: "• 设备兼容性验证",
  startMicrophoneTest: "开始麦克风测试",
  
  // 麦克风测试界面
  audioVisualization: "音频可视化",
  realTimeWaveform: "🎵 实时音频波形 - 说话时会看到波动",
  startSpeaking: "请开始说话...",
  usageTip: "💡 使用提示：对着麦克风说话，上方的波形会根据您的声音实时变化。波形越活跃说明麦克风工作越正常。",
  microphoneStatus: "麦克风状态",
  micConnected: "🎤 麦克风已连接",
  speakIntoMic: "请对着麦克风说话，测试音频输入效果",
  deviceWorking: "设备工作正常",
  volumeDetection: "音量检测",
  lowLevel: "偏低",
  goodLevel: "良好",
  overload: "过载！",
  dbLow: "-60dB 偏低",
  dbOverload: "0dB 过载",
  goodRange: "-25dB ~ -8dB 良好范围",
  
  // 实时监听
  realTimeMonitoring: "实时监听 (Hear Yourself)",
  monitoring: "监听中",
  closed: "已关闭",
  monitoringDesc: "开启后可实时听到自己的声音，帮助调整音量和音质 (建议佩戴耳机)",
  warningHeadphones: "⚠️ 建议佩戴耳机使用此功能",
  preventFeedback: "为防止啸叫声，请确保使用耳机或调低扬声器音量",
  audioDelay: "音频延迟",
  avoidOverlap: "避免声音重合",
  monitoringVolume: "监听音量",
  notAffectRecording: "不影响录音",
  speaker: "扬声器",
  monitoringEnabled: "实时监听已启用",
  adjustSettings: "可实时调整上方设置",
  
  // 录音测试
  recordingTest: "录音测试",
  startRecording: "开始录音",
  stopRecording: "停止录音",
  stopPlayback: "停止播放",
  playRecording: "播放录音",
  recording: "录音中",
  playing: "播放中",
  playbackProgress: "播放进度",
  playbackComplete: "播放完成",
  paused: "已暂停",
  recordingInstructions: "💡 使用说明：点击\"开始录音\"，然后说一段话，停止后点击\"播放录音\"听听效果，这样可以确认麦克风工作正常",
  advancedTest: "🔧 高级测试：可以在录音前调整上方的音频处理设置，然后录音对比不同设置下的音质差异",
  
  // 设备信息
  deviceInfo: "设备信息",
  sampleRate: "采样率:",
  channels: "声道:",
  mono: "单声道",
  stereo: "立体声",
  bitDepth: "位深度:",
  
  // 高级设置
  advancedSettings: "高级设置",
  whenToAdjust: "🎯 什么时候需要调整这些设置？",
  echoCancellation: "回声消除",
  echoCancellationDesc: "开会时对方听到自己说话的回音 → 尝试开启此功能",
  noiseSuppression: "噪音抑制",
  noiseSuppressionDesc: "背景噪音太大影响通话质量 → 开启可减少键盘、风扇等噪音",
  autoGainControl: "自动增益控制",
  autoGainControlDesc: "声音忽大忽小不稳定 → 开启可自动调节音量大小",
  realTimeEffect: "⚡ 实时生效：切换开关后设置会立即应用，您可以录音对比前后效果差异",
  enabled: "已开启",
  disabled: "已关闭",
  preventEcho: "防止对方听到自己说话的回音，适用于使用扬声器的场景",
  mayEcho: "⚠️ 关闭后可能产生回音，建议佩戴耳机",
  filterNoise: "智能过滤键盘声、风扇声、空调声等背景噪音",
  improveQuality: "✅ 开启后可显著提升通话质量",
  autoAdjustVolume: "自动调节音量强度，防止声音忽大忽小影响听感",
  naturalVariation: "💡 关闭后可听到音量的自然变化",
  
  // 测试建议
  testSuggestions: "💡 测试建议",
  defaultFirst: "先用默认设置录一段音，作为基准参考",
  compareSettings: "逐一切换不同设置，每次录音对比效果差异",
  quietEnvironment: "在安静环境下关闭\"噪音抑制\"，在嘈杂环境下开启",
  speakerEcho: "使用扬声器时开启\"回声消除\"，使用耳机时可关闭",
  
  stopTestButton: "停止测试",
  backToHomeButton: "返回首页",
  nextSpeakerTestButton: "下一步：扬声器测试",
  
  // 双击检测功能
  startDoubleClickDetection: "开始双击检测",
  stopMonitoring: "停止监测",
  doubleClickDetectionInProgress: "双击检测进行中",
  duration: "持续时间：",
  detectedSevereHardwareFailure: "检测到严重硬件故障",
  mouseMayHaveIssues: "鼠标可能存在问题",
  sporadicIssuesContinueMonitoring: "偶发性问题，继续监控",
  mouseWorkingNormally: "鼠标工作正常",
  detected: "检测到",
  issues: "问题",
  performSingleClickTest: "执行单击测试",
  tryNormalSingleClickOperations: "尝试进行正常的单击操作",
  systemWillAutoDetectDoubleClickIssues: "系统会自动检测意外的双击问题",
  doubleClickIssueDetection: "双击问题检测",
  noDoubleClickIssuesDetected: "暂无检测到双击问题",
  continueWithSingleClickTesting: "继续进行单击测试",
  severeHardwareFailureShortInterval: "严重硬件故障 - 极短间隔双击",
  possibleHardwareFailureUnexpectedDoubleClick: "可能硬件故障 - 意外双击",
  potentialIssueFastDoubleClick: "潜在问题 - 快速双击",
  interval: "间隔:",
  normal: "正常:",
  doubleClickDetectionInstructions: "双击检测说明",
  doubleClickTip1: "进行正常的单击操作，系统会自动检测意外的双击",
  doubleClickTip2: "红色警告：间隔<20ms，严重硬件故障",
  doubleClickTip3: "黄色警告：间隔<50ms，可能硬件故障",
  doubleClickTip4: "蓝色提示：间隔较短但在正常范围内",
  doubleClickTip5: "以上计时器显示的毫秒数可以帮助诊断问题严重程度",
  doubleClickTip6: "完成检测后，您可以进行正常的单击测试来验证鼠标功能",
  
  // 网络测试
  networkTestDesc: "测试您的网络连接质量以确保在线会议期间的稳定性",
  networkQualityTestDesc: "检测您的网络连接质量，确保会议期间的稳定性",
  testProgress: "测试进度",
  testingNetworkLatency: "正在测试网络延迟、下载和上传速度...",
  networkQualityAssessment: "网络质量评估",
  excellent: "优秀",
  good: "良好",
  fair: "一般",
  poor: "较差",
  latency: "延迟",
  download: "下载",
  upload: "上传",
  jitter: "抖动",
  recommendations: "建议",
  networkExcellentDesc: "网络质量优秀！您可以享受高清视频通话和流畅的屏幕共享。",
  networkGoodDesc: "网络质量良好，适合进行视频会议。建议关闭其他占用带宽的应用。",
  networkFairDesc: "网络质量一般，建议降低视频质量或关闭视频以确保音频稳定。",
  networkPoorDesc: "网络质量较差，建议检查网络连接或联系网络管理员。",
  serverIP: "服务器 IP",
  timezone: "时区",
  userLocation: "您的位置",
  serverLocation: "测试服务器位置",
  distance: "距离",
  approximateDistance: "大约距离",
  gpsLocation: "GPS定位",
  ipLocation: "IP定位",
  timezoneLocation: "时区推断",
  unknownLocation: "未知来源",
  loadingLocation: "正在获取位置信息...",
  server: "服务器",
  
  // Enhanced network test features
  testingLatency: "测试延迟",
  testingDownload: "测试下载速度",
  testingUpload: "测试上传速度",
  testingPacketLoss: "测试丢包率",
  testCompleted: "测试完成",
  preparingTest: "准备测试",
  aimScores: "AIM 使用场景评分",
  gaming: "游戏",
  streaming: "流媒体",
  realTimeCommunication: "实时通信",
  loadedMetrics: "负载下的网络指标",
  loadedLatency: "负载延迟",
  loadedJitter: "负载抖动",
  networkOptimizationTips: "网络优化建议",
  networkTip1: "• 使用有线连接而非WiFi可获得更稳定的网络",
  networkTip2: "• 关闭其他占用带宽的应用程序",
  networkTip3: "• 确保路由器距离较近且信号良好",
  networkTip4: "• 避免在网络高峰期进行重要会议",
  testRegion: "测试地区",
  nextMicrophoneTest: "下一步：麦克风测试",
  nextSpeakerTest: "下一步：扬声器测试",
  
  // 扬声器测试增强版
  enhancedSpeakerTest: "增强扬声器测试",
  comprehensiveAudioTest: "全面测试您的音频输出设备",
  volumeControl: "音量控制",
  stereoTest: "立体声测试",
  leftChannel: "左声道",
  rightChannel: "右声道",
  bothChannels: "双声道",
  testLeftRightChannels: "测试左右声道是否正常工作",
  confirmStereoTestPassed: "确认立体声测试通过",
  frequencyResponseTest: "频率响应测试",
  frequencySweep: "频率扫描",
  sweeping: "扫频中...",
  testTone1kHz: "1kHz 测试音",
  testSpeakerFrequencyRange: "测试扬声器的频率响应范围",
  confirmFrequencyTestPassed: "确认频率测试通过",
  speakerTestComplete: "✅ 扬声器测试完成",
  allAudioTestsPassed: "所有音频测试均已通过，您的扬声器工作正常！",
  audioOptimizationTips: "音频优化建议",
  audioTip1: "• 使用耳机可以获得更好的音频体验",
  audioTip2: "• 调整系统音量到合适的水平",
  audioTip3: "• 确保音频驱动程序是最新版本",
  audioTip4: "• 在安静环境中进行会议以获得最佳效果",
  nextCameraTest: "下一步：摄像头测试",
  
  // 摄像头测试增强版
  enhancedCameraTest: "增强摄像头测试",
  comprehensiveVideoTest: "全面测试您的视频输入设备",
  cameraPermissionDeniedDesc: "需要摄像头权限才能进行测试。请在浏览器设置中允许访问摄像头。",
  howToEnableCamera: "💡 如何开启摄像头权限：",
  cameraPermissionStep1: "• 点击地址栏左侧的锁定图标",
  cameraPermissionStep2: "• 选择\"允许\"摄像头权限",
  cameraPermissionStep3: "• 刷新页面重新尝试",
  cameraPermissionStep4: "• 或在浏览器设置中手动开启摄像头权限",
  readyToTestCamera: "准备测试您的摄像头",
  readyToTestCameraDesc: "我们将测试您的摄像头画质、分辨率和实时性能，确保在线会议时您的图像清晰传达。",
  cameraTestContent: "🎯 测试内容：",
  cameraTestContentItem1: "• 摄像头设备检测和权限获取",
  cameraTestContentItem2: "• 实时视频捕获和画质监测",
  cameraTestContentItem3: "• 视频质量和清晰度评估",
  cameraTestContentItem4: "• 光线条件检测和建议",
  cameraTestContentItem5: "• 设备兼容性验证",
  startCameraTest: "开始摄像头测试",
  cameraStatus: "摄像头状态",
  cameraConnected: "📹 摄像头已连接",
  lookIntoCameraTest: "请看向摄像头，测试视频输入效果",
  videoQualityAnalysis: "视频质量分析",
  brightness: "亮度",
  contrast: "对比度",
  sharpness: "清晰度",
  videoQualityExcellentDesc: "视频质量优秀！您的摄像头设置非常适合会议使用。",
  videoQualityGoodDesc: "视频质量良好，适合大多数会议场景。",
  videoQualityFairDesc: "视频质量一般，建议调整光线或摄像头位置。",
  videoQualityPoorDesc: "视频质量较差，建议检查摄像头设置或更换设备。",
  videoOptimizationTips: "视频优化建议",
  videoTip1: "• 确保面前有充足的光线",
  videoTip2: "• 将摄像头放置在眼部水平",
  videoTip3: "• 清洁摄像头镜头以获得更清晰的图像",
  videoTip4: "• 保持稳定的网络连接",
  completeTest: "完成测试",
  
  // 麦克风测试增强版
  enhancedMicrophoneTest: "增强麦克风测试",
  comprehensiveMicTest: "全面测试您的音频输入设备",
  audioQualityAnalysis: "音频质量分析",
  signalToNoiseRatio: "信噪比:",
  backgroundNoiseLevel: "背景噪音:",
  distortionLevel: "失真度:",
  echoDetection: "回声检测:",
  notDetected: "未检测到",
  micExcellentDesc: "音频质量优秀！您的麦克风设置非常适合会议使用。",
  micGoodDesc: "音频质量良好，适合大多数会议场景。",
  micFairDesc: "音频质量一般，建议调整麦克风位置或降低背景噪音。",
  micPoorDesc: "音频质量较差，建议检查麦克风设置或更换设备。",
  echoDetectedWarning: "⚠️ 检测到回声，建议使用耳机或调整扬声器音量。",
  micOptimizationTips: "麦克风优化建议",
  micTip1: "• 保持麦克风距离嘴部15-20厘米",
  micTip2: "• 在安静环境中进行测试",
  micTip3: "• 使用耳机避免回声问题",
  micTip4: "• 调整麦克风音量到合适水平",
  
  // 测试失败原因
  notTested: "未测试",
  failed: "失败",
  reason: "原因",
  networkNotTested: "未进行网络测试",
  latencyTooHigh: "延迟过高",
  downloadSpeedTooSlow: "下载速度过慢",
  uploadSpeedTooSlow: "上传速度过慢",
  jitterTooHigh: "网络抖动过大",
  networkQualityPoor: "网络质量较差，不适合视频会议",
  micPermissionDenied: "麦克风权限被拒绝，请在浏览器设置中允许麦克风访问",
  micPermissionNotGranted: "未获得麦克风权限",
  noMicrophoneDevices: "未检测到可用的麦克风设备",
  noAudioInput: "麦克风无音频输入，请检查设备连接或音量设置",
  audioQualityIssues: "音频质量问题",
  signalToNoiseRatioLow: "信噪比过低",
  backgroundNoiseTooHigh: "背景噪音过大",
  audioDistortionHigh: "音频失真严重",
  echoDetected: "检测到回声",
  audioQualityPoor: "音频质量较差",
  requiredTestsNotCompleted: "未完成必要的测试项目",
  stereoTestNotCompleted: "立体声测试",
  frequencyTestNotCompleted: "频率响应测试",
  cameraPermissionDenied: "摄像头权限被拒绝，请在浏览器设置中允许摄像头访问",
  noCameraDevices: "未检测到可用的摄像头设备",
  cameraNotStarted: "摄像头未启动，请点击'开始摄像头测试'按钮",
  cameraError: "摄像头错误",
  videoQualityIssues: "视频质量问题",
  lightingTooDark: "光线过暗",
  lightingTooBright: "光线过亮",
  contrastTooLow: "对比度过低",
  imageBlurry: "图像模糊",
  videoQualityPoor: "视频质量较差",
  
  // 总结报告
  meetingDeviceTestComplete: "会议设备检测完成",
  testsCompletedCount: "项测试完成",
  testsPassed: "项测试通过",
  allDevicesReady: "🎉 恭喜！您的设备已准备就绪，可以开始高质量的在线会议了。",
  someTestsFailed: "⚠️ 部分设备测试未通过，建议检查相关设备后重新测试。",
  
  // TestWorkflowPage 专用翻译键
  networkQualityTest: "网络质量测试",
  failureReason: "失败原因",
  onlineMeetingDeviceReport: "在线会议设备检测报告",
  generatedTime: "生成时间",
  testScenario: "测试场景",
  onlineMeetingScenario: "在线会议场景",
  unknownScenario: "未知场景",
  testCompletionStatus: "测试完成状态",
  testCompletionCount: "项测试完成",
  testResultsTitle: "测试结果",
  allHardwareTestsPassed: "所有硬件测试均通过！您的设备已准备好进行在线会议。",
  partialTestsCompleted: "部分测试未完成，建议完成所有测试项目。",
  checkFailedTests: "建议检查失败的测试项目。",
  suggestions: "使用建议",
  suggestion1: "1. 请确保网络连接稳定",
  suggestion2: "2. 将摄像头置于眼部水平位置",
  suggestion3: "3. 保持良好的光线条件",
  suggestion4: "4. 使用耳机可获得更好的音频体验",
  retestFailedDevices: "5. 对于失败的设备，建议重新测试或检查硬件连接",
  reportCopied: "报告已复制！",
  testResults: "测试结果",
  meetingDeviceTestReport: "会议设备测试报告",
  completeAllTests: "请完成所有测试以获得完整的设备状态评估。",

  // Gaming Setup Check
  gamingSetupCheckTitle: "游戏设置检查",
  gamingScenario: "游戏场景",
  gamingDeviceTestReport: "游戏设备测试报告", 
  gamingDeviceTestComplete: "游戏设备测试完成",
  allGamingTestsPassed: "所有游戏测试均已通过，您的设置已准备就绪！",
  gamingNetworkTestDesc: "测试您的网络连接质量以确保最佳游戏性能",
  gamingKeyboardTestDesc: "测试您的键盘游戏响应性和功能",
  gamingMouseTestDesc: "测试您的鼠标准确性和游戏响应性",
  gamingAudioTestDesc: "测试您的音频输出以获得沉浸式游戏体验",
  keyboardResponseTime: "键盘响应时间",
  mouseAccuracy: "鼠标精度",
  audioLatency: "音频延迟",
  peripheralPerformance: "外设性能",
  allPeripheralsReady: "所有外设已准备好进行游戏！",
  somePeripheralsFailed: "某些外设需要注意。请检查上方失败的测试。",
  completeAllGamingTests: "完成所有测试以获取您的游戏设置报告。",
  retestFailedPeripherals: "考虑重新测试失败的外设以获得更好的性能。",
  optimizeGamingSetup: "游戏设置优化技巧",
  gamingTip1: "• 使用有线外设以获得最低延迟",
  gamingTip2: "• 将鼠标灵敏度保持在舒适水平",
  gamingTip3: "• 测试所有键盘按键的正确响应",
  gamingTip4: "• 确保音频无延迟或失真",
  gamingTip5: "• 检查网络稳定性以进行在线游戏",

  // 麦克风测试模块新增翻译键
  micPermissionDeniedTitle: "麦克风访问被拒绝",
  micPermissionDeniedMessage: "请允许麦克风访问权限以测试您的音频输入设备。",
  enableMicPermissionInstructions: "💡 如何开启麦克风权限：",
  enableMicStep1: "• 点击地址栏左侧的🔒图标",
  enableMicStep2: "• 选择\"允许\"麦克风权限",
  enableMicStep3: "• 刷新页面重新测试",
  retryTest: "重新尝试",
  waitingConnection: "等待连接",
  startTestingButton: "点击开始测试按钮",
  waitingTesting: "等待测试中",
  deviceReady: "设备待测试",
  applyingNewSettings: "正在应用新设置",
  settingsApplied: "设置已应用",
  applySettingsError: "应用新设置时出错，请重试",
  realTimeAudioDelay: "实时音频延迟",
  seconds: "秒",
  preventSoundOverlap: "避免声音重合",
  adjustAnytime: "可实时调整上方设置",
  playingInProgress: "播放中",
  recordingCompleted: "录音完成",
  playbackStopped: "播放已停止",
  microphoneInformation: "麦克风信息",
  gettingDeviceInfo: "正在获取设备信息...",
  pleaseTurnOnTest: "请先开始测试以获取设备信息",
  whyAdjustSettings: "🎯 什么时候需要调整这些设置？",
  echoWhenUsingSpeaker: "开会时对方听到自己说话的回音 → 尝试开启此功能",
  noisyBackground: "背景噪音太大影响通话质量 → 开启可减少键盘、风扇等噪音",
  unstableVolume: "声音忽大忽小不稳定 → 开启可自动调节音量大小",
  settingsApplyImmediately: "⚡ 实时生效：切换开关后设置会立即应用，您可以录音对比前后效果差异",
  toggleOn: "已开启",
  toggleOff: "已关闭",
  useHeadphonesToPreventEcho: "⚠️ 关闭后可能产生回音，建议佩戴耳机",
  improveCallQuality: "✅ 开启后可显著提升通话质量",
  hearNaturalVolumeChanges: "💡 关闭后可听到音量的自然变化",
  testSuggestionsTitle: "💡 测试建议",
  defaultSettingsFirst: "先用默认设置录一段音，作为基准参考",
  compareEachSetting: "逐一切换不同设置，每次录音对比效果差异",
  noiseSuppressionTip: "在安静环境下关闭\"噪音抑制\"，在嘈杂环境下开启",
  echoCancellationTip: "使用扬声器时开启\"回声消除\"，使用耳机时可关闭",
  returnButton: "返回",

  // SEO相关翻译
  siteDescription: "专业的设备测试平台，测试摄像头、麦克风、扬声器、键盘、鼠标和网络质量。适用于在线会议和游戏设置。",
  siteKeywords: "设备测试,摄像头测试,麦克风测试,扬声器测试,键盘测试,鼠标测试,网络测试,在线会议,硬件检查,游戏设置",
  
  // 页面SEO描述
  homePageDescription: "在线测试您的设备 - 摄像头、麦克风、扬声器、键盘、鼠标和网络。免费的硬件兼容性检查，适用于在线会议和游戏。",
  toolsPageDescription: "专业设备测试工具集合。免费在线测试摄像头、麦克风、扬声器、键盘、鼠标和网络质量。",
  cameraTestDescription: "在线测试摄像头质量、分辨率和性能。检查摄像头在视频通话和直播中的兼容性。",
  microphoneTestDescription: "测试麦克风音频质量、噪音水平和清晰度。确保在线会议和游戏中的完美音频效果。",
  headphonesTestDescription: "测试耳机和扬声器的音频质量、立体声平衡和音量水平。检查音频兼容性。",
  keyboardTestDescription: "测试所有键盘按键功能、响应时间和按键映射。确保最佳打字和游戏性能。",
  mouseTestDescription: "测试鼠标精度、点击响应、滚轮功能和精确度。优化生产力和游戏体验。",
  networkTestDescription: "测试网络速度、延迟和连接稳定性。检查在线会议和游戏的网络质量。",
  meetingTestDescription: "在线会议设备兼容性完整检查。测试摄像头、麦克风、扬声器和网络质量。",
  gamingTestDescription: "游戏设置完整检查。测试键盘、鼠标、耳机、麦克风和网络性能，获得最佳游戏体验。",

  // 直播和内容创作场景
  streamingScenario: "直播与内容创作",
  streamingScenarioDesc: "为主播、内容创作者和在线教育者提供专业级设备测试",
  streamingSetupCheckTitle: "直播与内容创作设备检查",
  streamingDeviceTestReport: "直播设备测试报告",
  streamingDeviceTestComplete: "直播设备测试完成",
  allStreamingTestsPassed: "所有直播设备测试通过！您的设备已准备好进行专业内容创作。",
  streamingOptimizationTips: "直播优化建议",
  streamingTip1: "• 确保稳定的高速网络（1080p直播至少需要5 Mbps上传速度）",
  streamingTip2: "• 使用专用麦克风以获得更好的音频质量",
  streamingTip3: "• 优化照明和摄像头位置以获得专业外观",
  streamingTip4: "• 测试音频水平并消除背景噪音",
  streamingTip5: "• 考虑使用耳机来监控音频质量",
  streamingTestDescription: "完整的直播设置检查。测试摄像头、麦克风、耳机和网络性能，确保高质量内容创作。",

  // 设备诊断场景
  diagnosticScenario: "完整设备诊断",
  diagnosticScenarioDesc: "对所有连接设备进行全面硬件扫描和故障排除",
  diagnosticSetupCheckTitle: "完整设备诊断",
  diagnosticDeviceTestReport: "完整设备诊断报告",
  diagnosticDeviceTestComplete: "设备诊断完成",
  allDiagnosticTestsPassed: "所有硬件组件运行正常。未检测到问题。",
  diagnosticTroubleshootingTips: "故障排除建议",
  diagnosticTip1: "• 检查设备驱动程序并在必要时更新",
  diagnosticTip2: "• 验证所有硬件连接是否牢固",
  diagnosticTip3: "• 使用其他应用程序测试设备以隔离问题",
  diagnosticTip4: "• 如果问题持续存在，请重启设备和浏览器",
  diagnosticTip5: "• 对于持续的硬件故障，请联系技术支持",
  diagnosticTestDescription: "完整的硬件诊断扫描。测试所有设备，包括摄像头、麦克风、耳机、键盘、鼠标和网络，进行全面系统评估。",

  // 网络测试相关
  startNetworkTest: "开始网络测试",
  retestNetwork: "重新测试网络",

  // 错误消息 - 摄像头
  cameraAccessDenied: "摄像头访问被拒绝。请允许摄像头权限。",
  cameraNotFound: "未找到摄像头。请连接摄像头。",
  cameraAccessFailed: "无法访问摄像头",

  // 错误消息 - 麦克风
  microphoneAccessDenied: "麦克风权限被拒绝。请允许麦克风访问权限并重试。",
  microphoneAccessFailed: "无法访问麦克风。请检查您的设备并重试。",
  recordingStartFailed: "无法开始录音",

  // SEO验证消息
  seoTitleMissing: "缺少页面标题",
  seoTitleTooLong: "页面标题过长（建议60字符以内）",
  seoTitleTooShort: "页面标题过短（建议10字符以上）",
  seoDescriptionMissing: "缺少页面描述",
  seoDescriptionTooLong: "页面描述过长（建议160字符以内）",
  seoDescriptionTooShort: "页面描述过短（建议50字符以上）",

  // 默认SEO配置
  defaultSEOTitle: "设备测试平台",
  defaultSEODescription: "专业的设备测试平台，用于摄像头、麦克风、扬声器、键盘、鼠标和网络质量测试。",

  // 控制台消息
  setLanguageWarning: "从LanguageProvider调用setLanguage。请使用useLanguageNavigation hook。",
  invalidLanguageCode: "无效的语言代码",

  // 场景页面标签
  includedTests: "包含测试：",
  completeHardwareCheck: "完整的硬件兼容性检查",
  needIndividualTesting: "需要单独设备测试？",
  individualTestingDesc: "如果您只需要测试特定的硬件组件，可以直接访问单独的测试工具。",
  browseIndividualTools: "浏览单独工具",
  selectScenarioDesc: "选择最适合您需求的测试场景。每个场景都包含为特定用例设计的精选测试集。",

  // 场景时长
  duration5to8: "5-8 分钟",
  duration8to12: "8-12 分钟",
  duration6to10: "6-10 分钟",
  duration10to15: "10-15 分钟",

  // 难度级别
  difficultyBeginner: "初级",
  difficultyAdvanced: "高级",
  difficultyIntermediate: "中级",
  difficultyComprehensive: "全面",

  // 使用场景描述
  usageMeeting: "完美适用于视频通话、远程工作和在线会议",
  usageGaming: "针对游戏性能和竞技游戏进行优化",
  usageStreaming: "针对高质量直播和内容创作进行优化",
  usageDiagnostic: "完整的系统检查，用于故障排除和设备验证",

  // 键盘测试模块
  keyboardTestInstructions: "测试说明",
  keyboardTestInstruction1: "• 点击开始测试后，请按下常用的游戏按键",
  keyboardTestInstruction2: "• 建议测试：WASD、空格、Shift、Ctrl、Q、E 等",
  keyboardTestInstruction3: "• 我们将测量按键响应时间和覆盖率",
  keyboardTestInstruction4: "• 测试至少10次按键后可以完成",
  startKeyboardTest: "开始键盘测试",
  keyboardTestingActive: "正在测试键盘...",
  keyboardTestStopped: "键盘测试已停止",
  keyPressCount: "按键次数",
  averageLatency: "平均延迟",
  keyCoverage: "关键按键覆盖率",
  keyTestStatus: "关键按键测试状态",
  recentKeys: "最近按键 (延迟)",
  stopKeyboardTest: "停止测试",
  continueKeyboardTest: "继续测试",
  resetKeyboardTest: "重置",
  keyboardTestComplete: "键盘测试已完成！",
  keyboardTestCompleteDesc: "您可以继续测试更多按键或进入下一步",
  insufficientKeyCoverage: "按键覆盖率不足 - 请测试更多游戏按键",
  highInputLatency: "检测到高输入延迟",
  notEnoughKeyPresses: "按键次数不足 - 请测试更多按键",

  // 键盘延迟测试 (KeyboardTest.tsx)
  keyboardLatencyTest: "键盘延迟测试",
  testKeyboardResponseTime: "测试您的键盘响应时间和输入延迟",
  latencyShortestLatency: "最短延迟",
  latencyAverageLatency: "平均延迟",
  latencyScanRate: "扫描频率",
  latencyConnection: "连接方式",
  latencyNoData: "无数据",
  latencyPresses: "次按键",
  latencyMaxLatency: "最大",
  latencyHistory: "延迟历史",
  latencyTestInstructions: "延迟测试说明",
  latencyInstruction1: "• 按下并释放按键以测量响应时间",
  latencyInstruction2: "• 延迟越短 = 性能越好",
  latencyInstruction3: "• 扫描频率根据最短按键时间计算",
  latencyInstruction4: "• 游戏键盘通常延迟 <5ms",
  latencyInstruction5: "• 普通键盘平均延迟 10-15ms",
  latencyPerformanceAssessment: "性能评估",
  latencyOverallRating: "总体评级",
  latencyRecommendation: "建议",
  latencyKeyCharacteristics: "关键特性",
  latencyEstimatedPollingRate: "估计轮询率：~1000Hz",
  latencyConnectionType: "连接类型",
  latencyResponseConsistency: "响应一致性",
  latencyVeryConsistent: "非常一致",
  latencyVariable: "变化较大",
  latencyExcellent: "优秀",
  latencyVeryGood: "很好",
  latencyGood: "良好",
  latencyAverage: "一般",
  latencyPoor: "较差",
  latencyExcellentForGaming: "非常适合游戏和专业用途",
  latencyGoodForGeneral: "适合一般使用，可用于休闲游戏",
  latencyConsiderUpgrading: "建议升级以获得更好的性能",
  latencyUsbEstimated: "USB（估计）",
  latencyUnknown: "未知",

  // 隐私政策和 Cookie 政策
  privacyPolicy: "隐私政策",
  cookiePolicy: "Cookie 政策",
  privacyPolicyTitle: "隐私政策",
  cookiePolicyTitle: "Cookie 政策",
  lastUpdated: "最后更新",
  effectiveDate: "生效日期",
  contactUs: "联系我们",
  backToHome: "返回首页",

  // Cookie 同意相关
  cookieConsentTitle: "Cookie 设置",
  cookieConsentDescription: "我们使用 Cookie 来改善您的浏览体验、分析网站流量并个性化内容。您可以选择接受所有 Cookie，或自定义您的偏好设置。",
  cookieSettingsTitle: "Cookie 设置",
  cookieSettingsDescription: "我们使用不同类型的 Cookie 来优化您的体验。您可以选择启用或禁用每个类别，但请注意某些功能可能会受到影响。",
  acceptAll: "接受所有",
  rejectAll: "拒绝所有",
  customize: "自定义",
  saveSettings: "保存设置",
  required: "必需",
  close: "关闭",

  // Cookie 类别
  cookieNecessaryTitle: "必要 Cookie",
  cookieNecessaryDesc: "这些 Cookie 对网站正常运行是必需的，无法禁用。它们通常仅在您进行操作时设置，如设置隐私偏好、登录或填写表单。",
  cookieAnalyticsTitle: "分析 Cookie",
  cookieAnalyticsDesc: "这些 Cookie 帮助我们了解访问者如何与网站互动，收集和报告匿名信息。这有助于我们改善网站性能和用户体验。",
  cookieMarketingTitle: "营销 Cookie",
  cookieMarketingDesc: "这些 Cookie 用于跟踪访问者在网站上的活动，目的是显示相关和个性化的广告。",
  cookiePreferencesTitle: "偏好 Cookie",
  cookiePreferencesDesc: "这些 Cookie 使网站能够记住您的选择（如用户名、语言或地区），并提供增强的个性化功能。",

  // 隐私政策内容
  privacyIntroduction: "Setup Check（我们、我们的或公司）致力于保护您的隐私。本隐私政策解释了我们如何收集、使用和保护您在使用我们的设备测试服务时的信息。",
  dataCollectionTitle: "我们收集的信息",
  dataCollectionContent: "我们收集以下类型的信息：\n\n• **设备信息**：浏览器类型、操作系统、设备型号和屏幕分辨率\n• **使用数据**：您如何使用我们的服务，包括测试结果和交互模式\n• **技术数据**：IP 地址（匿名化）、访问时间和页面浏览量\n• **Cookie 和类似技术**：用于改善用户体验和分析网站性能\n\n我们不会收集个人身份信息，如姓名、电子邮件地址或电话号码，除非您主动提供。",
  dataUsageTitle: "信息使用方式",
  dataUsageContent: "我们使用收集的信息用于：\n\n• **提供服务**：运行设备测试并显示结果\n• **改善服务**：分析使用模式以优化用户体验\n• **技术支持**：诊断和解决技术问题\n• **安全保护**：检测和防止滥用或恶意活动\n• **合规要求**：遵守法律义务和监管要求",
  thirdPartyServicesTitle: "第三方服务",
  thirdPartyServicesContent: "我们使用以下第三方服务：\n\n• **Google Analytics 4**：用于网站分析和性能监控。Google 可能会收集匿名使用数据。您可以通过 Cookie 设置控制此数据收集。\n• **内容分发网络 (CDN)**：用于提高网站加载速度\n\n这些服务有自己的隐私政策，我们建议您查阅它们的政策以了解其数据处理方式。",
  userRightsTitle: "您的权利",
  userRightsContent: "根据适用的数据保护法律，您拥有以下权利：\n\n• **访问权**：请求访问我们持有的关于您的信息\n• **更正权**：请求更正不准确的信息\n• **删除权**：请求删除您的个人信息\n• **限制处理权**：请求限制对您信息的处理\n• **数据可携权**：请求以结构化格式接收您的数据\n• **反对权**：反对处理您的信息\n\n要行使这些权利，请通过下面的联系信息与我们联系。",
  dataSecurityTitle: "数据安全",
  dataSecurityContent: "我们实施适当的技术和组织措施来保护您的信息：\n\n• **加密**：所有数据传输都使用 HTTPS 加密\n• **访问控制**：限制对数据的访问仅限于授权人员\n• **定期审计**：定期审查我们的安全措施\n• **数据最小化**：仅收集必要的信息\n• **匿名化**：在可能的情况下对数据进行匿名化处理",
  contactInformationTitle: "联系信息",
  contactInformationContent: "如果您对本隐私政策有任何问题或需要行使您的权利，请通过以下方式联系我们：\n\n• **电子邮件**：<EMAIL>\n• **地址**：[公司地址]\n\n我们将在收到您的请求后 30 天内回复。",

  // Cookie 政策内容
  cookieIntroduction: "本 Cookie 政策解释了 Setup Check 如何使用 Cookie 和类似技术来识别您访问我们网站时的情况。它解释了这些技术是什么以及我们为什么使用它们，以及您控制我们使用它们的权利。",
  whatAreCookiesTitle: "什么是 Cookie",
  whatAreCookiesContent: "Cookie 是当您访问网站时放置在您的计算机或移动设备上的小数据文件。网站所有者广泛使用 Cookie，以使其网站正常工作或更高效地工作，以及提供报告信息。\\n\\n由网站所有者设置的 Cookie 称为第一方 Cookie。由网站所有者以外的其他方设置的 Cookie 称为第三方 Cookie。第三方 Cookie 可以在网站上或通过网站提供第三方功能或服务。",
  cookieTypesTitle: "我们使用的 Cookie 类型",
  cookieTypesContent: "• **必要 Cookie**：这些 Cookie 对于网站的正常运行是绝对必要的。它们使您能够浏览网站并使用其功能，如访问网站的安全区域。没有这些 Cookie，您请求的服务就无法提供。\n\n• **分析 Cookie**：这些 Cookie 收集有关访问者如何使用网站的信息，例如访问者最常访问哪些页面，以及他们是否从网页收到错误消息。这些 Cookie 不会收集识别访问者身份的信息。这些 Cookie 收集的所有信息都是聚合的，因此是匿名的。\n\n• **功能 Cookie**：这些 Cookie 允许网站记住您所做的选择（如您的用户名、语言或您所在的地区）并提供增强的、更个人化的功能。\n\n• **营销 Cookie**：这些 Cookie 用于跟踪网站访问者。其目的是显示与个人用户相关且有吸引力的广告，从而对发布商和第三方广告商更有价值。",
  manageCookiesTitle: "如何管理 Cookie",
  manageCookiesContent: "您可以通过多种方式管理 Cookie：\n\n• **Cookie 设置**：使用我们网站上的 Cookie 同意横幅来选择您的偏好\n• **浏览器设置**：大多数网络浏览器允许您通过浏览器设置控制 Cookie\n• **退出工具**：您可以使用各种在线工具来退出特定的跟踪\n\n请注意，禁用某些 Cookie 可能会影响网站的功能和您的用户体验。",
  thirdPartyCookiesTitle: "第三方 Cookie",
  thirdPartyCookiesContent: "我们使用 Google Analytics 来分析我们网站的使用情况。Google Analytics 使用 Cookie 来帮助我们分析用户如何使用网站。Cookie 生成的关于您使用网站的信息（包括您的 IP 地址）将被传输到 Google 并由 Google 存储在美国的服务器上。\n\nGoogle 将使用这些信息来评估您对网站的使用情况，为网站运营商编制网站活动报告，并提供与网站活动和互联网使用相关的其他服务。\n\n您可以通过在浏览器上选择适当的设置来拒绝使用 Cookie，但请注意，如果您这样做，您可能无法使用本网站的全部功能。",

  // 联系我们
  contactUs: "联系我们",
  userFeedback: "用户反馈",
  contactEmailTemplate: "请描述您遇到的问题或建议：\n\n[请在此处详细描述您的问题或建议]\n\n如果是技术问题，请说明：\n- 您正在使用的设备类型\n- 遇到问题的具体步骤\n- 错误信息（如有）",
  systemInfo: "系统信息",
  browser: "浏览器",
  language: "语言",
  timestamp: "时间戳",
  contactTitle: "联系我们",
  contactDescription: "遇到问题或有建议？我们很乐意听取您的反馈！请通过邮件联系我们，我们会尽快回复。",
  feedbackTypes: "反馈类型",
  bugReport: "错误报告",
  featureRequest: "功能建议",
  generalInquiry: "一般咨询",
  technicalSupport: "技术支持",
};