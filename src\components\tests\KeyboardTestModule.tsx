import React, { useState, useEffect, useRef } from "react";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { RotateCcw, Activity, Keyboard, CheckCircle2, X } from "lucide-react";
import { useLanguage } from "@/hooks/useLanguage";

interface KeyboardTestModuleProps {
  onNext?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
  onBack?: () => void;
  onTestResult?: (result: { passed: boolean; details?: any; failureReason?: string }) => void;
}

interface KeyPress {
  key: string;
  code: string;
  timestamp: number;
  latency: number;
}

interface TestStats {
  totalPresses: number;
  averageLatency: number;
  testedKeys: Set<string>;
  issues: string[];
}

export const KeyboardTestModule: React.FC<KeyboardTestModuleProps> = ({ onNext, onBack, onTestResult }) => {
  const { t } = useLanguage();
  const [isActive, setIsActive] = useState(false);
  const [pressedKeys, setPressedKeys] = useState<Set<string>>(new Set());
  const [keyHistory, setKeyHistory] = useState<KeyPress[]>([]);
  const [testStats, setTestStats] = useState<TestStats>({
    totalPresses: 0,
    averageLatency: 0,
    testedKeys: new Set(),
    issues: []
  });
  const [testComplete, setTestComplete] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  
  const keyDownTimeRef = useRef<Map<string, number>>(new Map());
  const keyboardTestRef = useRef<HTMLDivElement>(null);

  // 推荐测试的关键按键
  const essentialKeys = [
    'KeyW', 'KeyA', 'KeyS', 'KeyD', // WASD
    'Space', 'ShiftLeft', 'ControlLeft', 'Tab',
    'Escape', 'Enter', 'KeyQ', 'KeyE'
  ];

  const keyDisplayMap: { [key: string]: string } = {
    'KeyW': 'W', 'KeyA': 'A', 'KeyS': 'S', 'KeyD': 'D',
    'Space': 'Space', 'ShiftLeft': 'Shift', 'ControlLeft': 'Ctrl',
    'Tab': 'Tab', 'Escape': 'Esc', 'Enter': 'Enter',
    'KeyQ': 'Q', 'KeyE': 'E'
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isActive) return;
      
      event.preventDefault();
      const now = performance.now();
      
      if (!keyDownTimeRef.current.has(event.code)) {
        keyDownTimeRef.current.set(event.code, now);
        setPressedKeys(prev => new Set([...prev, event.code]));
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (!isActive) return;
      
      event.preventDefault();
      const now = performance.now();
      const downTime = keyDownTimeRef.current.get(event.code);
      
      if (downTime) {
        const latency = now - downTime;
        const newKeyPress: KeyPress = {
          key: event.key,
          code: event.code,
          timestamp: now,
          latency
        };
        
        setKeyHistory(prev => [...prev.slice(-19), newKeyPress]);
        keyDownTimeRef.current.delete(event.code);
        setPressedKeys(prev => {
          const newSet = new Set(prev);
          newSet.delete(event.code);
          return newSet;
        });
        
        // 更新统计
        setTestStats(prev => ({
          ...prev,
          totalPresses: prev.totalPresses + 1,
          testedKeys: new Set([...prev.testedKeys, event.code]),
          averageLatency: (prev.averageLatency * prev.totalPresses + latency) / (prev.totalPresses + 1)
        }));
      }
    };

    if (isActive) {
      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [isActive]);

  // 检查是否建议测试更多按键
  useEffect(() => {
    if (testStats.totalPresses >= 10) {
      const testedEssentialKeys = essentialKeys.filter(key => testStats.testedKeys.has(key));
      const coverage = testedEssentialKeys.length / essentialKeys.length;
      
      if (coverage >= 0.6) { // 测试了60%的关键按键
        setTestComplete(true);
      }
    }
  }, [testStats]);

  // 自动报告测试结果
  useEffect(() => {
    if (onTestResult && testComplete && testStats.totalPresses > 0) {
      const testedEssentialKeys = essentialKeys.filter(key => testStats.testedKeys.has(key));
      const coverage = testedEssentialKeys.length / essentialKeys.length;
      const testPassed = coverage >= 0.6 && testStats.averageLatency < 100; // 60%覆盖率且延迟小于100ms
      
      let failureReason;
      if (!testPassed) {
        const issues = [];
        if (coverage < 0.6) issues.push(t("keyboardCoverageInsufficient"));
        if (testStats.averageLatency >= 100) issues.push(t("keyboardLatencyTooHigh"));
        failureReason = issues.join(", ");
      }

      onTestResult({
        passed: testPassed,
        failureReason: failureReason,
        details: {
          totalPresses: testStats.totalPresses,
          averageLatency: testStats.averageLatency,
          coverage: coverage,
          testedKeys: Array.from(testStats.testedKeys),
          issues: testStats.issues
        }
      });
    }
  }, [onTestResult, testComplete, testStats, essentialKeys, t]);

  const startTest = () => {
    setIsActive(true);
    setHasStarted(true);
    setTestComplete(false);
    keyboardTestRef.current?.focus();
  };

  const stopTest = () => {
    setIsActive(false);
  };

  const resetTest = () => {
    setIsActive(false);
    setPressedKeys(new Set());
    setKeyHistory([]);
    setTestStats({
      totalPresses: 0,
      averageLatency: 0,
      testedKeys: new Set(),
      issues: []
    });
    setTestComplete(false);
    setHasStarted(false);
    keyDownTimeRef.current.clear();
  };

  const getTestResult = () => {
    const issues: string[] = [];
    
    // 检查测试覆盖率
    const testedEssentialKeys = essentialKeys.filter(key => testStats.testedKeys.has(key));
    const coverage = testedEssentialKeys.length / essentialKeys.length;
    
    if (coverage < 0.4) {
      issues.push(t("insufficientKeyCoverage"));
    }

    // 检查平均延迟
    if (testStats.averageLatency > 50) {
      issues.push(t("highInputLatency"));
    }

    // 检查最小测试次数
    if (testStats.totalPresses < 8) {
      issues.push(t("notEnoughKeyPresses"));
    }

    const passed = issues.length === 0;
    
    return {
      passed,
      details: {
        totalPresses: testStats.totalPresses,
        averageLatency: testStats.averageLatency,
        coverage: Math.round(coverage * 100),
        testedKeys: Array.from(testStats.testedKeys)
      },
      failureReason: issues.length > 0 ? issues.join(", ") : undefined
    };
  };

  const handleNext = () => {
    const result = getTestResult();
    onNext(result);
  };

  const testedEssentialKeys = essentialKeys.filter(key => testStats.testedKeys.has(key));
  const coverage = essentialKeys.length > 0 ? (testedEssentialKeys.length / essentialKeys.length) * 100 : 0;

  return (
    <GlassCard className="max-w-4xl mx-auto">
      <div className="text-center mb-6">
        <Keyboard className="h-16 w-16 mx-auto mb-4 text-blue-400" />
        <h2 className="text-3xl font-bold text-white mb-2">{t("keyboardTest")}</h2>
        <p className="text-white/70 text-lg">{t("gamingKeyboardTestDesc")}</p>
      </div>

      {!hasStarted ? (
        <div className="text-center">
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-6 mb-6">
            <h3 className="text-blue-200 font-semibold mb-3">{t("keyboardTestInstructions")}</h3>
            <div className="text-left text-white/80 space-y-2">
              <p>{t("keyboardTestInstruction1")}</p>
              <p>{t("keyboardTestInstruction2")}</p>
              <p>{t("keyboardTestInstruction3")}</p>
              <p>{t("keyboardTestInstruction4")}</p>
            </div>
          </div>
          <PrimaryButton onClick={startTest} size="lg">
            {t("startKeyboardTest")}
          </PrimaryButton>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 测试状态 */}
          <div className="text-center">
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${
              isActive ? "bg-green-500/20 text-green-300" : "bg-gray-500/20 text-gray-300"
            }`}>
              <Activity className={`h-4 w-4 ${isActive ? "animate-pulse" : ""}`} />
              <span>{isActive ? t("keyboardTestingActive") : t("keyboardTestStopped")}</span>
            </div>
          </div>

          {/* 实时统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-white">{testStats.totalPresses}</div>
              <div className="text-white/60 text-sm">{t("keyPressCount")}</div>
            </div>
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-white">
                {testStats.averageLatency > 0 ? `${testStats.averageLatency.toFixed(1)}ms` : "-"}
              </div>
              <div className="text-white/60 text-sm">{t("averageLatency")}</div>
            </div>
            <div className="bg-white/5 rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-white">{Math.round(coverage)}%</div>
              <div className="text-white/60 text-sm">{t("keyCoverage")}</div>
            </div>
          </div>

          {/* 按键状态显示 */}
          <div className="bg-white/5 rounded-xl p-4">
            <h4 className="text-white font-medium mb-3">{t("keyTestStatus")}</h4>
            <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
              {essentialKeys.map(keyCode => {
                const isTested = testStats.testedKeys.has(keyCode);
                const isPressed = pressedKeys.has(keyCode);
                return (
                  <div
                    key={keyCode}
                    className={`p-2 rounded text-center text-sm font-medium transition-all ${
                      isPressed 
                        ? "bg-blue-500 text-white transform scale-95" 
                        : isTested
                          ? "bg-green-500/20 text-green-300 border border-green-400/30"
                          : "bg-white/10 text-white/50 border border-white/20"
                    }`}
                  >
                    {keyDisplayMap[keyCode] || keyCode}
                    {isTested && <CheckCircle2 className="h-3 w-3 inline ml-1" />}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 最近按键历史 */}
          {keyHistory.length > 0 && (
            <div className="bg-white/5 rounded-xl p-4">
              <h4 className="text-white font-medium mb-3">{t("recentKeys")}</h4>
              <div className="flex flex-wrap gap-2">
                {keyHistory.slice(-10).map((keyPress, index) => (
                  <span
                    key={index}
                    className={`px-2 py-1 rounded text-sm ${
                      keyPress.latency < 20 
                        ? "bg-green-500/20 text-green-300"
                        : keyPress.latency < 40
                          ? "bg-yellow-500/20 text-yellow-300"
                          : "bg-red-500/20 text-red-300"
                    }`}
                  >
                    {keyPress.key} ({keyPress.latency.toFixed(1)}ms)
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 控制按钮 */}
          <div className="flex justify-center space-x-4">
            {isActive ? (
              <PrimaryButton onClick={stopTest} variant="secondary">
                {t("stopKeyboardTest")}
              </PrimaryButton>
            ) : (
              <PrimaryButton onClick={startTest}>
                {t("continueKeyboardTest")}
              </PrimaryButton>
            )}
            <PrimaryButton onClick={resetTest} variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              {t("resetKeyboardTest")}
            </PrimaryButton>
          </div>

          {/* 测试完成提示 */}
          {testComplete && (
            <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 text-center">
              <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
              <p className="text-green-200 font-medium">{t("keyboardTestComplete")}</p>
              <p className="text-green-200/80 text-sm">{t("keyboardTestCompleteDesc")}</p>
            </div>
          )}
        </div>
      )}

      {/* 保留兼容性的导航按钮 - 仅在使用旧API时显示 */}
      {!onTestResult && (
        <div className="flex justify-between mt-8">
          <PrimaryButton onClick={onBack} variant="outline">
            {t("back")}
          </PrimaryButton>
          <PrimaryButton 
            onClick={handleNext}
            disabled={!hasStarted || testStats.totalPresses < 5}
          >
            {t("next")}
          </PrimaryButton>
        </div>
      )}
      
      {/* 隐藏的焦点元素 */}
      <div
        ref={keyboardTestRef}
        tabIndex={-1}
        className="sr-only"
        aria-label="Keyboard test area"
      />
    </GlassCard>
  );
}; 