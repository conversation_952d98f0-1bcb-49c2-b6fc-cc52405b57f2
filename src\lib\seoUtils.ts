/**
 * SEO工具函数库
 * 包含面包屑导航、阅读时间计算、关键词密度分析等SEO友好功能
 */

import { Language, LANGUAGE_CONFIGS } from '@/config/languages';

export interface BreadcrumbItem {
  name: string;
  url: string;
  position: number;
}

/**
 * 生成面包屑导航
 */
export const generateBreadcrumbs = (
  pathname: string,
  t: (key: string) => string,
  language: Language = 'en'
): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [
    {
      name: t('home'),
      url: language === 'en' ? '/' : `/${language}/`,
      position: 1
    }
  ];

  const pathSegments = pathname.split('/').filter(segment => segment !== '');
  
  // 移除语言代码
  if (pathSegments[0] && Object.keys(LANGUAGE_CONFIGS).includes(pathSegments[0])) {
    pathSegments.shift();
  }

  pathSegments.forEach((segment, index) => {
    const position = index + 2;
    let name = segment;
    let url = '';

    // 构建URL
    if (language === 'en') {
      url = '/' + pathSegments.slice(0, index + 1).join('/');
    } else {
      url = `/${language}/` + pathSegments.slice(0, index + 1).join('/');
    }

    // 翻译路径段
    switch (segment) {
      case 'tools':
        name = t('tools');
        break;
      case 'camera-test':
      case 'camera':
        name = t('cameraTest');
        break;
      case 'microphone-test':
      case 'microphone':
        name = t('microphoneTest');
        break;
      case 'headphones-test':
      case 'headphones':
        name = t('headphonesTest');
        break;
      case 'keyboard-test':
      case 'keyboard':
        name = t('keyboardTest');
        break;
      case 'mouse-test':
      case 'mouse':
        name = t('mouseTest');
        break;
      case 'network-test':
      case 'network':
        name = t('networkQualityTest');
        break;
      case 'test':
        name = t('testScenario');
        break;
      case 'meeting':
        name = t('onlineMeeting');
        break;
      case 'gaming':
        name = t('gamingSetup');
        break;
      default:
        // 将横线替换为空格并首字母大写
        name = segment.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
    }

    breadcrumbs.push({
      name,
      url,
      position
    });
  });

  return breadcrumbs;
};

/**
 * 生成面包屑结构化数据
 */
export const generateBreadcrumbSchema = (breadcrumbs: BreadcrumbItem[], baseUrl: string) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map(item => ({
      "@type": "ListItem",
      "position": item.position,
      "name": item.name,
      "item": `${baseUrl}${item.url}`
    }))
  };
};

/**
 * 计算文本阅读时间（估算）
 */
export const calculateReadingTime = (text: string, wordsPerMinute: number = 200): number => {
  const words = text.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

/**
 * 生成关键词建议
 */
export const generateKeywordSuggestions = (content: string, language: Language = 'en'): string[] => {
  const commonKeywords: Record<Language, string[]> = {
    en: [
      'device test', 'hardware check', 'online meeting', 'gaming setup',
      'camera test', 'microphone test', 'speaker test', 'keyboard test',
      'mouse test', 'network test', 'compatibility check', 'performance test'
    ],
    zh: [
      '设备测试', '硬件检查', '在线会议', '游戏设置',
      '摄像头测试', '麦克风测试', '扬声器测试', '键盘测试',
      '鼠标测试', '网络测试', '兼容性检查', '性能测试'
    ],
    es: [
      'prueba de dispositivos', 'verificación de hardware', 'reunión en línea', 'configuración de juegos',
      'prueba de cámara', 'prueba de micrófono', 'prueba de altavoces', 'prueba de teclado',
      'prueba de ratón', 'prueba de red', 'verificación de compatibilidad', 'prueba de rendimiento'
    ],
    de: [
      'Gerätetest', 'Hardware-Check', 'Online-Meeting', 'Gaming-Setup',
      'Kameratest', 'Mikrofontest', 'Lautsprechertest', 'Tastaturtest',
      'Maustest', 'Netzwerktest', 'Kompatibilitätsprüfung', 'Leistungstest'
    ],
    ja: [
      'デバイステスト', 'ハードウェアチェック', 'オンライン会議', 'ゲーミングセットアップ',
      'カメラテスト', 'マイクテスト', 'スピーカーテスト', 'キーボードテスト',
      'マウステスト', 'ネットワークテスト', '互換性チェック', 'パフォーマンステスト'
    ],
    ko: [
      '장치 테스트', '하드웨어 검사', '온라인 회의', '게이밍 설정',
      '카메라 테스트', '마이크 테스트', '스피커 테스트', '키보드 테스트',
      '마우스 테스트', '네트워크 테스트', '호환성 검사', '성능 테스트'
    ]
  };

  // 基础关键词
  const baseKeywords = commonKeywords[language] || commonKeywords.en;
  
  // 从内容中提取关键词（简单实现）
  const contentKeywords = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3)
    .slice(0, 10);

  return [...baseKeywords, ...contentKeywords].slice(0, 20);
};

/**
 * 分析关键词密度
 */
export const analyzeKeywordDensity = (content: string, keyword: string): number => {
  if (!content || !keyword) return 0;
  
  const contentLower = content.toLowerCase();
  const keywordLower = keyword.toLowerCase();
  const keywordCount = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
  const totalWords = content.trim().split(/\s+/).length;
  
  return totalWords > 0 ? (keywordCount / totalWords) * 100 : 0;
};

/**
 * 生成相关页面建议
 */
export const generateRelatedPages = (
  currentPage: string,
  t: (key: string) => string,
  language: Language = 'en'
): Array<{ title: string; url: string; description: string }> => {
  const baseUrl = language === 'en' ? '' : `/${language}`;
  
  const allPages = [
    {
      key: 'camera-test',
      title: t('cameraTest'),
      url: `${baseUrl}/camera-test`,
      description: t('cameraTestDescription')
    },
    {
      key: 'microphone-test',
      title: t('microphoneTest'),
      url: `${baseUrl}/microphone-test`,
      description: t('microphoneTestDescription')
    },
    {
      key: 'headphones-test',
      title: t('headphonesTest'),
      url: `${baseUrl}/headphones-test`,
      description: t('headphonesTestDescription')
    },
    {
      key: 'keyboard-test',
      title: t('keyboardTest'),
      url: `${baseUrl}/keyboard-test`,
      description: t('keyboardTestDescription')
    },
    {
      key: 'mouse-test',
      title: t('mouseTest'),
      url: `${baseUrl}/mouse-test`,
      description: t('mouseTestDescription')
    },
    {
      key: 'network-test',
      title: t('networkQualityTest'),
      url: `${baseUrl}/network-test`,
      description: t('networkTestDescription')
    }
  ];

  // 过滤掉当前页面，随机选择3-4个相关页面
  return allPages
    .filter(page => !currentPage.includes(page.key))
    .sort(() => Math.random() - 0.5)
    .slice(0, 4);
};

/**
 * 生成页面元数据摘要
 */
export const generatePageSummary = (
  title: string,
  description: string,
  keywords: string[],
  readingTime?: number
): string => {
  let summary = `页面标题: ${title}\n`;
  summary += `描述: ${description}\n`;
  summary += `关键词: ${keywords.join(', ')}\n`;
  
  if (readingTime) {
    summary += `预计阅读时间: ${readingTime} 分钟\n`;
  }
  
  return summary;
};

/**
 * 验证SEO基础要求
 */
export const validateSEORequirements = (
  config: {
    title?: string;
    description?: string;
    keywords?: string[];
  },
  t: (key: string) => string
): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];

  // 检查标题
  if (!config.title) {
    issues.push(t('seoTitleMissing'));
  } else if (config.title.length > 60) {
    issues.push(t('seoTitleTooLong'));
  } else if (config.title.length < 10) {
    issues.push(t('seoTitleTooShort'));
  }

  // 检查描述
  if (!config.description) {
    issues.push(t('seoDescriptionMissing'));
  } else if (config.description.length > 160) {
    issues.push(t('seoDescriptionTooLong'));
  } else if (config.description.length < 50) {
    issues.push(t('seoDescriptionTooShort'));
  }

  // 检查关键词
  if (!config.keywords || config.keywords.length === 0) {
    issues.push('缺少关键词');
  } else if (config.keywords.length > 10) {
    issues.push('关键词过多（建议10个以内）');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}; 