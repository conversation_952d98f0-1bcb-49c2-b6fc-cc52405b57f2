/**
 * Español - Glosario Técnico
 * Contiene definiciones de términos técnicos relacionados con pruebas de dispositivos
 */

import type { GlossaryTranslation } from '../types';

export const esGlossary: GlossaryTranslation = {
  title: "Glosario Técnico",
  terms: {
    resolution: {
      title: "Resolución",
      description: "Dimensiones de píxeles de video como 1920x1080, valores más altos significan mejor calidad de imagen"
    },
    frameRate: {
      title: "Velocidad de Fotogramas",
      description: "Número de fotogramas de imagen mostrados por segundo, usualmente expresado en fps, afecta la suavidad del video"
    },
    latency: {
      title: "Latencia",
      description: "Retraso de tiempo en la transmisión de datos, medido en milisegundos (ms), menor es mejor"
    },
    bandwidth: {
      title: "Ancho de Banda",
      description: "Capacidad de transmisión de red, usualmente medida en Mbps, determina la velocidad de transferencia de datos"
    },
    sampleRate: {
      title: "Frecuencia de Muestreo",
      description: "Número de muestras de audio recopiladas por segundo, frecuencias comunes incluyen 44.1kHz, 48kHz"
    },
    bitRate: {
      title: "Tasa de Bits",
      description: "Velocidad de transmisión de datos de audio o video, afecta la calidad y el tamaño del archivo"
    },
    dpi: {
      title: "DPI",
      description: "Unidad de sensibilidad del ratón, representa píxeles movidos por pulgada"
    },
    pollingRate: {
      title: "Frecuencia de Sondeo",
      description: "Frecuencia con la que el dispositivo reporta estado a la computadora, medida en Hz, mayor significa respuesta más rápida"
    },
    fps: {
      title: "Velocidad de Fotogramas (FPS)",
      description: "Número de fotogramas de imagen mostrados por segundo, afecta la suavidad y calidad del video"
    },
    megapixel: {
      title: "Megapíxel",
      description: "Unidad básica de imágenes digitales, megapíxeles determinan la claridad de la imagen"
    },
    exposure: {
      title: "Exposición",
      description: "Nivel de sensibilidad a la luz de la cámara, afecta el brillo y claridad de la imagen"
    },
    noiseReduction: {
      title: "Reducción de Ruido",
      description: "Tecnología para eliminar ruido y sonidos de fondo del audio"
    },
    sensitivity: {
      title: "Sensibilidad",
      description: "Capacidad del micrófono para detectar señales de sonido"
    },
    frequency: {
      title: "Frecuencia",
      description: "Número de vibraciones de sonido o señales eléctricas, medido en Hz"
    },
    impedance: {
      title: "Impedancia",
      description: "Resistencia del dispositivo de audio a la corriente eléctrica, afecta el emparejamiento de potencia"
    },
    soundStage: {
      title: "Escenario Sonoro",
      description: "Sentido espacial y posicionamiento del audio, refleja las capas de calidad del sonido"
    },
    drivers: {
      title: "Controladores",
      description: "Componentes centrales en auriculares que convierten señales eléctricas en sonido"
    },
    thd: {
      title: "Distorsión Armónica Total",
      description: "Indicador de medición del nivel de distorsión de señal de audio"
    },
    keyTravel: {
      title: "Recorrido de Tecla",
      description: "Distancia que se mueve una tecla desde la posición de reposo hasta la activación"
    },
    actuationForce: {
      title: "Fuerza de Activación",
      description: "Presión mínima requerida para activar una tecla"
    },
    tactile: {
      title: "Táctil",
      description: "Retroalimentación táctil cuando se activa la tecla"
    },
    linear: {
      title: "Lineal",
      description: "Característica de presión de tecla proporcional a la distancia de recorrido"
    },
    polling: {
      title: "Sondeo",
      description: "Mecanismo para que el sistema verifique periódicamente el estado del dispositivo"
    },
    acceleration: {
      title: "Aceleración",
      description: "Característica de respuesta del ratón durante movimiento rápido"
    },
    liftOffDistance: {
      title: "Distancia de Despegue",
      description: "Altura máxima que se puede levantar el ratón mientras aún detecta"
    },
    tracking: {
      title: "Seguimiento",
      description: "Capacidad del sensor del ratón para detectar movimiento"
    },
    jitter: {
      title: "Jitter",
      description: "Grado de variación de latencia de red, afecta la estabilidad"
    },
    packetLoss: {
      title: "Pérdida de Paquetes",
      description: "Proporción de paquetes de datos perdidos durante la transmisión de red"
    },
    throughput: {
      title: "Rendimiento",
      description: "Velocidad real de transmisión de datos de red"
    },
    codec: {
      title: "Códec",
      description: "Algoritmo para comprimir y descomprimir datos de audio/video"
    },
    compression: {
      title: "Compresión",
      description: "Tecnología para reducir el tamaño de datos para ahorrar ancho de banda"
    },
    inputLag: {
      title: "Lag de Entrada",
      description: "Diferencia de tiempo desde la operación de entrada hasta la respuesta del sistema"
    }
  }
};
