/**
 * Español - Exportación Principal de Traducciones SEO
 * Combina todas las traducciones en español en un objeto unificado
 */

import { esEnhanced } from './enhanced';
import { esFooter } from './footer';
import { esKeywords } from './keywords';
import { esFAQ } from './faq';
import { esGlossary } from './glossary';
import { esTroubleshooting } from './troubleshooting';
import type { SEOTranslation } from '../types';

export const esTranslation: SEOTranslation = {
  enhanced: esEnhanced,
  seoFooter: esFooter,
  seoKeywords: esKeywords,
  faq: esFAQ,
  glossary: esGlossary,
  troubleshooting: esTroubleshooting
};

// Exportar módulos individuales para uso directo
export {
  esEnhanced,
  esFooter,
  esKeywords,
  esFAQ,
  esGlossary,
  esTroubleshooting
};
