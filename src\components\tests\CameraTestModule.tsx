import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Camera, CameraOff, Settings, Download, Maximize, Star, Check, AlertCircle, FlipHorizontal } from 'lucide-react';
import { GlassCard } from '@/components/ui/GlassCard';
import { PrimaryButton } from '@/components/ui/PrimaryButton';
import { useLanguage } from '@/hooks/useLanguage';
import { useCamera } from '@/hooks/useCamera';
import { useDeviceTestTracking, useInteractionTracking } from '@/hooks/useAnalytics';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface CameraStats {
  resolution: string;
  frameRate: number;
  deviceName: string;
}

export const CameraTestModule: React.FC = () => {
  const { t } = useLanguage();
  const {
    isActive,
    devices,
    selectedDevice,
    error,
    videoRef,
    startCamera,
    stopCamera,
    setSelectedDevice,
    hasPermission,
  } = useCamera();

  // 分析跟踪
  const { trackTestStart, trackTestComplete, trackTestError } = useDeviceTestTracking();
  const { trackClick, trackFormSubmit } = useInteractionTracking();

  const [resolution, setResolution] = useState('1920x1080');
  const [supportedResolutions, setSupportedResolutions] = useState<string[]>([]);
  const [frameRate, setFrameRate] = useState(0);
  const [showScorecard, setShowScorecard] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMirrored, setIsMirrored] = useState(false);
  const [stats, setStats] = useState<CameraStats>({
    resolution: '',
    frameRate: 0,
    deviceName: ''
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(0);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const frameRateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 获取视频轨道的实际帧率设置
  const getVideoTrackFrameRate = useCallback(() => {
    try {
      // 从useCamera hook获取mediaStream
      const video = videoRef.current;
      if (!video || !video.srcObject) return null;

      const stream = video.srcObject as MediaStream;
      const videoTracks = stream.getVideoTracks();

      if (videoTracks.length > 0) {
        const settings = videoTracks[0].getSettings();
        const capabilities = videoTracks[0].getCapabilities();

        console.log('视频轨道设置:', settings);
        console.log('视频轨道能力:', capabilities);

        return settings.frameRate || null;
      }
    } catch (error) {
      console.warn('无法获取视频轨道帧率设置:', error);
    }
    return null;
  }, []);

  // 计算实际视频帧率
  const calculateActualFrameRate = useCallback(() => {
    if (!videoRef.current) return;

    const now = performance.now();

    // 首先尝试从视频轨道设置获取帧率
    const trackFrameRate = getVideoTrackFrameRate();
    if (trackFrameRate && trackFrameRate > 0) {
      setFrameRate(Number(trackFrameRate.toFixed(1)));
      return;
    }

    // 如果无法从设置获取，则通过监听视频播放来计算
    frameCountRef.current++;

    if (lastTimeRef.current === 0) {
      lastTimeRef.current = now;
      return;
    }

    const elapsed = now - lastTimeRef.current;
    if (elapsed >= 2000) { // 2秒测量周期以获得更准确的结果
      const fps = (frameCountRef.current * 1000) / elapsed;
      // 限制帧率在合理范围内 (5-60 fps)，并四舍五入到常见帧率值
      let clampedFps = Math.max(5, Math.min(60, fps));

      // 四舍五入到常见的帧率值
      if (clampedFps >= 28 && clampedFps <= 32) clampedFps = 30;
      else if (clampedFps >= 23 && clampedFps <= 27) clampedFps = 25;
      else if (clampedFps >= 58 && clampedFps <= 62) clampedFps = 60;
      else if (clampedFps >= 13 && clampedFps <= 17) clampedFps = 15;

      setFrameRate(Number(clampedFps.toFixed(1)));
      frameCountRef.current = 0;
      lastTimeRef.current = now;
    }
  }, [getVideoTrackFrameRate]);

  // 获取实际分辨率
  const updateResolution = useCallback(() => {
    if (videoRef.current) {
      const video = videoRef.current;
      const actualResolution = `${video.videoWidth}x${video.videoHeight}`;
      setResolution(actualResolution);
      setStats(prev => ({
        ...prev,
        resolution: actualResolution,
        frameRate,
        deviceName: devices.find(d => d.deviceId === selectedDevice)?.label || ''
      }));
    }
  }, [frameRate, devices, selectedDevice]);

  // 启动测试
  const handleStartTest = async () => {
    const startTime = Date.now();

    try {
      // 跟踪测试开始
      trackTestStart('camera');

      await startCamera();

      setTimeout(() => {
        setShowScorecard(true);

        // 跟踪测试完成
        const duration = Date.now() - startTime;
        trackTestComplete('camera', 'success', duration);
      }, 3000);
    } catch (error) {
      // 跟踪测试错误
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      trackTestError('camera', errorMessage);
    }
  };

  // 拍照功能
  const takePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    if (ctx) {
      ctx.drawImage(video, 0, 0);
      const imageData = canvas.toDataURL('image/png');
      setCapturedImage(imageData);

      // 跟踪拍照操作
      trackClick('camera-take-photo', 'camera-controls', 'Take Photo');
    }
  };

  // 下载照片
  const downloadPhoto = () => {
    if (!capturedImage) return;

    const link = document.createElement('a');
    link.download = `camera-test-${Date.now()}.png`;
    link.href = capturedImage;
    link.click();

    // 跟踪下载操作
    trackClick('camera-download-photo', 'camera-controls', 'Download Photo');
  };

  // 全屏功能
  const toggleFullscreen = () => {
    if (!videoRef.current) return;

    if (!isFullscreen) {
      videoRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // 开始帧率计算和分辨率更新
  useEffect(() => {
    if (isActive && videoRef.current) {
      // 延迟一点时间让视频完全加载
      const initTimer = setTimeout(() => {
        // 首先尝试从视频轨道设置获取帧率
        const trackFrameRate = getVideoTrackFrameRate();
        if (trackFrameRate && trackFrameRate > 0) {
          setFrameRate(Number(trackFrameRate.toFixed(1)));
        } else {
          // 如果无法从轨道设置获取，则启动备用计算方法
          frameCountRef.current = 0;
          lastTimeRef.current = 0;

          // 使用requestAnimationFrame进行更精确的帧率计算
          const startFrameRateCalculation = () => {
            calculateActualFrameRate();
            if (frameRateIntervalRef.current) {
              requestAnimationFrame(startFrameRateCalculation);
            }
          };

          frameRateIntervalRef.current = setTimeout(() => {
            startFrameRateCalculation();
          }, 100);
        }
      }, 500); // 等待500ms让视频稳定

      const resolutionTimer = setInterval(updateResolution, 1000);

      return () => {
        clearTimeout(initTimer);
        if (frameRateIntervalRef.current) {
          clearTimeout(frameRateIntervalRef.current);
          frameRateIntervalRef.current = null;
        }
        clearInterval(resolutionTimer);
      };
    }
  }, [isActive, getVideoTrackFrameRate, calculateActualFrameRate, updateResolution]);

  // 获取支持的分辨率
  useEffect(() => {
    const resolutions = [
      '320x240', '640x480', '1280x720', 
      '1920x1080', '2560x1440', '3840x2160'
    ];
    setSupportedResolutions(resolutions);
  }, []);

  // 智能评分
  const getScorecard = () => {
    const resolutionScore = resolution.includes('1920x1080') ? 5 : 
                           resolution.includes('1280x720') ? 4 : 3;
    const frameRateScore = frameRate >= 25 ? 5 : frameRate >= 15 ? 3 : 2;
    const overallScore = Math.round((resolutionScore + frameRateScore) / 2);

    return {
      resolutionScore,
      frameRateScore,
      overallScore,
      resolutionText: resolutionScore >= 4 ? "真 1080p 高清画质，细节清晰" : "画质良好",
      frameRateText: frameRateScore >= 4 ? "视频流畅稳定，非常适合视频会议" : "帧率稳定",
      colorText: "色彩表现均衡，亮度适中",
      overallText: overallScore >= 4 ? "您的摄像头性能卓越，可完美胜任高清直播与专业会议" : "摄像头性能良好"
    };
  };

  const scorecard = showScorecard ? getScorecard() : null;



  return (
    <div className="min-h-screen px-6">
      <div className="w-4/5 mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
          {/* 左侧摄像头卡片 */}
          <div className="lg:col-span-4">
            <GlassCard>
              <div className="flex items-center gap-3 mb-4">
                {isActive ? (
                  <Camera className="h-6 w-6 text-green-400" />
                ) : (
                  <CameraOff className="h-6 w-6 text-white/60" />
                )}
                <h2 className="text-xl font-semibold text-white">{t("cameraTestTitle")}</h2>
              </div>

              {error && (
                <div className="bg-red-500/20 border border-red-400/50 rounded-xl p-4 mb-6">
                  <p className="text-red-200 text-sm">{error}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {/* 设备选择 */}
                {devices.length > 0 && (
                  <div>
                    <label className="block text-white/80 text-sm font-medium mb-2">
                      {t("selectCamera")}:
                    </label>
                    <Select value={selectedDevice} onValueChange={setSelectedDevice}>
                      <SelectTrigger className="w-full bg-white/10 border border-white/20 text-white">
                        <SelectValue placeholder={t('selectCamera')} />
                      </SelectTrigger>
                      <SelectContent>
                        {devices.map((device) => (
                          <SelectItem key={device.deviceId} value={device.deviceId}>
                            {device.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* 分辨率选择 */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">
                    {t("resolution")}:
                  </label>
                  <Select value={resolution} onValueChange={setResolution}>
                    <SelectTrigger className="w-full bg-white/10 border border-white/20 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {supportedResolutions.map((res) => (
                        <SelectItem key={res} value={res}>
                          {res}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 帧率显示 */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">
                    {t("frameRate")}:
                  </label>
                  <div className="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white">
                    {frameRate} FPS
                  </div>
                </div>
              </div>

              {/* 视频预览区域 */}
              <div className="relative mb-4">
                <div className="relative bg-black/30 rounded-xl overflow-hidden border border-white/20 aspect-video">
                  {error && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <AlertCircle className="h-12 w-12 mx-auto mb-2 text-red-400" />
                        <p>{error}</p>
                      </div>
                    </div>
                  )}

                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                    style={{ 
                      display: isActive ? 'block' : 'none',
                      transform: isMirrored ? 'scaleX(-1)' : 'none'
                    }}
                  />

                                      {!isActive && (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <CameraOff className="h-12 w-12 text-white/30 mx-auto mb-2" />
                          <p className="text-white/50 text-sm">{t("cameraPlaceholder")}</p>
                        </div>
                      </div>
                    )}

                  {/* 隐藏的canvas用于拍照 */}
                  <canvas ref={canvasRef} className="hidden" />
                </div>
              </div>

              {/* 控制按钮 */}
              <div className="flex justify-center">
                {!isActive ? (
                  <PrimaryButton onClick={handleStartTest} size="lg">
                    <Camera className="h-5 w-5" />
                    {t("startCameraTest")}
                  </PrimaryButton>
                ) : (
                  <div className="space-y-3">
                    {/* 第一排：停止和拍照 */}
                    <div className="flex items-center justify-center gap-4">
                      <PrimaryButton 
                        onClick={stopCamera} 
                        variant="secondary" 
                        size="lg"
                        className="group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/25 min-w-[120px]"
                      >
                        <CameraOff className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                        <span className="ml-2">{t("stopCamera")}</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>
                      
                      <PrimaryButton 
                        onClick={takePhoto} 
                        variant="outline" 
                        size="lg"
                        className="group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 min-w-[120px]"
                      >
                        <Camera className="h-5 w-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-6" />
                        <span className="ml-2">{t('takePhoto')}</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>
                    </div>

                    {/* 第二排：镜像和全屏 */}
                    <div className="flex items-center justify-center gap-4">
                      <PrimaryButton 
                        onClick={() => setIsMirrored(!isMirrored)} 
                        variant="outline" 
                        size="lg"
                        className={`group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 min-w-[120px] ${isMirrored ? 'bg-purple-500/20 border-purple-400/50' : ''}`}
                      >
                        <FlipHorizontal className={`h-5 w-5 transition-all duration-300 group-hover:scale-110 ${isMirrored ? 'rotate-180' : ''}`} />
                        <span className="ml-2">{t('mirror')}</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>

                      <PrimaryButton 
                        onClick={toggleFullscreen} 
                        variant="outline" 
                        size="lg"
                        className={`group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/25 min-w-[120px] ${isFullscreen ? 'bg-green-500/20 border-green-400/50' : ''}`}
                      >
                        <Maximize className="h-5 w-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
                        <span className="ml-2">{t('fullscreen')}</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>
                    </div>
                  </div>
                )}
              </div>

              {/* 拍照预览 */}
              {capturedImage && (
                <div className="mt-4 p-4 bg-white/5 rounded-xl border border-white/10">
                  <h3 className="text-base font-semibold text-white mb-3">{t('photoPreview')}</h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <img
                      src={capturedImage}
                      alt={t('cameraCapture')}
                      className="w-full sm:max-w-xs rounded-lg border border-white/20"
                    />
                    <div className="flex flex-row sm:flex-col gap-2">
                      <PrimaryButton 
                        onClick={downloadPhoto} 
                        size="sm" 
                        className="group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-yellow-500/25 flex-1 sm:flex-initial"
                      >
                        <Download className="h-4 w-4 mr-2 transition-transform duration-300 group-hover:scale-110 group-hover:-translate-y-1" />
                        {t('downloadPhoto')}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>
                      <PrimaryButton
                        onClick={() => setCapturedImage(null)}
                        variant="outline"
                        size="sm"
                        className="group relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/25 flex-1 sm:flex-initial"
                      >
                        <span>{t('deletePhoto')}</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      </PrimaryButton>
                    </div>
                  </div>
                </div>
              )}
            </GlassCard>
          </div>

          {/* 右侧评分和建议卡片 */}
          <div className="lg:col-span-2">
            <GlassCard>
              {/* 智能评分卡 */}
              {showScorecard && scorecard ? (
                <div>
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-bold text-white mb-2">{t('smartScorecard')}</h3>
                    <div className="flex justify-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${
                            i < scorecard.overallScore
                              ? 'text-yellow-400 fill-yellow-400'
                              : 'text-gray-400'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-white/70 text-sm">
                      {scorecard.overallText}
                    </p>
                  </div>

                  <div className="space-y-3">
                    {/* 分辨率评分 */}
                    <div className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-400 mt-0.5" />
                      <div className="flex-1">
                        <div className="font-medium text-white text-sm">
                          {t('resolution')}: {stats.resolution}
                        </div>
                        <div className="text-xs text-white/70">
                          {scorecard.resolutionText}
                        </div>
                      </div>
                    </div>

                    {/* 帧率评分 */}
                    <div className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-400 mt-0.5" />
                      <div className="flex-1">
                        <div className="font-medium text-white text-sm">
                          {t('frameRate')}: {frameRate} FPS
                        </div>
                        <div className="text-xs text-white/70">
                          {scorecard.frameRateText}
                        </div>
                      </div>
                    </div>

                    {/* 色彩与亮度 */}
                    <div className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-400 mt-0.5" />
                      <div className="flex-1">
                        <div className="font-medium text-white text-sm">
                          {t('colorBrightness')}
                        </div>
                        <div className="text-xs text-white/70">
                          {scorecard.colorText}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="mb-3">
                    <Star className="h-8 w-8 text-white/30 mx-auto" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{t('smartScorecard')}</h3>
                  <p className="text-white/70 text-sm">
                    {t('startTestToSeeScore')}
                  </p>
                </div>
              )}

              {/* 状态提示 */}
              <div className="mt-4">
                {isActive ? (
                  <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-3">
                    <h4 className="text-green-200 font-medium mb-1 text-sm">{t("cameraWorking")}</h4>
                    <p className="text-green-200/80 text-xs">
                      {t("cameraWorkingDesc")}
                    </p>
                  </div>
                ) : (
                  <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-3">
                    <h4 className="text-blue-200 font-medium mb-1 text-sm">{t("cameraTips")}</h4>
                    <ul className="text-blue-200/80 text-xs space-y-0.5">
                      <li>{t("cameraTip1")}</li>
                      <li>{t("cameraTip2")}</li>
                      <li>{t("cameraTip3")}</li>
                      <li>{t("cameraTip4")}</li>
                    </ul>
                  </div>
                )}
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </div>
  );
};