import { useState, useCallback, useMemo } from 'react';
import {
  WorkflowState,
  TestStepState,
  TestStepConfig,
  EnhancedTestResult,
  NavigationAction,
  NavigationControl,
  TestStatus,
  WorkflowManager
} from '@/types/testWorkflow';

interface UseWorkflowManagerProps {
  steps: TestStepConfig[];
  initialStepIndex?: number;
}

export const useWorkflowManager = ({ 
  steps, 
  initialStepIndex = 0 
}: UseWorkflowManagerProps): WorkflowManager => {
  
  // 初始化工作流程状态
  const [state, setState] = useState<WorkflowState>(() => ({
    currentStepIndex: initialStepIndex,
    steps: steps.map((config, index) => ({
      config,
      retryCount: 0,
      isActive: index === initialStepIndex,
      result: undefined
    })),
    isComplete: false,
    overallResult: undefined
  }));

  // 获取当前步骤
  const getCurrentStep = useCallback((): TestStepState | undefined => {
    return state.steps[state.currentStepIndex];
  }, [state.currentStepIndex, state.steps]);

  // 计算导航控制状态
  const getNavigationControl = useCallback((): NavigationControl => {
    const currentStep = getCurrentStep();
    const isFirstStep = state.currentStepIndex === 0;
    const isLastStep = state.currentStepIndex === state.steps.length - 1;
    
    if (!currentStep) {
      return {
        canGoNext: false,
        canGoBack: !isFirstStep,
        canRetry: false,
        canSkip: false
      };
    }

    const result = currentStep.result;
    const config = currentStep.config;
    
    // 计算是否可以进入下一步
    let canGoNext = false;
    if (result) {
      if (result.status === TestStatus.COMPLETED && result.passed) {
        canGoNext = true;
      } else if (result.status === TestStatus.FAILED && config.allowProceedOnFailure) {
        canGoNext = true;
      } else if (result.status === TestStatus.SKIPPED) {
        canGoNext = true;
      }
    }

    // 如果测试需要用户确认，且还没有结果，也允许继续（用户可以手动确认）
    if (!result && config.requiresUserConfirmation) {
      canGoNext = true;
    }

    // 计算是否可以重试
    const canRetry = result && 
                    result.status === TestStatus.FAILED && 
                    result.canRetry !== false &&
                    currentStep.retryCount < (config.maxRetries || 3);

    // 计算是否可以跳过
    const canSkip = config.canSkip === true && 
                   (!result || result.status !== TestStatus.COMPLETED);

    return {
      canGoNext: canGoNext || isLastStep,
      canGoBack: !isFirstStep,
      canRetry,
      canSkip,
      nextButtonText: isLastStep ? 'viewResults' : 'next',
      backButtonText: isFirstStep ? 'home' : 'back',
      showRetryButton: canRetry,
      showSkipButton: canSkip
    };
  }, [state, getCurrentStep]);

  // 检查是否可以进入下一步
  const canProceedToNext = useCallback((): boolean => {
    return getNavigationControl().canGoNext;
  }, [getNavigationControl]);

  // 更新步骤结果
  const updateStepResult = useCallback((stepIndex: number, result: EnhancedTestResult) => {
    setState(prevState => {
      const newSteps = [...prevState.steps];
      if (newSteps[stepIndex]) {
        newSteps[stepIndex] = {
          ...newSteps[stepIndex],
          result: {
            ...result,
            timestamp: new Date()
          }
        };
      }
      
      return {
        ...prevState,
        steps: newSteps
      };
    });
  }, []);

  // 重置步骤
  const resetStep = useCallback((stepIndex: number) => {
    setState(prevState => {
      const newSteps = [...prevState.steps];
      if (newSteps[stepIndex]) {
        newSteps[stepIndex] = {
          ...newSteps[stepIndex],
          result: undefined,
          retryCount: 0
        };
      }
      
      return {
        ...prevState,
        steps: newSteps
      };
    });
  }, []);

  // 执行导航操作
  const executeAction = useCallback((action: NavigationAction, result?: EnhancedTestResult): boolean => {
    const currentStep = getCurrentStep();
    if (!currentStep) return false;

    setState(prevState => {
      const newState = { ...prevState };
      const newSteps = [...newState.steps];

      switch (action) {
        case NavigationAction.NEXT:
          // 如果提供了结果，先更新当前步骤
          if (result) {
            newSteps[newState.currentStepIndex] = {
              ...newSteps[newState.currentStepIndex],
              result: { ...result, timestamp: new Date() }
            };
          }
          
          // 移动到下一步
          if (newState.currentStepIndex < newSteps.length - 1) {
            newSteps[newState.currentStepIndex].isActive = false;
            newState.currentStepIndex += 1;
            newSteps[newState.currentStepIndex].isActive = true;
          } else {
            // 到达最后一步，标记完成
            newState.isComplete = true;
            newSteps[newState.currentStepIndex].isActive = false;
          }
          break;

        case NavigationAction.BACK:
          if (newState.currentStepIndex > 0) {
            newSteps[newState.currentStepIndex].isActive = false;
            newState.currentStepIndex -= 1;
            newSteps[newState.currentStepIndex].isActive = true;
          }
          break;

        case NavigationAction.RETRY:
          // 重置当前步骤并增加重试计数
          newSteps[newState.currentStepIndex] = {
            ...newSteps[newState.currentStepIndex],
            result: undefined,
            retryCount: newSteps[newState.currentStepIndex].retryCount + 1
          };
          break;

        case NavigationAction.SKIP:
          // 标记当前步骤为跳过
          newSteps[newState.currentStepIndex] = {
            ...newSteps[newState.currentStepIndex],
            result: {
              status: TestStatus.SKIPPED,
              passed: false,
              timestamp: new Date(),
              allowProceedOnFailure: true
            }
          };

          // 自动进入下一步
          if (newState.currentStepIndex < newSteps.length - 1) {
            newSteps[newState.currentStepIndex].isActive = false;
            newState.currentStepIndex += 1;
            newSteps[newState.currentStepIndex].isActive = true;
          } else {
            // 到达最后一步，标记完成
            newState.isComplete = true;
            newSteps[newState.currentStepIndex].isActive = false;
          }
          break;

        default:
          return false;
      }

      return {
        ...newState,
        steps: newSteps
      };
    });

    return true;
  }, [getCurrentStep]);

  // 计算整体结果
  const overallResult = useMemo(() => {
    const completedSteps = state.steps.filter(step => 
      step.result?.status === TestStatus.COMPLETED
    ).length;
    
    const failedSteps = state.steps.filter(step => 
      step.result?.status === TestStatus.FAILED
    ).length;
    
    const skippedSteps = state.steps.filter(step => 
      step.result?.status === TestStatus.SKIPPED
    ).length;

    const passed = failedSteps === 0 && completedSteps > 0;

    return {
      passed,
      completedSteps,
      failedSteps,
      skippedSteps
    };
  }, [state.steps]);

  // 更新状态中的整体结果
  useState(() => {
    setState(prevState => ({
      ...prevState,
      overallResult
    }));
  });

  return {
    state,
    getCurrentStep,
    getNavigationControl,
    executeAction,
    updateStepResult,
    resetStep,
    canProceedToNext
  };
};
