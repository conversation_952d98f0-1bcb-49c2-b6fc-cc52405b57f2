import type { Language } from '@/config/languages';
import type { TranslationKeys } from './types';

// 翻译缓存
const translationCache: Record<string, TranslationKeys> = {};

// 加载翻译文件
export const loadTranslation = async (language: Language): Promise<TranslationKeys> => {
  // 如果已缓存，直接返回
  if (translationCache[language]) {
    return translationCache[language]!;
  }

  try {
    let translationModule: any;
    
    switch (language) {
      case 'en':
        translationModule = await import('./en');
        translationCache[language] = translationModule.en;
        break;
      case 'zh':
        translationModule = await import('./zh');
        translationCache[language] = translationModule.zh;
        break;
      case 'es':
        translationModule = await import('./es');
        translationCache[language] = translationModule.es;
        break;
      case 'de':
        translationModule = await import('./de');
        translationCache[language] = translationModule.de;
        break;
      case 'ja':
        translationModule = await import('./ja');
        translationCache[language] = translationModule.ja;
        break;
      case 'ko':
        translationModule = await import('./ko');
        translationCache[language] = translationModule.ko;
        break;
      case 'fr':
        translationModule = await import('./fr');
        translationCache[language] = translationModule.fr;
        break;
      default:
        // 回退到英语
        const { en } = await import('./en');
        translationCache[language] = en;
    }

    return translationCache[language]!;
  } catch (error) {
    console.error(`Failed to load translation for language: ${language}`, error);
    
    // 回退到英语翻译
    if (language !== 'en') {
      return loadTranslation('en');
    }
    
    // 如果连英语都加载失败，返回一个空的翻译对象
    throw new Error(`Failed to load any translation for language: ${language}`);
  }
};

// 注意：所有语言文件现在都已完整，不再需要基础翻译回退

// 预加载常用语言的翻译
export const preloadTranslations = async (languages: Language[] = ['en', 'zh']) => {
  const promises = languages.map(lang => loadTranslation(lang));
  await Promise.allSettled(promises);
};

// 获取已缓存的翻译（同步方法）
export const getCachedTranslation = (language: Language): TranslationKeys | null => {
  return translationCache[language] || null;
};

// 清除翻译缓存
export const clearTranslationCache = () => {
  Object.keys(translationCache).forEach(key => {
    delete translationCache[key as Language];
  });
};

// 导出类型和常量
export type { TranslationKeys };
export type { Language } from '@/config/languages';