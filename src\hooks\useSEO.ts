import { useEffect } from 'react';
import { useLanguage } from './useLanguage';
import { LANGUAGE_CONFIGS, Language } from '@/config/languages';

export interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  twitterSite?: string;
  noindex?: boolean;
  nofollow?: boolean;
  structuredData?: object;
}

export interface PageSEOConfig {
  [key: string]: SEOConfig;
}

export const useSEO = (seoConfig: SEOConfig) => {
  const { language, t } = useLanguage();

  useEffect(() => {
    // 设置页面标题
    if (seoConfig.title) {
      document.title = `${seoConfig.title} | ${t('siteName')}`;
    }

    // 设置或更新meta标签
    const setMetaTag = (name: string, content: string, property = false) => {
      const attribute = property ? 'property' : 'name';
      const selector = `meta[${attribute}="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // 设置基本meta标签
    if (seoConfig.description) {
      setMetaTag('description', seoConfig.description);
    }

    if (seoConfig.keywords && seoConfig.keywords.length > 0) {
      setMetaTag('keywords', seoConfig.keywords.join(', '));
    }

    // 设置robots meta标签
    const robotsContent = [];
    if (seoConfig.noindex) robotsContent.push('noindex');
    if (seoConfig.nofollow) robotsContent.push('nofollow');
    if (robotsContent.length === 0) robotsContent.push('index', 'follow');
    setMetaTag('robots', robotsContent.join(', '));

    // 设置语言相关的meta标签
    document.documentElement.lang = language;
    setMetaTag('language', language);

    // 设置Open Graph标签
    setMetaTag('og:title', seoConfig.ogTitle || seoConfig.title || t('siteName'), true);
    setMetaTag('og:description', seoConfig.ogDescription || seoConfig.description || t('siteDescription'), true);
    setMetaTag('og:type', seoConfig.ogType || 'website', true);
    setMetaTag('og:locale', getOGLocale(language), true);
    setMetaTag('og:site_name', t('siteName'), true);

    if (seoConfig.ogImage) {
      setMetaTag('og:image', seoConfig.ogImage, true);
    }

    // 设置Twitter Card标签
    setMetaTag('twitter:card', seoConfig.twitterCard || 'summary_large_image');
    setMetaTag('twitter:title', seoConfig.ogTitle || seoConfig.title || t('siteName'));
    setMetaTag('twitter:description', seoConfig.ogDescription || seoConfig.description || t('siteDescription'));

    if (seoConfig.twitterSite) {
      setMetaTag('twitter:site', seoConfig.twitterSite);
    }

    // 设置canonical链接
    const setCanonical = (url: string) => {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.rel = 'canonical';
        document.head.appendChild(canonical);
      }
      canonical.href = url;
    };

    if (seoConfig.canonical) {
      setCanonical(seoConfig.canonical);
    }

    // 设置hreflang标签
    setHreflangTags();

    // 设置结构化数据
    if (seoConfig.structuredData) {
      setStructuredData(seoConfig.structuredData);
    }

    // 清理函数
    return () => {
      // 清理结构化数据
      const existingScript = document.querySelector('script[type="application/ld+json"]');
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, [seoConfig, language, t]);

  // 设置hreflang标签
  const setHreflangTags = () => {
    // 清除现有的hreflang标签
    document.querySelectorAll('link[hreflang]').forEach(link => link.remove());

    const currentPath = window.location.pathname;
    const baseUrl = window.location.origin;

    // 为每种支持的语言添加hreflang标签
    Object.values(LANGUAGE_CONFIGS).forEach(langConfig => {
      const hreflang = document.createElement('link');
      hreflang.rel = 'alternate';
      hreflang.hreflang = langConfig.code;
      hreflang.href = `${baseUrl}/${langConfig.code}${currentPath}`;
      document.head.appendChild(hreflang);
    });

    // 添加x-default
    const xDefault = document.createElement('link');
    xDefault.rel = 'alternate';
    xDefault.hreflang = 'x-default';
    xDefault.href = `${baseUrl}${currentPath}`;
    document.head.appendChild(xDefault);
  };

  // 设置结构化数据
  const setStructuredData = (data: object) => {
    // 清除现有的结构化数据
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
  };

  // 获取Open Graph语言代码
  const getOGLocale = (language: Language): string => {
    const localeMap: Record<Language, string> = {
      en: 'en_US',
      zh: 'zh_CN',
      es: 'es_ES',
      de: 'de_DE',
      ja: 'ja_JP',
      ko: 'ko_KR'
    };
    return localeMap[language] || 'en_US';
  };

  return {
    updateSEO: (newConfig: Partial<SEOConfig>) => {
      // 可以用于动态更新SEO配置
      Object.assign(seoConfig, newConfig);
    }
  };
}; 