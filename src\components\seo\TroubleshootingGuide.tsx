import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { GlassCard } from "@/components/ui/GlassCard";
import { AlertTriangle, CheckCircle, HelpCircle } from "lucide-react";
import { getSEOTranslation } from "@/locales/seo";

interface TroubleshootingGuideProps {
  pageType: 'home' | 'tools' | 'camera' | 'microphone' | 'headphones' | 'keyboard' | 'mouse' | 'network' | 'meeting' | 'gaming' | 'streaming' | 'diagnostic';
}

export const TroubleshootingGuide: React.FC<TroubleshootingGuideProps> = ({ pageType }) => {
  const { t, language } = useLanguage();

  const getTroubleshootingSteps = () => {
    const troubleshootingData = getSEOTranslation(language, `troubleshooting.${pageType}`);
    if (!troubleshootingData) return [];
    
    const steps = [];
    for (let i = 1; i <= 5; i++) {
      const step = troubleshootingData[`step${i}`];
      if (step) {
        steps.push(`step${i}`);
      }
    }
    return steps;
  };

  const getCommonIssues = () => {
    const troubleshootingData = getSEOTranslation(language, `troubleshooting.${pageType}`);
    if (!troubleshootingData) return [];
    
    const issues = [];
    for (let i = 1; i <= 3; i++) {
      const issue = troubleshootingData[`issue${i}`];
      if (issue) {
        issues.push(`issue${i}`);
      }
    }
    return issues;
  };

  const steps = getTroubleshootingSteps();
  const issues = getCommonIssues();

  if (steps.length === 0 && issues.length === 0) return null;

  return (
    <GlassCard className="p-6 mt-8">
      <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
        <HelpCircle className="h-5 w-5 text-blue-400" />
        {getSEOTranslation(language, 'troubleshooting.title')}
      </h3>
      
      {/* Common Issues */}
      {issues.length > 0 && (
        <div className="mb-8">
          <h4 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-yellow-400" />
            {getSEOTranslation(language, 'troubleshooting.commonIssues')}
          </h4>
          <div className="space-y-3">
            {issues.map((issueKey, index) => {
              const solutionKey = issueKey.replace('issue', 'solution');
              const troubleshootingData = getSEOTranslation(language, `troubleshooting.${pageType}`);
              return (
                <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h5 className="text-white font-medium mb-2">
                    {troubleshootingData[issueKey]}
                  </h5>
                  <p className="text-white/70 text-sm">
                    {troubleshootingData[solutionKey]}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Step-by-step Guide */}
      {steps.length > 0 && (
        <div>
          <h4 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-400" />
            {getSEOTranslation(language, 'troubleshooting.stepByStep')}
          </h4>
          <div className="space-y-3">
            {steps.map((stepKey, index) => {
              const troubleshootingData = getSEOTranslation(language, `troubleshooting.${pageType}`);
              return (
                <div key={index} className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center text-blue-300 text-sm font-medium">
                    {index + 1}
                  </div>
                  <p className="text-white/70 text-sm pt-0.5">
                    {troubleshootingData[stepKey]}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </GlassCard>
  );
};