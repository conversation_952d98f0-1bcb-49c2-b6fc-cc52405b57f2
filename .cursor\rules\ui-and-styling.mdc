---
alwaysApply: false
---
# UI 和样式

## 风格

整体页面风格需要符合苹果的“液态玻璃”设计风格。


## UI 组件库 (shadcn/ui)

项目使用 [shadcn/ui](https://ui.shadcn.com/) 作为组件库。这些组件不是从 npm 安装的，而是直接复制到 [`src/components/ui`](mdc:src/components/ui/) 目录下，因此可以自由修改。

这些组件基于 [Radix UI](https://www.radix-ui.com/) (用于可访问性和交互) 和 [Tailwind CSS](https://tailwindcss.com/) (用于样式)。

## 样式

主要使用 Tailwind CSS 进行样式设计。配置文件是 [`tailwind.config.ts`](mdc:tailwind.config.ts)。

### ClassName 合并

为了动态和有条件地应用 class，项目使用了 `clsx` 和 `tailwind-merge`。在 [`src/lib/utils.ts`](mdc:src/lib/utils.ts) 中提供了一个名为 `cn` 的实用工具函数来简化这个过程。

```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```
