import React from "react";
import { useNavigate } from "react-router-dom";
import { Video, Headphones, Camera, Mic, Keyboard, Mouse, Wifi, Radio, Search } from "lucide-react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { GlassCard } from "@/components/ui/GlassCard";
import { PrimaryButton } from "@/components/ui/PrimaryButton";
import { useLanguage } from "@/hooks/useLanguage";
import { generatePageSEO } from "@/config/seo";
import { SEOFooter } from "@/components/seo/SEOFooter";
import { EnhancedSEO } from "@/components/seo/EnhancedSEO";
import { FAQ } from "@/components/seo/FAQ";
import { Glossary } from "@/components/seo/Glossary";
import { TroubleshootingGuide } from "@/components/seo/TroubleshootingGuide";

export const ScenarioSelectionPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  
  // 生成SEO配置
  const seoConfig = generatePageSEO('home', t, window.location.origin);

  const scenarios = [
    {
      id: "meeting",
      title: t("onlineMeeting"),
      description: t("onlineMeetingDesc"),
      icon: Video,
      color: 'from-blue-500 to-cyan-500',
      features: [t("networkQualityTest"), t("microphoneTest"), t("cameraTest"), t("headphonesTest")],
      duration: t("duration5to8"),
      difficulty: t("difficultyBeginner"),
      primary: true,
      usage: t("usageMeeting")
    },
    {
      id: "gaming",
      title: t("gamingSetup"),
      description: t("gamingSetupDesc"),
      icon: Headphones,
      color: 'from-purple-500 to-pink-500',
      features: [t("networkQualityTest"), t("microphoneTest"), t("keyboardTest"), t("mouseTest"), t("headphonesTest")],
      duration: t("duration8to12"),
      difficulty: t("difficultyAdvanced"),
      primary: false,
      usage: t("usageGaming")
    },
    {
      id: "streaming",
      title: t("streamingScenario"),
      description: t("streamingScenarioDesc"),
      icon: Radio,
      color: 'from-pink-500 to-rose-500',
      features: [t("networkQualityTest"), t("microphoneTest"), t("cameraTest"), t("headphonesTest")],
      duration: t("duration6to10"),
      difficulty: t("difficultyIntermediate"),
      primary: false,
      usage: t("usageStreaming")
    },
    {
      id: "diagnostic",
      title: t("diagnosticScenario"),
      description: t("diagnosticScenarioDesc"),
      icon: Search,
      color: 'from-orange-500 to-red-500',
      features: [t("networkQualityTest"), t("microphoneTest"), t("cameraTest"), t("keyboardTest"), t("mouseTest"), t("headphonesTest")],
      duration: t("duration10to15"),
      difficulty: t("difficultyComprehensive"),
      primary: false,
      usage: t("usageDiagnostic")
    }
  ];

  const handleScenarioSelect = (scenarioId: string) => {
    navigate(`/test/${scenarioId}`);
  };

  return (
    <MainLayout seoConfig={seoConfig}>
      <div className="text-center mb-8 sm:mb-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4">
          {t("siteName")}
        </h1>
        <p className="text-lg sm:text-xl text-white/70 max-w-2xl mx-auto px-4">
          {t("selectScenario")}
        </p>
      </div>

      {/* 场景网格 - 2x2布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 max-w-6xl mx-auto mb-12">
        {scenarios.map((scenario) => {
          const IconComponent = scenario.icon;

          return (
            <GlassCard
              key={scenario.id}
              className="group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              onClick={() => handleScenarioSelect(scenario.id)}
            >
              {/* 渐变背景 */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${scenario.color} opacity-10 rounded-2xl group-hover:opacity-20 transition-opacity duration-300`}
              />

              {/* 推荐标签 */}
              {scenario.primary && (
                <div className="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
                  {t("recommended")}
                </div>
              )}

              {/* 内容 */}
              <div className="relative flex flex-col h-full p-6 lg:p-8">
                {/* 图标和标题 */}
                <div className="flex items-center mb-6">
                  <div className={`p-4 rounded-xl bg-gradient-to-br ${scenario.color} mr-4`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl lg:text-2xl font-semibold text-white group-hover:text-blue-300 transition-colors">
                      {scenario.title}
                    </h3>
                    <p className="text-white/60 text-sm mt-1">{scenario.usage}</p>
                  </div>
                </div>

                {/* 描述 */}
                <p className="text-white/70 mb-6 text-base leading-relaxed">
                  {scenario.description}
                </p>

                {/* 测试项目 */}
                <div className="mb-6 flex-1">
                  <h4 className="text-white font-medium mb-3 flex items-center">
                    <Video className="w-4 h-4 mr-2" />
                    {t("includedTests")}
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {scenario.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-white/60">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 元信息 */}
                <div className="flex items-center justify-between mb-6 text-sm">
                  <div className="flex items-center text-white/50">
                    <Wifi className="w-4 h-4 mr-1" />
                    <span>{scenario.duration}</span>
                  </div>
                  <div className="flex items-center text-white/50">
                    <Video className="w-4 h-4 mr-1" />
                    <span>{scenario.difficulty}</span>
                  </div>
                </div>

                {/* 行动按钮 */}
                <div className="flex items-center justify-between mt-auto">
                  <div className="text-white/50 text-sm">
                    {t("completeHardwareCheck")}
                  </div>

                  <PrimaryButton
                    variant={scenario.primary ? "primary" : "secondary"}
                    size="sm"
                    className="group-hover:scale-105 transition-transform"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleScenarioSelect(scenario.id);
                    }}
                  >
                    {t('startTest')}
                  </PrimaryButton>
                </div>
              </div>
            </GlassCard>
          );
        })}
      </div>

      {/* Additional Tools Section */}
      <div className="mt-8 sm:mt-12">
        <h2 className="text-xl sm:text-2xl font-semibold text-white text-center mb-6 sm:mb-8 px-4">
          {t("individualDeviceTests")}
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 max-w-5xl mx-auto">
          {/* 网络质量测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer flex flex-col">
            <div className="flex flex-col h-full p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Wifi className="h-6 w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("networkQualityTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4 flex-1">
                {t("networkQualityTestDesc")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/network")}
              >
                {t("networkQualityTest")}
              </PrimaryButton>
            </div>
          </GlassCard>

          {/* 麦克风测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer flex flex-col">
            <div className="flex flex-col h-full p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Mic className="h-6 w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("microphoneTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4 flex-1">
                {t("micTestDesc2")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/microphone")}
              >
                {t("microphoneTest")}
              </PrimaryButton>
            </div>
          </GlassCard>

          {/* 摄像头测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer flex flex-col">
            <div className="flex flex-col h-full p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Camera className="h-6 w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("cameraTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4 flex-1">
                {t("cameraTestDesc2")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/camera")}
              >
                {t("cameraTest")}
              </PrimaryButton>
            </div>
          </GlassCard>

          {/* 键盘测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer">
            <div className="p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Keyboard className="h-5 w-5 sm:h-6 sm:w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("keyboardTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4">
                {t("keyboardTestDesc")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/keyboard")}
              >
                {t("keyboardTest")}
              </PrimaryButton>
            </div>
          </GlassCard>

          {/* 鼠标测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer">
            <div className="p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Mouse className="h-5 w-5 sm:h-6 sm:w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("mouseTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4">
                {t("mouseTestDesc")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/mouse")}
              >
                {t("mouseTest")}
              </PrimaryButton>
            </div>
          </GlassCard>

          {/* 耳机测试 */}
          <GlassCard className="text-center hover:bg-white/15 transition-all duration-300 cursor-pointer">
            <div className="p-4 sm:p-6">
              <div className="bg-white/10 rounded-full p-3 w-fit mx-auto mb-4">
                <Headphones className="h-5 w-5 sm:h-6 sm:w-6 text-white/70" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-white mb-2">{t("headphonesTest")}</h3>
              <p className="text-white/60 text-xs sm:text-sm mb-4">
                {t("headphonesTestDesc")}
              </p>
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={() => navigate("/tools/headphones")}
              >
                {t("headphonesTest")}
              </PrimaryButton>
            </div>
          </GlassCard>
        </div>
      </div>
      
      {/* SEO组件 */}
      <div className="container mx-auto max-w-6xl px-4">
        <EnhancedSEO pageType="home" />
        <FAQ pageType="home" />
        <Glossary pageType="home" />
        <TroubleshootingGuide pageType="home" />
        <SEOFooter pageType="home" />
      </div>
    </MainLayout>
  );
};