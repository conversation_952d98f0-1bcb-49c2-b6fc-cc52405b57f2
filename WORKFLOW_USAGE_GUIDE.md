# 新工作流程系统使用指南

## 概述

新的工作流程系统提供了更灵活、更用户友好的测试步骤导航体验。主要改进包括：

- 智能的导航按钮控制
- 清晰的测试状态指示
- 支持重试和跳过操作
- 场景特定的配置
- 改进的用户引导

## 快速开始

### 1. 访问演示页面
访问 `/zh/demo/workflow` 查看新工作流程系统的演示。

### 2. 选择测试场景
- **会议场景**：适用于在线会议，配置较为宽松
- **游戏场景**：适用于游戏设置，对外设要求更严格
- **直播场景**：适用于直播和内容创作，对音视频要求高
- **诊断场景**：适用于设备诊断，允许所有测试失败

### 3. 体验新功能
- 模拟测试通过/失败
- 观察导航按钮的智能控制
- 尝试重试和跳过功能
- 查看测试进度指示

## 主要功能

### 1. 智能导航控制

#### 下一步按钮启用条件
- ✅ 测试通过
- ✅ 测试失败但允许失败后继续
- ✅ 测试被跳过
- ✅ 需要用户确认的测试（即使没有结果）

#### 重试按钮显示条件
- 测试失败
- 允许重试（`canRetry !== false`）
- 未达到最大重试次数

#### 跳过按钮显示条件
- 步骤配置允许跳过（`canSkip: true`）
- 测试未完成或失败

### 2. 测试状态指示

#### 状态图标
- 🕐 未开始：时钟图标
- 🔄 进行中：旋转加载图标
- ✅ 完成且通过：绿色勾选
- ❌ 失败：红色叉号
- ⏭️ 跳过：黄色跳过图标

#### 状态文本
- 清晰的中英文状态描述
- 失败时显示具体原因
- 实时更新状态变化

### 3. 进度指示

#### 进度条
- 显示当前步骤和总步骤数
- 百分比进度显示
- 平滑的动画过渡

#### 步骤信息
- 步骤标题和描述
- 当前步骤配置信息
- 测试要求说明

## 场景配置

### 会议场景特点
```typescript
{
  allowProceedOnFailure: true,  // 大部分测试失败后可继续
  canSkip: true,               // 允许跳过非关键测试
  requiresUserConfirmation: true // 音频测试需要用户确认
}
```

### 游戏场景特点
```typescript
{
  allowProceedOnFailure: false, // 外设测试失败不能继续
  canSkip: false,              // 不允许跳过关键测试
  maxRetries: 3                // 支持多次重试
}
```

### 直播场景特点
```typescript
{
  allowProceedOnFailure: false, // 音视频测试必须通过
  canSkip: false,              // 不允许跳过核心功能
  requiresUserConfirmation: false // 自动检测质量
}
```

### 诊断场景特点
```typescript
{
  allowProceedOnFailure: true,  // 允许所有测试失败
  canSkip: true,               // 允许跳过任何测试
  maxRetries: 3                // 支持重试诊断
}
```

## 开发者指南

### 1. 创建新的测试步骤

```typescript
// 在 testScenarios.ts 中添加新步骤
{
  key: 'newTest',
  title: 'newTestTitle',
  description: 'newTestDesc',
  number: 3,
  allowProceedOnFailure: false,
  requiresUserConfirmation: true,
  canSkip: false,
  autoAdvance: false,
  maxRetries: 3
}
```

### 2. 实现测试组件

```typescript
interface TestComponentProps {
  onTestResult?: (result: EnhancedTestResult) => void;
}

const MyTestComponent: React.FC<TestComponentProps> = ({ onTestResult }) => {
  // 测试逻辑
  const handleTestComplete = () => {
    onTestResult?.({
      status: TestStatus.COMPLETED,
      passed: true,
      details: { /* 测试详情 */ },
      timestamp: new Date()
    });
  };

  return <div>测试内容</div>;
};
```

### 3. 集成到工作流程

```typescript
// 在 TestWorkflowPage.tsx 中添加新的测试步骤
case "newTest":
  return (
    <TestStepController
      onAction={handleAction}
      navigationControl={navigationControl}
      testResult={currentStepResult}
      stepTitle={currentStep?.config.title ? t(currentStep.config.title) : undefined}
      showProgress={true}
      currentStep={currentIndex + 1}
      totalSteps={workflowManager.state.steps.length}
    >
      <TestWrapper onTestComplete={handleTestComplete}>
        <MyTestComponent />
      </TestWrapper>
    </TestStepController>
  );
```

## 最佳实践

### 1. 测试组件设计
- 保持测试组件的单一职责
- 及时报告测试结果
- 提供清晰的用户反馈
- 支持重试机制

### 2. 用户体验
- 提供明确的操作指引
- 显示测试进度和状态
- 失败时给出具体原因
- 支持灵活的导航选项

### 3. 错误处理
- 优雅处理测试失败
- 提供重试选项
- 记录详细的错误信息
- 支持跳过非关键测试

### 4. 性能优化
- 避免不必要的状态更新
- 使用适当的缓存策略
- 优化测试组件的渲染
- 减少网络请求

## 故障排除

### 常见问题

#### 1. 下一步按钮被禁用
- 检查测试是否已完成
- 确认步骤配置是否正确
- 验证测试结果格式

#### 2. 重试按钮不显示
- 确认测试状态为失败
- 检查 `canRetry` 配置
- 验证重试次数限制

#### 3. 跳过按钮不显示
- 确认步骤配置 `canSkip: true`
- 检查测试状态
- 验证场景配置

### 调试技巧

#### 1. 使用演示页面
- 访问 `/zh/demo/workflow` 进行调试
- 模拟不同的测试结果
- 观察状态变化

#### 2. 检查控制台
- 查看工作流程状态
- 监控导航控制变化
- 检查错误信息

#### 3. 使用开发工具
- React DevTools 查看组件状态
- Network 面板监控请求
- Console 查看日志信息

## 更新日志

### v1.0.0 (当前版本)
- ✅ 智能导航控制
- ✅ 测试状态指示
- ✅ 重试和跳过功能
- ✅ 场景特定配置
- ✅ 进度指示器
- ✅ 用户体验改进

### 计划功能
- 🔄 动态配置加载
- 🔄 个性化测试流程
- 🔄 分析和监控集成
- 🔄 无障碍访问增强
- 🔄 多语言支持完善

## 支持

如有问题或建议，请：
1. 查看演示页面了解功能
2. 检查配置文件和文档
3. 使用开发工具调试
4. 提交 Issue 或 PR

---

*最后更新：2024年7月*
