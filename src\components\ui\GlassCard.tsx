import * as React from "react";
import { cn } from "@/lib/utils";

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: "default" | "large" | "compact";
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ className, children, variant = "default", ...props }, ref) => {
    const variants = {
      default: "p-6",
      large: "p-8",
      compact: "p-4",
    };

    return (
      <div
        ref={ref}
        className={cn(
          "bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20",
          "shadow-xl hover:bg-white/15 transition-all duration-300",
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
GlassCard.displayName = "GlassCard";

export { GlassCard };