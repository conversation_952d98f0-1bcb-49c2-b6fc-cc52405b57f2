/**
 * English - Troubleshooting Guide
 * Contains step-by-step solutions for common device problems
 */

import type { TroubleshootingTranslation } from '../types';

export const enTroubleshooting: TroubleshootingTranslation = {
  title: "Troubleshooting Guide",
  commonIssues: "Common Issues",
  stepByStep: "Solution Steps",
  camera: {
    // Basic Issues
    issue1: "Camera black screen or won't start",
    solution1: "Check browser permission settings, re-authorize camera access, restart browser and try again.",
    issue2: "Video stuttering or lag",
    solution2: "Close other programs using camera, check system resource usage, reduce video quality settings.",
    issue3: "Blurry or unclear image",
    solution3: "Clean camera lens, adjust lighting conditions, check focus settings.",

    // Extended Issues
    issue4: "Camera not working in specific applications",
    solution4: "Check application permission settings, ensure camera is not exclusively used by other programs, try running application as administrator.",
    issue5: "Camera resolution or frame rate abnormal",
    solution5: "Update camera drivers in Device Manager, check if camera specifications support current settings, adjust video settings in application.",
    issue6: "Camera color distortion or overexposure",
    solution6: "Adjust ambient lighting, check camera auto-exposure settings, manually adjust white balance and exposure parameters in camera software.",
    issue7: "USB camera frequently disconnects",
    solution7: "Replace USB cable, try different USB ports, check if USB port power supply is sufficient, disable USB selective suspend settings.",
    issue8: "Camera driver conflicts",
    solution8: "Uninstall conflicting drivers, download latest drivers from manufacturer's website, use Device Manager to roll back to stable driver version.",
    issue9: "Camera audio-video sync issues in video calls",
    solution9: "Check network connection stability, close unnecessary background programs, adjust video encoding settings, restart router to improve network quality.",
    issue10: "Camera privacy indicator light abnormal",
    solution10: "Check for malware, run system security scan, check background running programs, confirm camera permission settings are correct.",
    issue11: "Multi-camera device switching problems",
    solution11: "Specify default camera in system settings, check application camera selection settings, ensure drivers support multi-device management.",
    issue12: "Camera severe noise in low light environments",
    solution12: "Increase ambient lighting, adjust camera ISO settings, use noise reduction software, consider upgrading to camera with better low-light performance.",

    // Step-by-step Solutions
    step1: "Confirm camera hardware connection is normal",
    step2: "Check browser camera permission settings",
    step3: "Restart browser and re-authorize",
    step4: "Test camera functionality in other applications",
    step5: "If problems persist, try updating camera drivers",
    step6: "Check Windows privacy settings for camera permissions",
    step7: "Run Windows troubleshooter",
    step8: "Check camera status in Device Manager",
    step9: "Try testing camera in safe mode",
    step10: "Consider resetting camera settings to default",

    // Cross-platform Operation Steps
    windowsSteps: [
      "Open Settings > Privacy > Camera, ensure apps are allowed to access camera",
      "Check camera driver status in Device Manager",
      "Run Windows Update to check for driver updates",
      "Use Camera app to test basic functionality",
      "Check if antivirus software is blocking camera access"
    ],
    macosSteps: [
      "Open System Preferences > Security & Privacy > Camera",
      "Ensure relevant apps have camera permissions",
      "Reset SMC (System Management Controller)",
      "Check Activity Monitor for programs using camera",
      "Try creating new user account to test camera functionality"
    ],
    linuxSteps: [
      "Use lsusb command to check if camera is recognized",
      "Check if /dev/video* device files exist",
      "Install v4l-utils package for camera debugging",
      "Use cheese or guvcview to test camera functionality",
      "Check if user is in video user group"
    ],

    // Preventive Maintenance
    maintenance: [
      "Regularly clean camera lens to avoid dust accumulation",
      "Keep camera drivers updated to latest version",
      "Avoid using camera in high temperature environments for extended periods",
      "Regularly check USB connection cables for damage",
      "Create camera settings backup for quick recovery"
    ]
  },
  microphone: {
    // Basic Issues
    issue1: "No sound from microphone or low volume",
    solution1: "Check microphone permissions, volume settings, ensure device is not muted, adjust input volume.",
    issue2: "Poor audio quality or noise",
    solution2: "Choose quiet environment, adjust microphone position, enable noise reduction, check device connections.",
    issue3: "Echo or feedback issues",
    solution3: "Use headphones instead of speakers, reduce volume, enable echo cancellation features.",

    // Extended Issues
    issue4: "Severe microphone latency",
    solution4: "Check audio drivers, reduce audio buffer size, disable audio enhancements, use professional audio interface.",
    issue5: "Microphone only has sound on one side",
    solution5: "Check microphone connection cable, confirm audio settings for mono or stereo, test different audio ports.",
    issue6: "Microphone automatic volume adjustment abnormal",
    solution6: "Disable Windows automatic gain control, turn off application auto volume adjustment, manually set fixed volume levels.",
    issue7: "USB microphone not recognized",
    solution7: "Change USB port, check USB drivers, confirm microphone compatibility, try testing on other devices.",
    issue8: "Microphone feedback at specific frequencies",
    solution8: "Adjust distance and angle between microphone and speakers, use equalizer to reduce problem frequencies, check room acoustics.",
    issue9: "Bluetooth microphone connection unstable",
    solution9: "Check Bluetooth drivers, clear Bluetooth cache, re-pair device, ensure device is within effective range.",
    issue10: "Microphone recording has electrical noise",
    solution10: "Check power grounding, replace audio cables, use audio isolator, check surrounding electromagnetic interference sources.",
    issue11: "Multi-microphone device conflicts",
    solution11: "Specify default microphone in system settings, disable unused audio devices, check application audio settings.",
    issue12: "Microphone not working in games",
    solution12: "Check game audio settings, confirm game has microphone permissions, disable exclusive mode, update game and audio drivers.",

    // Step-by-step Solutions
    step1: "Check microphone hardware connection",
    step2: "Check browser microphone permissions",
    step3: "Adjust system audio settings",
    step4: "Test recording quality in different environments",
    step5: "Try other recording software for problem verification",
    step6: "Check audio driver status",
    step7: "Test microphone working status in safe mode",
    step8: "Check if audio services are running normally",
    step9: "Try using different audio format settings",
    step10: "Consider resetting audio settings to default",

    // Cross-platform Operation Steps
    windowsSteps: [
      "Right-click volume icon, select Recording devices",
      "Ensure microphone is enabled and set as default device",
      "Check microphone properties for levels and enhancements",
      "Run Voice Recorder app to test microphone functionality",
      "Check Windows privacy settings for microphone permissions"
    ],
    macosSteps: [
      "Open System Preferences > Sound > Input",
      "Select correct microphone device",
      "Adjust input volume and test volume levels",
      "Check Security & Privacy for microphone permissions",
      "Use QuickTime Player to test recording functionality"
    ],
    linuxSteps: [
      "Use arecord -l command to list recording devices",
      "Check microphone volume settings in alsamixer",
      "Use pavucontrol to adjust PulseAudio settings",
      "Test arecord command recording functionality",
      "Check if user is in audio user group"
    ],

    // Preventive Maintenance
    maintenance: [
      "Regularly clean microphone windscreen and housing",
      "Avoid using microphone in humid environments",
      "Regularly check audio cable connection status",
      "Keep audio drivers updated",
      "Create audio settings configuration backup"
    ]
  },
  headphones: {
    // Basic Issues
    issue1: "No sound from headphones or low volume",
    solution1: "Check audio output device selection, adjust system and application volume, ensure audio drivers are working properly.",
    issue2: "Left/right channel imbalance or one-sided silence",
    solution2: "Check audio balance settings, test headphone connections, try different audio interface, could be hardware issue.",
    issue3: "Poor audio quality or noise",
    solution3: "Clean headphone interface, check audio file quality, adjust audio effect settings, avoid electromagnetic interference.",

    // Extended Issues
    issue4: "Bluetooth headphones connection unstable",
    solution4: "Check Bluetooth drivers, clear Bluetooth device cache, re-pair headphones, ensure device is within effective connection range.",
    issue5: "Severe headphone audio latency",
    solution5: "Use wired connection, check audio codec settings, disable audio enhancements, update Bluetooth drivers.",
    issue6: "Headphone microphone not working",
    solution6: "Check if headphones support microphone functionality, confirm audio device settings are correct, test microphone permission settings.",
    issue7: "No sound from headphones in specific applications",
    solution7: "Check application audio settings, confirm app has audio permissions, restart application, check audio exclusive mode.",
    issue8: "Headphone volume control ineffective",
    solution8: "Check headphone drivers, confirm volume control function support, reinstall audio drivers, check system volume mixer.",
    issue9: "Headphones producing electrical noise or background hum",
    solution9: "Check audio interface cleanliness, replace audio cables, adjust audio gain settings, check power interference.",
    issue10: "Multiple audio device conflicts",
    solution10: "Specify default audio device in system settings, disable unused audio devices, check application audio settings.",

    // Step-by-step Solutions
    step1: "Confirm headphone hardware connection is normal",
    step2: "Check system audio output settings",
    step3: "Test different audio files and volume levels",
    step4: "Check working status of other audio devices",
    step5: "Update audio drivers",
    step6: "Check audio format compatibility",
    step7: "Test headphones on other devices",
    step8: "Check audio service running status",
    step9: "Try resetting audio settings",
    step10: "Consider using professional audio software for testing",

    // Cross-platform Operation Steps
    windowsSteps: [
      "Right-click volume icon, select Playback devices",
      "Ensure headphones are set as default playback device",
      "Check headphone properties for audio format settings",
      "Test Windows system sounds",
      "Check if audio enhancements affect playback"
    ],
    macosSteps: [
      "Open System Preferences > Sound > Output",
      "Select correct headphone device",
      "Adjust output volume and balance settings",
      "Check Audio MIDI Setup for format configuration",
      "Use Music app to test audio playback"
    ],
    linuxSteps: [
      "Use aplay -l command to list playback devices",
      "Check volume settings in alsamixer",
      "Use pavucontrol to configure PulseAudio",
      "Test speaker-test command",
      "Check audio group permissions"
    ],

    // Preventive Maintenance
    maintenance: [
      "Regularly clean headphone interfaces and cables",
      "Avoid excessive pulling of headphone cables",
      "Keep audio drivers updated",
      "Regularly check headphone pads and headband wear",
      "Create personal audio settings configuration backup"
    ]
  },
  keyboard: {
    issue1: "Keys not responding or delayed response",
    solution1: "Check keyboard connection, reinstall drivers, clean keyboard internal components, check system resource usage.",
    issue2: "Key repeat input or sticking",
    solution2: "Adjust keyboard repeat rate settings, clean key switches, check keyboard aging, replacement may be needed.",
    issue3: "Special function keys not working",
    solution3: "Install keyboard-specific software, check shortcut settings, confirm function key drivers are normal, update firmware.",
    issue4: "Wireless keyboard connection unstable",
    solution4: "Replace batteries, check wireless receiver connection, re-pair keyboard, ensure wireless signal is interference-free.",
    issue5: "Keyboard backlight not working",
    solution5: "Check backlight shortcut key settings, confirm keyboard supports backlight functionality, update keyboard drivers and management software.",
    step1: "Check keyboard physical connection status",
    step2: "Check keyboard driver installation",
    step3: "Test basic key and combination key functions",
    step4: "Clean keyboard and check mechanical status",
    step5: "Test keyboard functionality on other devices",
    windowsSteps: [
      "Open Device Manager to check keyboard driver status",
      "Adjust keyboard repeat speed in Control Panel",
      "Check Windows keyboard language and layout settings",
      "Use Notepad to test all key functions",
      "Check keyboard manufacturer's management software"
    ],
    macosSteps: [
      "Open System Preferences > Keyboard",
      "Adjust key repeat and delay settings",
      "Check input sources and keyboard layout",
      "Use Keyboard Viewer to test keys",
      "Check keyboard settings in Accessibility"
    ],
    linuxSteps: [
      "Use lsusb command to check keyboard recognition status",
      "Check keyboard information in /proc/bus/input/devices",
      "Use xev command to test key events",
      "Configure xmodmap for key mapping",
      "Check keyboard layout configuration files"
    ],
    maintenance: [
      "Regularly clean keyboard surface and key gaps",
      "Avoid eating over keyboard to prevent contamination",
      "Keep keyboard drivers and firmware updated",
      "Regularly check wireless keyboard battery levels",
      "Create keyboard settings and macro configuration backup"
    ]
  },
  mouse: {
    issue1: "Mouse pointer movement inaccurate or jumping",
    solution1: "Clean mouse sensor, replace mouse pad, adjust DPI and acceleration settings, check surface compatibility.",
    issue2: "Mouse buttons not responding or double-click issues",
    solution2: "Adjust double-click speed, clean button switches, check microswitch lifespan, repair or replacement may be needed.",
    issue3: "Mouse wheel abnormal or not responding",
    solution3: "Clean mouse wheel mechanical structure, check encoder status, adjust scroll settings, avoid forceful use.",
    issue4: "Wireless mouse connection unstable",
    solution4: "Replace batteries, check wireless receiver position, re-pair mouse, avoid wireless signal interference.",
    issue5: "Mouse DPI switching function ineffective",
    solution5: "Check mouse drivers, confirm DPI button functionality, update mouse management software, reset DPI settings.",
    step1: "Check mouse hardware connection and power supply",
    step2: "Clean sensor and usage surface",
    step3: "Adjust mouse settings and DPI configuration",
    step4: "Test tracking performance on different surfaces",
    step5: "Check compatibility with other devices",
    windowsSteps: [
      "Open mouse settings to adjust pointer speed and precision",
      "Check mouse driver status in Device Manager",
      "Use mouse manufacturer's management software",
      "Test mouse functionality in different applications",
      "Check Windows pointer precision enhancement settings"
    ],
    macosSteps: [
      "Open System Preferences > Mouse",
      "Adjust tracking speed and scroll direction",
      "Check mouse settings in Accessibility",
      "Use Activity Monitor to check mouse-related processes",
      "Test mouse gestures and multi-touch functionality"
    ],
    linuxSteps: [
      "Use xinput command to list and configure mouse devices",
      "Check mouse information in /proc/bus/input/devices",
      "Use xev command to test mouse events",
      "Configure xorg.conf file to adjust mouse settings",
      "Check mouse performance in different desktop environments"
    ],
    maintenance: [
      "Regularly clean mouse sensor and housing",
      "Use appropriate mouse pad to protect sensor",
      "Avoid using mouse on reflective or transparent surfaces",
      "Keep mouse drivers and firmware updated",
      "Regularly check wireless mouse battery levels"
    ]
  },
  network: {
    issue1: "High network latency",
    solution1: "Use wired connection, close background downloads, ask network service provider for line quality check, optimize router settings.",
    issue2: "Slow download speeds",
    solution2: "Restart router, check other device usage, select appropriate test server, upgrade network package.",
    issue3: "Unstable network with frequent disconnections",
    solution3: "Check network cable connections, update network drivers, avoid peak hours, replace network equipment.",
    issue4: "WiFi signal weak or connection unstable",
    solution4: "Move closer to router, check WiFi band settings, update wireless network card drivers, consider using WiFi extender.",
    issue5: "DNS resolution failure or slow",
    solution5: "Change DNS servers (like *******), clear DNS cache, check router DNS settings, reset network configuration.",
    step1: "Check network hardware connection status",
    step2: "Restart network devices to clear cache",
    step3: "Test wired and wireless connection performance",
    step4: "Check network settings and DNS configuration",
    step5: "Contact ISP for line testing",
    windowsSteps: [
      "Run network troubleshooter",
      "Use Command Prompt to execute ipconfig /flushdns",
      "Check network adapter settings and drivers",
      "Use ping and tracert commands to test connection",
      "Check Windows Firewall and network configuration"
    ],
    macosSteps: [
      "Open Network preferences to check connection status",
      "Use Network Diagnostics tool to analyze problems",
      "Reset network settings (delete and re-add network)",
      "Use Terminal to execute sudo dscacheutil -flushcache",
      "Check system network proxy settings"
    ],
    linuxSteps: [
      "Use ifconfig or ip command to check network interfaces",
      "Check DNS settings in /etc/resolv.conf",
      "Use ping, traceroute, nslookup to test connection",
      "Check NetworkManager or systemd-networkd configuration",
      "View network-related errors in system logs"
    ],
    maintenance: [
      "Regularly restart router and modem",
      "Keep network device firmware updated",
      "Regularly clean network device heat vents",
      "Monitor network usage and performance metrics",
      "Create network configuration backup and recovery plan"
    ]
  },
  meeting: {
    issue1: "Poor video or audio quality",
    solution1: "Ensure stable network, close bandwidth-intensive applications, adjust video quality settings, use wired connection instead of WiFi.",
    issue2: "Participants can't hear or see you",
    solution2: "Check microphone and camera permission settings, confirm devices are not muted or disabled, try rejoining meeting.",
    issue3: "Unstable connection or disconnections during meeting",
    solution3: "Use wired network connection, close unnecessary applications, reduce video quality, check if network bandwidth meets requirements.",
    step1: "Test all device functionality before meeting",
    step2: "Ensure network connection is stable and reliable",
    step3: "Adjust camera position and lighting environment",
    step4: "Check audio settings and microphone volume",
    step5: "Prepare backup devices for emergency situations"
  },
  gaming: {
    issue1: "Lag or stuttering in games",
    solution1: "Use wired network connection, close background programs, update graphics drivers, optimize game graphics settings, check system temperature.",
    issue2: "Peripherals not responding or inaccurate",
    solution2: "Update device drivers, adjust DPI and polling rate settings, clean sensors, check USB ports are working properly.",
    issue3: "Inaccurate audio positioning in games",
    solution3: "Use headphones with spatial audio support, update audio drivers, adjust in-game audio settings, disable unnecessary audio effects.",
    step1: "Ensure all gaming peripherals are working properly",
    step2: "Optimize network connection to reduce latency",
    step3: "Adjust peripheral settings for game requirements",
    step4: "Update all device drivers and firmware",
    step5: "Monitor system performance to avoid overheating issues"
  },
  tools: {
    issue1: "Testing tool won't load or start",
    solution1: "Refresh the page, clear browser cache, ensure JavaScript is enabled, try a different browser.",
    issue2: "Device not detected by testing tool",
    solution2: "Check device connections, grant necessary permissions, ensure device is not being used by other applications.",
    issue3: "Test results seem inaccurate",
    solution3: "Ensure proper testing environment, check device calibration, verify no interference from other programs.",
    step1: "Verify browser compatibility and update if necessary",
    step2: "Grant all required device permissions",
    step3: "Close other applications that might interfere",
    step4: "Ensure stable internet connection for real-time testing",
    step5: "Follow testing instructions carefully for accurate results"
  }
};
